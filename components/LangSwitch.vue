<template>
  <view class="lang-switch" @click="toggleLang">
    {{ lang === 'zh' ? 'EN' : '中' }}
  </view>
</template>

<script>
export default {
  data() {
    return {
      lang: uni.getStorageSync('lang') || 'zh'
    }
  },
  methods: {
    toggleLang() {
      this.lang = this.lang === 'zh' ? 'en' : 'zh'
      uni.setStorageSync('lang', this.lang)
      // 使用 uni.$emit 发送全局事件，通知所有页面和组件
      uni.$emit('lang-change', this.lang)
    }
  }
}
</script>

<style>
.lang-switch {
  position: fixed;
  right: 32rpx;
  bottom:200rpx;
  z-index: 9999;
  width: 80rpx;
  height: 80rpx;
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}
</style>