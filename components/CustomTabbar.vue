<template>
  <view class="custom-tabbar">
    <u-tabbar
    :value="current"
    :fixed="true"
    :placeholder="true"
    :safe-area-inset-bottom="true"
    @change="switchTab"
  >
    <u-tabbar-item
      v-for="(item, idx) in tabList"
      :key="item.page"
      :icon="item.icon"
      :text="lang === 'zh' ? item.text_zh : item.text_en"
    />
  </u-tabbar>
  </view>
</template>

<script>
export default {
  props: {
    current: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      lang: uni.getStorageSync('lang') || 'zh',
      tabList: [
        { page: '/pages/index/index', icon: 'home', text_zh: '首页', text_en: 'Home' },
        { page: '/pages/events/index', icon: 'calendar', text_zh: '活动', text_en: 'Events' },
        { page: '/pages/my-join/index', icon: 'star', text_zh: '我参与的', text_en: 'Joined' },
        { page: '/pages/profile/index', icon: 'account', text_zh: '我的', text_en: 'Profile' }
      ]
    }
  },
  mounted() {
    // 监听全局语言切换事件
    uni.$on('lang-change', this.onLangChange)
  },
  beforeDestroy() {
    uni.$off('lang-change', this.onLangChange)
  },
  methods: {
    switchTab(idx) {
      if (idx === this.current) return
      uni.switchTab({ url: this.tabList[idx].page })
    },
    onLangChange(newLang) {
      this.lang = newLang
    }
  }
}
</script>

<style>
.custom-tabbar {
  
}

</style>