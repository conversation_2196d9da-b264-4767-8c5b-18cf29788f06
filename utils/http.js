const BASE_URL = 'https://xialingying.guaguakj.com';

export function http({ url, method = 'POST', data = {}, header = {} }) {
  return new Promise((resolve, reject) => {
    const token = uni.getStorageSync('token') || '';
    const lang = uni.getStorageSync('lang') || 'zh';
    uni.request({
      url: BASE_URL + url,
      method,
      data,
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token,
        'lang':lang,
        ...header
      },
      success: (res) => {
        // 检查登录超时
        if (res.data && res.data.code === 2 && res.data.msg && res.data.msg.indexOf('登录超时') !== -1) {
          uni.removeStorageSync('token');
        }
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(res);
        }
      },
      fail: reject
    });
  });
} 
  // 上传图片
  export function File(data, success, fail) {
    uni.uploadFile({
      url: BASE_URL + '/Uploads/UploadFile',
      filePath: data.temfile,
      name: 'file',
      header: {
        'content-type': 'multipart/form-data'
      },
      formData: data, //请求额外的form data
      success(res) {
        let result = null;
        try {
          result = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
        } catch (e) {
          fail(e);
        }
        if (result.code == 0) {
          success(result);
        } else {
          fail(result);
        }
      },
      fail
    })
  }