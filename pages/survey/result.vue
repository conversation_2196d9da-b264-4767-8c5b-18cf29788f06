<template>
  <view class="result-page">
    <!-- 结果卡片 -->
    <view class="result-card">
      <view class="card-header">
        <u-icon name="checkmark-circle" color="#19be6b" size="60"></u-icon>
        <view class="header-text">{{ lang === 'zh' ? '问卷已完成' : 'Survey Completed' }}</view>
      </view>
      
      <view class="divider"></view>
      
      <!-- 认知水平评估结果 -->
      <view class="result-section">
        <view class="section-title">{{ lang === 'zh' ? '认知水平评估' : 'Cognitive Level Assessment' }}</view>
        
        <view class="level-chart">
          <view class="chart-item">
            <view class="chart-label">{{ lang === 'zh' ? '语言能力' : 'Language' }}</view>
            <view class="chart-bar">
              <view class="chart-fill" :style="{width: result.language + '%'}"></view>
            </view>
            <view class="chart-value">{{ result.language }}%</view>
          </view>
          
          <view class="chart-item">
            <view class="chart-label">{{ lang === 'zh' ? '逻辑思维' : 'Logic' }}</view>
            <view class="chart-bar">
              <view class="chart-fill" :style="{width: result.logic + '%'}"></view>
            </view>
            <view class="chart-value">{{ result.logic }}%</view>
          </view>
          
          <view class="chart-item">
            <view class="chart-label">{{ lang === 'zh' ? '创造力' : 'Creativity' }}</view>
            <view class="chart-bar">
              <view class="chart-fill" :style="{width: result.creativity + '%'}"></view>
            </view>
            <view class="chart-value">{{ result.creativity }}%</view>
          </view>
          
          <view class="chart-item">
            <view class="chart-label">{{ lang === 'zh' ? '社交能力' : 'Social' }}</view>
            <view class="chart-bar">
              <view class="chart-fill" :style="{width: result.social + '%'}"></view>
            </view>
            <view class="chart-value">{{ result.social }}%</view>
          </view>
          
          <view class="chart-item">
            <view class="chart-label">{{ lang === 'zh' ? '运动能力' : 'Physical' }}</view>
            <view class="chart-bar">
              <view class="chart-fill" :style="{width: result.physical + '%'}"></view>
            </view>
            <view class="chart-value">{{ result.physical }}%</view>
          </view>
        </view>
      </view>
      
      <!-- 课程推荐 -->
      <!-- <view class="divider"></view>
      <view class="result-section">
        <view class="section-title">{{ lang === 'zh' ? '课程推荐' : 'Course Recommendations' }}</view>
        
        <view class="course-list">
          <view class="course-item" v-for="(course, index) in recommendedCourses" :key="index">
            <view class="course-icon">
              <u-icon :name="course.icon" size="40" color="#409eff"></u-icon>
            </view>
            <view class="course-info">
              <view class="course-name">{{ lang === 'zh' ? course.name : course.name_en }}</view>
              <view class="course-desc">{{ lang === 'zh' ? course.desc : course.desc_en }}</view>
            </view>
          </view>
        </view>
      </view> -->
      
      <view class="divider"></view>
      
      <!-- 个性化建议 -->
      <view class="result-section">
        <view class="section-title">{{ lang === 'zh' ? '个性化建议' : 'Personalized Suggestions' }}</view>
        
        <view class="suggestion-content">
          {{ lang === 'zh' ? suggestions.zh : suggestions.en }}
        </view>
      </view>
    </view>
    
    <!-- 按钮区域 -->
    <view class="button-group safe-area-inset-bottom">
      <u-button type="primary" @click="viewActivityDetail">{{ lang === 'zh' ? '查看活动详情' : 'View Activity Details' }}</u-button>
      <!-- <u-button type="info" @click="downloadReport">{{ lang === 'zh' ? '下载完整报告' : 'Download Full Report' }}</u-button> -->
    </view>
    
    <!-- 语言切换按钮 -->
    <LangSwitch />
  </view>
</template>

<script>
import LangSwitch from '@/components/LangSwitch.vue';

export default {
  components: {
    LangSwitch
  },
  data() {
    return {
      lang: uni.getStorageSync('lang') || 'zh',
      activityId: null,
      result: {
        language: 85,
        logic: 70,
        creativity: 90,
        social: 65,
        physical: 75
      },
      recommendedCourses: [
        {
          icon: 'edit-pen',
          name: '创意绘画课程',
          name_en: 'Creative Drawing Course',
          desc: '根据孩子的创造力水平，推荐参加创意绘画课程，培养艺术感知能力。',
          desc_en: 'Based on your child\'s creativity level, we recommend the Creative Drawing Course to develop artistic perception.'
        },
        {
          icon: 'chat',
          name: '英语口语强化班',
          name_en: 'English Speaking Intensive Class',
          desc: '提高孩子的语言表达能力，增强沟通自信。',
          desc_en: 'Improve your child\'s language expression skills and enhance communication confidence.'
        },
        {
          icon: 'grid',
          name: '团队协作训练营',
          name_en: 'Team Collaboration Camp',
          desc: '通过团队活动提升孩子的社交能力和协作精神。',
          desc_en: 'Enhance your child\'s social skills and collaborative spirit through team activities.'
        }
      ],
      suggestions: {
        zh: '根据问卷结果，您的孩子在创造力方面表现出色，建议多参加艺术类和创新思维类的活动。语言能力也较强，可以通过阅读和口语练习进一步提升。社交能力相对较弱，建议参加更多团队活动，培养合作精神和人际交往能力。',
        en: 'Based on the survey results, your child shows excellent performance in creativity. We recommend more participation in artistic and innovative thinking activities. Language ability is also strong and can be further improved through reading and speaking practice. Social skills are relatively weak, so we suggest participating in more team activities to develop cooperation and interpersonal skills.'
      }
    };
  },
  onLoad(options) {
    // 获取活动ID
    if (options.id) {
      this.activityId = options.id;
    }
    
    // 从本地存储获取语言设置
    this.lang = uni.getStorageSync('lang') || 'zh';
    
    // 从全局获取语言设置
    if (getApp().globalData && getApp().globalData.lang) {
      this.lang = getApp().globalData.lang;
    }
    
    // 监听语言变化
    uni.$on('lang-change', this.onLangChange);
    
    // 设置导航栏标题
    this.setNavigationBarTitle();
  },
  onUnload() {
    // 取消监听语言变化
    uni.$off('lang-change', this.onLangChange);
  },
  methods: {
    viewActivityDetail() {
      uni.navigateTo({
        url: `/pages/events/detail?id=${this.activityId}`
      });
    },
    downloadReport() {
      uni.showLoading({
        title: this.lang === 'zh' ? '准备下载...' : 'Preparing download...'
      });
      
      // 模拟下载
      setTimeout(() => {
        uni.hideLoading();
        
        uni.showToast({
          title: this.lang === 'zh' ? '报告已保存到手机' : 'Report saved to your device',
          icon: 'success'
        });
      }, 2000);
    },
    goBack() {
      uni.navigateBack();
    },
    onLangChange(newLang) {
      this.lang = newLang;
      // 更新导航栏标题
      this.setNavigationBarTitle();
    },
    setNavigationBarTitle() {
      uni.setNavigationBarTitle({
        title: this.lang === 'zh' ? '调查结果反馈' : 'Survey Result'
      });
    }
  }
};
</script>

<style>
.result-page {
  min-height: 100vh;
  padding-bottom: 40rpx;
  box-sizing: border-box;
  background-color: #f5f5f5;
  padding-top: 20rpx;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.navbar-left, .navbar-right {
  padding: 10rpx;
  width: 80rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #303133;
  flex-grow: 1;
  text-align: center;
}

.result-card {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.card-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 30rpx;
}

.header-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #303133;
  margin-top: 20rpx;
}

.divider {
  height: 1rpx;
  background-color: #ebeef5;
  margin: 0 30rpx;
}

.result-section {
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 24rpx;
}

.level-chart {
}

.chart-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.chart-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #606266;
}

.chart-bar {
  flex: 1;
  height: 30rpx;
  background-color: #ebeef5;
  border-radius: 15rpx;
  overflow: hidden;
  margin: 0 20rpx;
}

.chart-fill {
  height: 100%;
  background-color: #409eff;
  border-radius: 15rpx;
}

.chart-value {
  width: 80rpx;
  font-size: 24rpx;
  color: #909399;
  text-align: right;
}

.course-list {
  margin-top: 20rpx;
}

.course-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #ebeef5;
}

.course-item:last-child {
  border-bottom: none;
}

.course-icon {
  margin-right: 20rpx;
  display: flex;
  align-items: center;
}

.course-info {
  flex: 1;
}

.course-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8rpx;
}

.course-desc {
  font-size: 26rpx;
  color: #606266;
  line-height: 1.5;
}

.suggestion-content {
  font-size: 28rpx;
  color: #606266;
  line-height: 1.6;
}

.button-group {
  margin: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.safe-area-inset-bottom {
  padding-bottom: calc(40rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
}
</style> 