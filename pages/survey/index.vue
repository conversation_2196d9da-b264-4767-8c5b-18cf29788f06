<template>
  <view class="survey-page">
    <!-- 问卷说明 -->
    <view class="survey-intro">
      <view class="intro-title">{{ lang === 'zh' ? '亲爱的家长' : 'Dear Parent' }}</view>
      <view class="intro-text">
        {{ lang === 'zh' ? 
          '为了更好地了解您孩子的认知水平，请您花几分钟时间填写以下问卷。这将帮助我们为您的孩子提供更加个性化的培训课程。' : 
          'To better understand your child\'s cognitive level, please take a few minutes to complete the following questionnaire. This will help us provide more personalized training courses for your child.' 
        }}
      </view>
    </view>
    
    <!-- 问卷内容 -->
    <view class="survey-content">
      <view class="question-item" v-for="(question, index) in questions" :key="index">
        <view class="question-title">{{ index + 1 }}. {{ lang === 'zh' ? question.title : question.title_en }}</view>
        
        <!-- 单选题 -->
        <view class="options-list" v-if="question.type === 'radio'">
          <u-radio-group v-model="answers[index]" placement="column">
            <u-radio
              v-for="(option, optIndex) in lang === 'zh' ? question.options : question.options_en" 
              :key="optIndex"
              :label="option"
              :name="optIndex"
              :customStyle="{marginBottom: '20rpx'}"
            >
            </u-radio>
          </u-radio-group>
        </view>
        
        <!-- 多选题 -->
        <view class="options-list" v-if="question.type === 'checkbox'">
          <u-checkbox-group v-model="answers[index]" placement="column">
            <u-checkbox
              v-for="(option, optIndex) in lang === 'zh' ? question.options : question.options_en" 
              :key="optIndex"
              :label="option"
              :name="optIndex"
              :customStyle="{marginBottom: '20rpx'}"
            >
            </u-checkbox>
          </u-checkbox-group>
        </view>
        
        <!-- 文本输入 -->
        <view class="text-input" v-if="question.type === 'text'">
          <u-input 
            v-model="answers[index]" 
            type="text" 
            border="bottom"
            :placeholder="lang === 'zh' ? '请输入您的回答' : 'Please enter your answer'"
          />
        </view>
      </view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="submit-section safe-area-inset-bottom">
      <u-button type="primary" @click="submitSurvey">{{ lang === 'zh' ? '提交问卷' : 'Submit Survey' }}</u-button>
    </view>
    
    <!-- 语言切换按钮 -->
    <LangSwitch />
  </view>
</template>

<script>
import LangSwitch from '@/components/LangSwitch.vue';

export default {
  components: {
    LangSwitch
  },
  data() {
    return {
      lang: uni.getStorageSync('lang') || 'zh',
      activityId: null,
      answers: [],
      questions: [
        {
          type: 'radio',
          title: '您的孩子年龄是？',
          title_en: 'What is your child\'s age?',
          options: ['3-5岁', '6-8岁', '9-12岁', '13-15岁', '16岁以上'],
          options_en: ['3-5 years', '6-8 years', '9-12 years', '13-15 years', '16+ years']
        },
        {
          type: 'radio',
          title: '您的孩子是否有参加过类似的培训课程？',
          title_en: 'Has your child participated in similar training courses before?',
          options: ['是，多次参加', '是，参加过1-2次', '没有参加过'],
          options_en: ['Yes, multiple times', 'Yes, 1-2 times', 'No, never']
        },
        {
          type: 'checkbox',
          title: '您的孩子对以下哪些领域表现出兴趣？（可多选）',
          title_en: 'In which of the following areas does your child show interest? (Multiple choices)',
          options: ['艺术与创意', '体育运动', '科学探索', '语言学习', '数学逻辑', '音乐舞蹈', '社交活动'],
          options_en: ['Arts & Creativity', 'Sports', 'Science Exploration', 'Language Learning', 'Mathematical Logic', 'Music & Dance', 'Social Activities']
        },
        {
          type: 'radio',
          title: '您的孩子在学习新知识时的接受能力如何？',
          title_en: 'How would you rate your child\'s ability to learn new knowledge?',
          options: ['非常快', '较快', '一般', '需要反复学习', '学习困难'],
          options_en: ['Very fast', 'Fast', 'Average', 'Needs repetition', 'Difficult']
        },
        {
          type: 'radio',
          title: '您的孩子在团队活动中通常扮演什么角色？',
          title_en: 'What role does your child usually play in team activities?',
          options: ['领导者', '积极参与者', '跟随者', '观察者', '不喜欢团队活动'],
          options_en: ['Leader', 'Active participant', 'Follower', 'Observer', 'Dislikes team activities']
        },
        {
          type: 'text',
          title: '您对孩子参加本次培训有什么特别的期望或需求？',
          title_en: 'Do you have any specific expectations or requirements for your child\'s participation in this training?'
        }
      ]
    };
  },
  onLoad(options) {
    // 获取活动ID
    if (options.id) {
      this.activityId = options.id;
    }
    
    // 初始化答案数组
    this.initAnswers();
    
    // 从本地存储获取语言设置
    this.lang = uni.getStorageSync('lang') || 'zh';
    
    // 从全局获取语言设置
    if (getApp().globalData && getApp().globalData.lang) {
      this.lang = getApp().globalData.lang;
    }
    
    // 监听语言变化
    uni.$on('lang-change', this.onLangChange);
    
    // 设置导航栏标题
    this.setNavigationBarTitle();
  },
  onUnload() {
    // 取消监听语言变化
    uni.$off('lang-change', this.onLangChange);
  },
  methods: {
    initAnswers() {
      // 根据问题数量初始化答案数组
      this.answers = this.questions.map(q => {
        if (q.type === 'checkbox') {
          return [];
        }
        return '';
      });
    },
    onLangChange(newLang) {
      this.lang = newLang;
      // 更新导航栏标题
      this.setNavigationBarTitle();
    },
    setNavigationBarTitle() {
      uni.setNavigationBarTitle({
        title: this.lang === 'zh' ? '认知水平调查问卷' : 'Cognitive Level Survey'
      });
    },
    submitSurvey() {
      // 验证是否所有问题都已回答
      const unansweredIndex = this.answers.findIndex((answer, index) => {
        if (Array.isArray(answer)) {
          return answer.length === 0;
        }
        return answer === '';
      });
      
      if (unansweredIndex !== -1) {
        uni.showToast({
          title: this.lang === 'zh' ? 
            `请回答第${unansweredIndex + 1}个问题` : 
            `Please answer question ${unansweredIndex + 1}`,
          icon: 'none'
        });
        return;
      }
      
      // 提交问卷
      uni.showLoading({
        title: this.lang === 'zh' ? '提交中...' : 'Submitting...'
      });
      
      // 模拟提交
      setTimeout(() => {
        uni.hideLoading();
        
        // 显示提交成功
        uni.showToast({
          title: this.lang === 'zh' ? '提交成功' : 'Submitted successfully',
          icon: 'success'
        });
        
        // 跳转到结果页
        setTimeout(() => {
          uni.navigateTo({
            url: `/pages/survey/result?id=${this.activityId}`
          });
        }, 1500);
      }, 2000);
    }
  }
};
</script>

<style>
.survey-page {
  min-height: 100vh;
  padding-bottom: 180rpx;
  box-sizing: border-box;
}

.survey-intro {
  background-color: #f8f8f8;
  padding: 30rpx;
  margin: 30rpx;
  border-radius: 16rpx;
}

.intro-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  color: #303133;
}

.intro-text {
  font-size: 28rpx;
  color: #606266;
  line-height: 1.6;
}

.survey-content {
  padding: 0 30rpx;
  margin-bottom: 30rpx;
}

.question-item {
  margin-bottom: 40rpx;
  background-color: #ffffff;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.question-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 24rpx;
}

.options-list {
  padding-left: 20rpx;
}

.text-input {
  margin-top: 20rpx;
}

.submit-section {
  padding: 40rpx 30rpx;
  position: fixed;
  bottom: 0;
  box-sizing: border-box;
  left: 0;
  z-index: 9999999;
  width: 100%;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.safe-area-inset-bottom {
  padding-bottom: calc(10rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(10rpx + env(safe-area-inset-bottom));
}
</style> 