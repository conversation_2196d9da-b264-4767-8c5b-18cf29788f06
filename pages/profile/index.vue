<template>
  <view class="profile-page">
    <!-- 个人信息 -->
    <view class="profile-header">
      <view class="profile-header-inner">
        <u-avatar :src="user.avatar" size="80" />
        <view class="profile-info">
          <view class="profile-nick">
            {{ user.nickname }}
            <text v-if="user.level > 0" class="level-badge">LV{{ user.level }}</text>
          </view>
          <view class="profile-sign">{{ user.signature }}</view>
          <view class="profile-desc">{{ user.desc }}</view>
          <view v-if="user.money > 0" class="profile-money">{{ lang === 'zh' ? '余额：' : 'Balance: ' }}¥{{ user.money }}</view>
        </view>
        <u-icon name="arrow-right" size="24" color="#c0c4cc" class="profile-edit" @click="onEditProfile" />
      </view>
    </view>

    <!-- 我的积分 -->
    <view class="section-card">
      <view class="profile-points-bar">
        <view class="points-left">
          <text class="points-num">{{ points }}</text>
          <text class="points-unit">{{ lang === 'zh' ? '积分' : 'Points' }}</text>
        </view>
        <view class="points-right">
          <u-button type="primary" class="exchange-btn" @click="onExchange">{{ lang === 'zh' ? '积分兑换' : 'Exchange' }}</u-button>
        </view>
      </view>
      
      <view class="divider"></view>
      
      <view class="points-record" @click="onPointsRecord">
        <text class="record-text">{{ lang === 'zh' ? '积分记录' : 'Points Record' }}</text>
        <u-icon name="arrow-right" size="20" color="#c0c4cc"></u-icon>
      </view>
    </view>

    <!-- 报名资料管理入口 -->
    <view class="section-card signup-card" @click="onSignupManage">
      <view class="signup-header">
        <view class="signup-title">
          <u-icon name="file-text" size="32" color="#2979ff" class="signup-icon"></u-icon>
          <text>{{ lang === 'zh' ? '报名资料管理' : 'Sign-up Info' }}</text>
        </view>
        <u-icon name="arrow-right" size="20" color="#c0c4cc"></u-icon>
      </view>
      <view class="signup-content">
        <view class="signup-desc">{{ lang === 'zh' ? '管理您的个人报名信息，确保参与活动时资料完整' : 'Manage your personal registration information' }}</view>
        <u-tag text="重要" type="warning" size="mini" v-if="lang === 'zh'" class="signup-tag"></u-tag>
        <u-tag text="Important" type="warning" size="mini" v-else class="signup-tag"></u-tag>
      </view>
    </view>

    <!-- 设置区 -->
    <view class="section-card settings-card">
      <view class="settings-item" @click="onPrivacy">
        <view class="settings-left">
          <view class="custom-icon privacy-icon">
            <u-icon name="info-circle" size="28" color="#2979ff"></u-icon>
          </view>
          <text class="settings-title">{{ lang === 'zh' ? '隐私协议' : 'Privacy Policy' }}</text>
        </view>
        <u-icon name="arrow-right" size="20" color="#c0c4cc"></u-icon>
      </view>
      
      <view class="settings-divider"></view>
      
      <view class="settings-item" @click="onAccount">
        <view class="settings-left">
          <view class="custom-icon security-icon">
            <u-icon name="lock" size="28" color="#ff9900"></u-icon>
          </view>
          <text class="settings-title">{{ lang === 'zh' ? '账户安全' : 'Account Security' }}</text>
        </view>
        <u-icon name="arrow-right" size="20" color="#c0c4cc"></u-icon>
      </view>
      
      <view class="settings-divider"></view>
      
      <!-- 在线客服 - 整个区域都是按钮 -->
      <button open-type="contact" class="contact-btn-full" @contact="onContactSuccess" @error="onContactFail">
        <view class="settings-item service-item">
          <view class="settings-left">
            <view class="custom-icon service-icon">
              <u-icon name="kefu-ermai" size="28" color="#19be6b"></u-icon>
            </view>
            <text class="settings-title">{{ lang === 'zh' ? '在线客服' : 'Customer Service' }}</text>
          </view>
          <view class="arrow-right">
            <u-icon name="arrow-right" size="20" color="#c0c4cc"></u-icon>
          </view>
        </view>
      </button>
    </view>

    <CustomTabbar :current="3" />
    <LangSwitch />
  </view>
</template>

<script>
import CustomTabbar from '@/components/CustomTabbar.vue';
import LangSwitch from '@/components/LangSwitch.vue';
import { postUserInfo } from '@/api/user.js';

export default {
  components: {
    CustomTabbar,
    LangSwitch
  },
  data() {
    return {
      lang: 'zh',
      user: {
        avatar: 'https://picsum.photos/200',
        nickname: 'User123',
        signature: '这是个性签名',
        desc: '这里是用户描述信息'
      },
      points: 1280
    };
  },
  onShow() {
    this.loadLocalUserInfo();
  },
  mounted() {
    // 获取语言设置
    const lang = uni.getStorageSync('lang') || 'zh';
    this.lang = lang;
    
    // 监听语言变化
    uni.$on('lang-change', this.onLangChange);
    
    // 设置导航栏标题
    this.setNavTitle();
    
    // 获取用户信息
    this.getUserInfo();
  },
  beforeDestroy() {
    uni.$off('lang-change', this.onLangChange);
  },
  methods: {
        async getUserInfo() {
      try {
        // 调用接口获取用户信息
        const response = await postUserInfo();
        
        if (response && response.code === 0 && response.data) {
          // 处理头像URL - 如果是相对路径，需要拼接完整URL
          let avatarUrl = response.data.head || 'https://picsum.photos/200';
          if (avatarUrl.startsWith('/')) {
            avatarUrl = 'https://xialingying.guaguakj.com' + avatarUrl;
          }
          
          // 更新用户信息 - 根据实际返回的字段映射
          this.user = {
            id: response.data.userID,
            avatar: avatarUrl,
            nickname: response.data.name || 'User123',
            signature: '这是个性签名', // 接口暂无此字段，使用默认值
            desc: '这里是用户描述信息', // 接口暂无此字段，使用默认值
            level: response.data.level || 0,
            money: response.data.money || 0,
            cardNums: response.data.cardNums || 0,
            isHaveEWM: response.data.isHaveEWM || 0
          };
          
          // 更新积分 - 使用 integral 字段
          this.points = response.data.integral || 0;
          
          // 保存到本地存储
          uni.setStorageSync('userInfo', this.user);
          uni.setStorageSync('userPoints', this.points);
          
          console.log('用户信息获取成功:', response.data);
        } else {
          console.error('获取用户信息失败:', response);
          // 失败时从本地存储获取
          this.loadLocalUserInfo();
        }
      } catch (error) {
        console.error('获取用户信息异常:', error);
        // 异常时从本地存储获取
        this.loadLocalUserInfo();
        uni.showToast({
          title: this.lang === 'zh' ? '获取用户信息失败' : 'Failed to get user info',
          icon: 'none'
        });
      }
    },
    
    loadLocalUserInfo() {
      // 从本地存储获取用户信息
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo) {
        this.user = userInfo;
      }
    },
    onLangChange(newLang) {
      this.lang = newLang;
      this.setNavTitle();
    },
    setNavTitle() {
      const title = this.lang === 'zh' ? '我的' : 'Profile';
      uni.setNavigationBarTitle({ title });
    },
    onEditProfile() {
      uni.navigateTo({
        url: '/pages/profile/edit'
      });
    },
    onExchange() {
      uni.navigateTo({
        url: '/pages/points/exchange'
      });
    },
    onPointsRecord() {
      uni.navigateTo({
        url: '/pages/points/record'
      });
    },
    onSignupManage() {
      uni.navigateTo({
        url: '/pages/signup/manage'
      });
    },
    onPrivacy() {
      uni.navigateTo({
        url: '/pages/profile/privacy'
      });
    },
    onAccount() {
      uni.navigateTo({
        url: '/pages/profile/account'
      });
    },
    onService() {
      // 不执行任何操作，让button的open-type处理
    },
    onContactSuccess(e) {
      console.log('客服联系成功', e);
    },
    onContactFail(e) {
      console.log('客服联系失败', e);
      uni.showToast({
        title: this.lang === 'zh' ? '客服联系失败' : 'Contact failed',
        icon: 'none'
      });
    }
  }
}
</script>

<style scoped>
.profile-page {
  min-height: 100vh;
  box-sizing: border-box;
  padding-bottom: 100rpx;
  padding-top: 20rpx;
}
.profile-header {
  background: #f5f7fa;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
  padding: 0 24rpx;
  margin: 0 24rpx;
  margin-bottom: 24rpx;
  box-sizing: border-box;
}
.profile-header-inner {
  display: flex;
  align-items: center;
  padding: 32rpx 0 24rpx 0;
  position: relative;
}
.profile-info {
  flex: 1;
  margin-left: 24rpx;
}
.profile-nick {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  display: flex;
  align-items: center;
}
.level-badge {
  background: linear-gradient(45deg, #ff9900, #ffcc00);
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  margin-left: 12rpx;
  font-weight: normal;
}
.profile-sign {
  font-size: 26rpx;
  color: #888;
  margin-top: 8rpx;
}
.profile-desc {
  font-size: 24rpx;
  color: #aaa;
  margin-top: 8rpx;
}
.profile-money {
  font-size: 24rpx;
  color: #19be6b;
  margin-top: 8rpx;
  font-weight: 500;
}
.loading-text {
  font-size: 22rpx;
  color: #2979ff;
  margin-top: 8rpx;
}
.profile-edit {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  color: #c0c4cc;
}
.section-card {
  background: #f5f7fa;
  border-radius: 12rpx;
  margin: 0 24rpx 24rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
  border: 1rpx solid #f0f0f0;
  padding-bottom: 8rpx;
}
.profile-points-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
}
.points-left {
  display: flex;
  align-items: baseline;
}
.points-right {
  flex: 1;
  margin-left: 24rpx;
  max-width: 60%;
}
.points-num {
  color: #ff9900;
  font-weight: bold;
  font-size: 40rpx;
}
.points-unit {
  font-size: 26rpx;
  color: #888;
  margin-left: 8rpx;
}
.exchange-btn {
  width: 100%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.divider {
  height: 1px;
  background-color: #eee;
  margin: 0;
}
.points-record {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
}
.record-text {
  font-size: 28rpx;
  color: #333;
}
.section-title {
  font-size: 28rpx;
  font-weight: bold;
  padding: 24rpx 0 8rpx 24rpx;
  color: #2979ff;
}
.signup-card {
  margin-top: 0;
  padding: 24rpx;
  background: #f5f7fa;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
  border: 1rpx solid #f0f0f0;
}
.signup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.signup-title {
  display: flex;
  align-items: center;
}
.signup-title text {
  font-size: 32rpx;
  font-weight: bold;
  color: #2979ff;
}
.signup-icon {
  margin-right: 12rpx;
}
.signup-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 12rpx;
}
.signup-desc {
  font-size: 26rpx;
  color: #666;
  flex: 1;
  margin-right: 16rpx;
}
.signup-tag {
  margin-left: 16rpx;
}
.settings-card {
  margin-top: 0;
  padding: 24rpx;
  background: #f5f7fa;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
  border: 1rpx solid #f0f0f0;
}
.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}
.settings-left {
  display: flex;
  align-items: center;
}
.settings-title {
  font-size: 28rpx;
  color: #333;
  margin-left: 16rpx;
}
.settings-divider {
  height: 1px;
  background-color: #eee;
  margin: 8rpx 0;
}
.custom-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 12rpx;
}
.privacy-icon {
  background-color: #e6f2ff; /* 浅蓝色背景 */
}
.security-icon {
  background-color: #fff5e6; /* 浅橙色背景 */
}
.service-icon {
  background-color: #e6f9f0; /* 浅绿色背景 */
}
.contact-btn {
  padding: 0;
  margin: 0;
  line-height: 1;
  height: auto;
  background-color: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 0;
  width: auto;
  overflow: visible;
}
.contact-btn::after {
  border: none;
}
.contact-btn-full {
  width: 100%;
  display: block;
  padding: 0;
  margin: 0;
  background-color: transparent;
  border: none;
  line-height: normal;
  text-align: left;
}
.contact-btn-full::after {
  border: none;
}
.service-item {
  width: 100%;
  box-sizing: border-box;
}
.arrow-right {
  display: flex;
  align-items: center;
}
</style> 