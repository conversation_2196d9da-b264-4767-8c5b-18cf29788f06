<template>
  <view class="privacy-page">
    <view class="privacy-container">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <text class="loading-text">{{ lang === 'zh' ? '加载中...' : 'Loading...' }}</text>
      </view>
      
      <!-- 内容显示 -->
      <view v-else class="content-container">
        <!-- 内容 -->
        <view class="content-body">
          <rich-text :nodes="privacyContent.content || '暂无内容'"></rich-text>
        </view>
      </view>
    </view>
    
    <!-- 添加语言切换组件 -->
    <LangSwitch />
  </view>
</template>

<script>
import LangSwitch from '@/components/LangSwitch.vue';
import { postSingleInfo } from '@/api/apiApplet.js';

export default {
  components: {
    LangSwitch
  },
  data() {
    return {
      lang: 'zh',
      loading: true,
      privacyContent: {
        title: '',
        content: ''
      }
    };
  },
  mounted() {
    // 获取语言设置
    const lang = uni.getStorageSync('lang') || 'zh';
    this.lang = lang;
    
    // 监听语言变化
    uni.$on('lang-change', this.onLangChange);
    
    // 设置导航栏标题
    this.setNavTitle();
    
    // 获取隐私政策内容
    this.getPrivacyContent();
  },
  beforeDestroy() {
    uni.$off('lang-change', this.onLangChange);
  },
  methods: {
    // 获取隐私政策内容
    async getPrivacyContent() {
      try {
        this.loading = true;
        
        // 调用接口，传递id为2
        const response = await postSingleInfo({ id: 2 });
        
        if (response && response.code === 0 && response.data) {
          this.privacyContent = {
            title: '',
            content: response.data
          };
        } else {
          console.error('获取隐私政策失败:', response);
          // 失败时使用默认内容
          this.setDefaultContent();
        }
      } catch (error) {
        console.error('获取隐私政策异常:', error);
        // 异常时使用默认内容
        this.setDefaultContent();
      } finally {
        this.loading = false;
      }
    },
    
    // 设置默认内容（作为备用方案）
    setDefaultContent() {
      this.privacyContent = {
        title: '',
        content: this.lang === 'zh' 
          ? '我们致力于保护您的隐私和个人信息安全。请仔细阅读本隐私政策以了解我们如何收集、使用和保护您的信息。'
          : 'We are committed to protecting your privacy and personal information security. Please read this privacy policy carefully to understand how we collect, use and protect your information.'
      };
    },
    
    onLangChange(newLang) {
      this.lang = newLang;
      this.setNavTitle();
      // 语言切换时重新获取内容
      this.getPrivacyContent();
    },
    setNavTitle() {
      const title = this.lang === 'zh' ? '隐私协议' : 'Privacy Policy';
      uni.setNavigationBarTitle({ title });
    }
  }
}
</script>

<style scoped>
.privacy-page {
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #f5f7fa;
}
.privacy-container {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
  min-height: 500rpx;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.loading-text {
  font-size: 28rpx;
  color: #2979ff;
}

/* 内容样式 */
.content-container {
  min-height: 400rpx;
}
.content-body {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
}

/* 富文本内容样式优化 */
.content-body rich-text {
  word-break: break-all;
}
</style> 