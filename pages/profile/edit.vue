<template>
  <view class="edit-profile-page">
    <view class="edit-form">
      <!-- 头像编辑 -->
      <view class="form-item avatar-item">
        <view class="item-label">{{ lang === 'zh' ? '头像' : 'Avatar' }}</view>
        <view class="avatar-wrapper" @click="chooseAvatar">
          <u-avatar :src="userForm.avatar" size="140"></u-avatar>
          <view class="avatar-edit-icon" :class="{ uploading: uploading }">
            <u-icon v-if="!uploading" name="camera-fill" color="#ffffff" size="40"></u-icon>
            <u-loading-icon v-else color="#ffffff" size="30"></u-loading-icon>
          </view>
          <view v-if="uploading" class="upload-mask">
            <text class="upload-text">{{ lang === 'zh' ? '上传中...' : 'Uploading...' }}</text>
          </view>
        </view>
      </view>
      
      <!-- 昵称编辑 -->
      <view class="form-item">
        <view class="item-label">{{ lang === 'zh' ? '昵称' : 'Nickname' }}</view>
        <u-input
          v-model="userForm.nickname"
          :placeholder="lang === 'zh' ? '请输入昵称' : 'Enter nickname'"
          border="bottom"
          clearable
        ></u-input>
      </view>
      
      <!-- 个性签名编辑 -->
      <view class="form-item">
        <view class="item-label">{{ lang === 'zh' ? '个性签名' : 'Signature' }}</view>
        <u-input
          v-model="userForm.signature"
          :placeholder="lang === 'zh' ? '请输入个性签名' : 'Enter signature'"
          border="bottom"
          clearable
        ></u-input>
      </view>
      
      <!-- 个人简介编辑 -->
      <view class="form-item">
        <view class="item-label">{{ lang === 'zh' ? '个人简介' : 'Description' }}</view>
        <u-textarea
          v-model="userForm.desc"
          :placeholder="lang === 'zh' ? '请输入个人简介' : 'Enter description'"
          count
          :maxlength="100"
          height="200"
        ></u-textarea>
      </view>
      
      <!-- 保存按钮 -->
      <view class="btn-wrapper">
        <u-button type="primary" @click="saveProfile" :text="lang === 'zh' ? '保存' : 'Save'"></u-button>
      </view>
    </view>
    
    <!-- 语言切换 -->
    <LangSwitch />
  </view>
</template>

<script>
import LangSwitch from '@/components/LangSwitch.vue';
import { File } from '@/utils/http.js';
import { postUpdateMe } from '@/api/user.js';

export default {
  components: {
    LangSwitch
  },
  data() {
    return {
      lang: 'zh',
      uploading: false, // 头像上传状态
      userForm: {
        avatar: '',
        nickname: '',
        signature: '',
        desc: ''
      }
    };
  },
  mounted() {
    // 获取语言设置
    const lang = uni.getStorageSync('lang') || 'zh';
    this.lang = lang;
    
    // 监听语言变化
    uni.$on('lang-change', this.onLangChange);
    
    // 设置导航栏标题
    this.setNavTitle();
    
    // 获取用户信息
    this.getUserInfo();
  },
  beforeDestroy() {
    uni.$off('lang-change', this.onLangChange);
  },
  methods: {
    onLangChange(newLang) {
      this.lang = newLang;
      this.setNavTitle();
    },
    setNavTitle() {
      const title = this.lang === 'zh' ? '编辑资料' : 'Edit Profile';
      uni.setNavigationBarTitle({ title });
    },
    getUserInfo() {
      // 从本地存储获取用户信息
      const userInfo = uni.getStorageSync('userInfo') || {
        avatar: 'https://picsum.photos/200',
        nickname: 'User123',
        signature: '这是个性签名',
        desc: '这里是用户描述信息'
      };
      
      // 兼容新的数据结构
      this.userForm = {
        avatar: userInfo.avatar || 'https://picsum.photos/200',
        nickname: userInfo.nickname || 'User123',
        signature: userInfo.signature || '这是个性签名',
        desc: userInfo.desc || '这里是用户描述信息'
      };
    },
    chooseAvatar() {
      // 如果正在上传，不允许再次选择
      if (this.uploading) {
        uni.showToast({
          title: this.lang === 'zh' ? '正在上传中，请稍候' : 'Uploading, please wait',
          icon: 'none'
        });
        return;
      }
      
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          // 获取图片路径
          const tempFilePath = res.tempFilePaths[0];
          
          // 上传头像到服务器
          this.uploadAvatar(tempFilePath);
        },
        fail: (error) => {
          console.error('选择图片失败:', error);
          uni.showToast({
            title: this.lang === 'zh' ? '选择图片失败' : 'Failed to select image',
            icon: 'none'
          });
        }
      });
    },
    // 上传头像
    uploadAvatar(filePath) {
      this.uploading = true;
      
      // 准备上传数据
      const uploadData = {
        temfile: filePath,
        type: 'avatar' // 标识这是头像上传
      };
      
      // 调用上传接口
      File(
        uploadData,
        (result) => {
          // 上传成功
          console.log('头像上传成功:', result);
          this.uploading = false;
          
          if (result.data && result.data.length > 0 && result.data[0].url) {
            // 更新头像URL - data是数组，取第一个元素的url
            this.userForm.avatar = result.data[0].url;
            uni.showToast({
              title: this.lang === 'zh' ? '头像上传成功' : 'Avatar uploaded successfully',
              icon: 'success'
            });
          } else {
            console.error('上传响应数据格式异常:', result);
            uni.showToast({
              title: this.lang === 'zh' ? '上传失败，返回数据异常' : 'Upload failed, invalid response',
              icon: 'none'
            });
          }
        },
        (error) => {
          // 上传失败
          console.error('头像上传失败:', error);
          this.uploading = false;
          
          const errorMsg = error.msg || error.message || '上传失败';
          uni.showToast({
            title: this.lang === 'zh' ? errorMsg : 'Upload failed',
            icon: 'none'
          });
        }
      );
    },
    async saveProfile() {
      // 表单验证
      if (!this.userForm.nickname) {
        return uni.showToast({
          title: this.lang === 'zh' ? '请输入昵称' : 'Please enter nickname',
          icon: 'none'
        });
      }
      
      try {
        // 显示加载中
        uni.showLoading({
          title: this.lang === 'zh' ? '保存中...' : 'Saving...'
        });
        
        // 调用更新用户信息接口
        const response = await postUpdateMe({
          name: this.userForm.nickname,
          head: this.userForm.avatar, // 如果头像已上传，这里应该是服务器返回的URL
          // 根据接口需要添加其他字段
        });
        
        uni.hideLoading();
        
        if (response && response.code === 0) {
          // 更新本地存储的用户信息
          const userInfo = uni.getStorageSync('userInfo') || {};
          userInfo.nickname = this.userForm.nickname;
          userInfo.avatar = this.userForm.avatar;
          userInfo.signature = this.userForm.signature;
          userInfo.desc = this.userForm.desc;
          uni.setStorageSync('userInfo', userInfo);
          
          uni.showToast({
            title: this.lang === 'zh' ? '保存成功' : 'Saved successfully',
            icon: 'success'
          });
          
          // 返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          uni.showToast({
            title: this.lang === 'zh' ? '保存失败，请重试' : 'Save failed, please try again',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('保存用户信息失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: this.lang === 'zh' ? '保存失败，请检查网络' : 'Save failed, please check network',
          icon: 'none'
        });
      }
    }
  }
}
</script>

<style scoped>
.edit-profile-page {
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #f5f7fa;
}
.edit-form {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}
.form-item {
  margin-bottom: 40rpx;
}
.item-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.avatar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 50rpx;
}
.avatar-wrapper {
  position: relative;
  margin-top: 20rpx;
}
.avatar-edit-icon {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 60rpx;
  height: 60rpx;
  background-color: #2979ff;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.avatar-edit-icon.uploading {
  background-color: #ff9900;
}
.upload-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-text {
  color: #ffffff;
  font-size: 24rpx;
}
.btn-wrapper {
  margin-top: 60rpx;
  padding: 0 20rpx;
}
</style> 