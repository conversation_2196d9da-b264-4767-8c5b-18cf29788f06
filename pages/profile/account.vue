<template>
  <view class="account-page">
    <view class="account-container">
      <!-- 手机号绑定 -->
      <view class="security-section">
        <view class="section-title">{{ lang === 'zh' ? '手机号绑定' : 'Phone Number' }}</view>
        <view class="security-item" @click="onPhoneBinding">
          <view class="security-info">
            <u-icon name="phone" size="28" color="#2979ff"></u-icon>
            <view class="security-content">
              <view class="security-label">{{ lang === 'zh' ? '已绑定手机号' : 'Bound Phone' }}</view>
              <view class="security-value">{{ phoneNumber || (lang === 'zh' ? '未绑定' : 'Not Bound') }}</view>
            </view>
          </view>
          <view class="security-action">
            <text class="action-text">{{ phoneNumber ? (lang === 'zh' ? '修改' : 'Change') : (lang === 'zh' ? '绑定' : 'Bind') }}</text>
            <u-icon name="arrow-right" size="20" color="#c0c4cc"></u-icon>
          </view>
        </view>
      </view>

      <!-- 弹窗：手机号绑定 -->
      <u-popup :show="showPhonePopup" mode="center" :round="16" width="80%" :safeAreaInsetBottom="false">
        <view class="popup-container">
          <view class="popup-title">{{ phoneNumber ? (lang === 'zh' ? '修改手机号' : 'Change Phone Number') : (lang === 'zh' ? '绑定手机号' : 'Bind Phone Number') }}</view>
          <view class="popup-form">
            <view class="form-item">
              <u-input 
                v-model="formPhone" 
                :placeholder="lang === 'zh' ? '请输入手机号' : 'Enter phone number'"
                type="number"
                border="bottom"
                clearable
              ></u-input>
            </view>
            <view class="form-item verification-code">
              <u-input 
                v-model="formCode" 
                :placeholder="lang === 'zh' ? '请输入验证码' : 'Enter verification code'"
                type="number"
                border="bottom"
                clearable
                class="code-input"
              ></u-input>
              <view class="code-button" :class="{ 'disabled': codeButtonDisabled }" @click="!codeButtonDisabled && sendVerificationCode()">
                {{ codeButtonText }}
              </view>
            </view>
          </view>
          <view class="popup-buttons">
            <view class="cancel-button" @click="cancelPhoneBinding" v-if="lang === 'zh'">取消</view>
            <view class="cancel-button" @click="cancelPhoneBinding" v-else>Cancel</view>
            <view class="confirm-button" @click="confirmPhoneBinding" v-if="lang === 'zh'">确认</view>
            <view class="confirm-button" @click="confirmPhoneBinding" v-else>Confirm</view>
          </view>
        </view>
      </u-popup>

      <LangSwitch />
    </view>
  </view>
</template>

<script>
import LangSwitch from '@/components/LangSwitch.vue';

export default {
  components: {
    LangSwitch
  },
  data() {
    return {
      lang: 'zh',
      phoneNumber: '', // 模拟数据
      
      // 弹窗相关
      showPhonePopup: false,
      formPhone: '',
      formCode: '',
      
      // 验证码按钮
      codeCountdown: 0,
      codeTimer: null
    };
  },
  computed: {
    codeButtonText() {
      if (this.codeCountdown > 0) {
        return `${this.codeCountdown}s`;
      }
      return this.lang === 'zh' ? '获取验证码' : 'Get Code';
    },
    codeButtonDisabled() {
      return this.codeCountdown > 0;
    }
  },
  onLoad() {
    // 获取语言设置
    const lang = uni.getStorageSync('lang') || 'zh';
    this.lang = lang;
    
    // 监听语言变化
    uni.$on('lang-change', this.onLangChange);
    
    // 设置导航栏标题
    this.setNavTitle();
    
    // 获取账户安全信息
    this.getAccountSecurityInfo();
  },
  onUnload() {
    uni.$off('lang-change', this.onLangChange);
    
    // 清除定时器
    if (this.codeTimer) {
      clearInterval(this.codeTimer);
    }
  },
  methods: {
    onLangChange(newLang) {
      this.lang = newLang;
      this.setNavTitle();
    },
    setNavTitle() {
      const title = this.lang === 'zh' ? '账户安全' : 'Account Security';
      uni.setNavigationBarTitle({ title });
    },
    getAccountSecurityInfo() {
      // 模拟从API获取账户安全信息
      // 实际项目中应该从服务器获取
      setTimeout(() => {
        // 模拟数据 - 默认未绑定手机号
        this.phoneNumber = '';
      }, 500);
    },
    
    // 手机号绑定相关
    onPhoneBinding() {
      this.formPhone = '';
      this.formCode = '';
      this.showPhonePopup = true;
    },
    sendVerificationCode() {
      // 验证手机号格式
      if (!this.formPhone || this.formPhone.length !== 11) {
        return uni.showToast({
          title: this.lang === 'zh' ? '请输入正确的手机号' : 'Please enter a valid phone number',
          icon: 'none'
        });
      }
      
      // 开始倒计时
      this.codeCountdown = 60;
      this.codeTimer = setInterval(() => {
        this.codeCountdown--;
        if (this.codeCountdown <= 0) {
          clearInterval(this.codeTimer);
        }
      }, 1000);
      
      // 模拟发送验证码
      uni.showToast({
        title: this.lang === 'zh' ? '验证码已发送' : 'Verification code sent',
        icon: 'success'
      });
    },
    cancelPhoneBinding() {
      this.showPhonePopup = false;
    },
    confirmPhoneBinding() {
      // 验证手机号和验证码
      if (!this.formPhone || this.formPhone.length !== 11) {
        return uni.showToast({
          title: this.lang === 'zh' ? '请输入正确的手机号' : 'Please enter a valid phone number',
          icon: 'none'
        });
      }
      if (!this.formCode || this.formCode.length !== 6) {
        return uni.showToast({
          title: this.lang === 'zh' ? '请输入6位验证码' : 'Please enter 6-digit verification code',
          icon: 'none'
        });
      }
      
      // 模拟绑定成功
      uni.showLoading({
        title: this.lang === 'zh' ? '处理中...' : 'Processing...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        this.showPhonePopup = false;
        
        // 更新手机号
        this.phoneNumber = this.formPhone.substring(0, 3) + '****' + this.formPhone.substring(7);
        
        uni.showToast({
          title: this.lang === 'zh' ? '手机号绑定成功' : 'Phone number bound successfully',
          icon: 'success'
        });
      }, 1500);
    }
  }
}
</script>

<style scoped>
.account-page {
  min-height: 100vh;
  background-color: #f8f8f8;
}
.account-container {
  padding: 24rpx;
}
.security-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  padding: 0 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
}
.section-title {
  font-size: 28rpx;
  color: #909399;
  padding: 24rpx 0 16rpx;
}
.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-top: 1px solid #f5f5f5;
}
.security-item:first-child {
  border-top: none;
}
.security-info {
  display: flex;
  align-items: center;
  flex: 1;
}
.security-content {
  flex: 1;
  margin-left: 24rpx; /* 增加图标与文本的距离 */
}
.security-label {
  font-size: 28rpx;
  color: #303133;
  margin-bottom: 4rpx;
}
.security-value {
  font-size: 24rpx;
  color: #909399;
}
.security-action {
  display: flex;
  align-items: center;
}
.action-text {
  font-size: 24rpx;
  color: #2979ff;
  margin-right: 8rpx;
}

/* 弹窗样式 */
.popup-container {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  border-radius: 14rpx;
}
.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #303133;
}
.popup-form {
  margin-bottom: 40rpx;
}
.form-item {
  margin-bottom: 30rpx;
}
.verification-code {
  display: flex;
  align-items: center;
}
.code-input {
  flex: 1;
  margin-right: 20rpx;
}
.code-button {
  width: 180rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2979ff;
  color: #ffffff;
  font-size: 26rpx;
  border-radius: 8rpx;
  margin-left: 20rpx;
}
.code-button.disabled {
  background-color: #a0cfff;
  color: #ffffff;
}
.popup-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}
.cancel-button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #f2f2f2;
  color: #606266;
  border-radius: 8rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
}
.confirm-button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #2979ff;
  color: #ffffff;
  border-radius: 8rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}
</style> 