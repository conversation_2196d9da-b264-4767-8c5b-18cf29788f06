<template>
    <view class="index-page">
        <!-- 搜索栏 -->
        <view class="search-bar">
            <u-search :placeholder="lang === 'zh' ? '搜索我参与的活动' : 'Search My Joined Events'" v-model="searchValue"
                @search="onSearch" :show-action="false" />
        </view>

        <view class="activity-list">
            <view v-for="item in filteredActivityList" :key="item.id" class="activity-card" @click="onActivityClick(item)">
                <view class="card-img-wrap">
                    <view style="width: 100%;"><u-image :src="item.img" width="100%" height="250rpx"
                            border-radius="16rpx 16rpx 0 0" /></view>
                    <view class="status-tag">
                        <u-tag :text="statusText(item.status)" :type="statusType(item.status)" size="mini" />
                    </view>
                </view>
                <view class="card-content">
                    <view class="card-title">{{ item.title }}</view>
                    <view class="card-subtitle">{{ item.time }}</view>
                    <view class="card-desc">{{ item.desc }}</view>
                </view>
            </view>
        </view>

        <CustomTabbar :current="2" />
        <LangSwitch />
    </view>
</template>

<script>
import CustomTabbar from '../../components/CustomTabbar.vue'
import LangSwitch from '../../components/LangSwitch.vue'
export default {
    components: {
        CustomTabbar,
        LangSwitch
    },
    data() {
        return {
            lang: uni.getStorageSync('lang') || 'zh',
            searchValue: '',
            showPopup: true,
            activityList: [{
                id: 1,
                title: '篮球训练营',
                time: '2024-07-10',
                img: 'https://picsum.photos/800/600?random=4',
                desc: '专业教练指导，提升篮球技能。',
                status: 'not_started'
            },
            {
                id: 2,
                title: '少儿美术班',
                time: '2024-07-15',
                img: 'https://picsum.photos/800/600?random=5',
                desc: '培养孩子艺术素养，激发创造力。',
                status: 'answered'
            },
            {
                id: 3,
                title: '青少年编程课程',
                time: '2024-07-05',
                img: 'https://picsum.photos/800/600?random=6',
                desc: '学习基础编程知识，培养逻辑思维能力。',
                status: 'in_progress'
            },
            {
                id: 4,
                title: '儿童英语口语训练',
                time: '2024-06-28',
                img: 'https://picsum.photos/800/600?random=7',
                desc: '外教一对一，提高英语口语表达能力。',
                status: 'ended'
            }
            ]
        }
    },
    computed: {
        filteredActivityList() {
            // 如果没有搜索值，返回全部活动
            if (!this.searchValue) {
                return this.activityList;
            }
            
            // 搜索过滤
            const val = this.searchValue.toLowerCase();
            return this.activityList.filter(item => 
                item.title.toLowerCase().includes(val) || 
                item.desc.toLowerCase().includes(val)
            );
        }
    },
    mounted() {
        uni.$on('lang-change', this.onLangChange)
        this.setNavTitle() // 新增：页面加载时设置标题
    },
    beforeDestroy() {
        uni.$off('lang-change', this.onLangChange)
    },
    methods: {
        statusText(status) {
            const map = {
                not_started: this.lang === 'zh' ? '未开始' : 'Not Started',
                in_progress: this.lang === 'zh' ? '进行中' : 'In Progress',
                ended: this.lang === 'zh' ? '已结束' : 'Ended',
                answered: this.lang === 'zh' ? '已答题' : 'Answered'
            }
            return map[status] || status
        }, statusType(status) {
            const map = {
                not_started: 'info',
                in_progress: 'primary',
                ended: 'warning',
                answered: 'success'
            }
            return map[status] || 'default'
        },
        onSearch(val) {
            // 更新搜索值
            this.searchValue = val;
            
            // 通过计算属性自动过滤
            // 如果搜索内容不为空且有结果，显示提示
            if (val && this.filteredActivityList.length > 0) {
                uni.showToast({
                    title: this.lang === 'zh' ? 
                        `找到 ${this.filteredActivityList.length} 个活动` : 
                        `Found ${this.filteredActivityList.length} activities`,
                    icon: 'none'
                });
            } else if (val && this.filteredActivityList.length === 0) {
                uni.showToast({
                    title: this.lang === 'zh' ? '没有找到相关活动' : 'No matching activities',
                    icon: 'none'
                });
            }
        },
        onCategoryClick(idx) {
            // 分类点击逻辑
            uni.showToast({
                title: '点击分类：' + (this.lang === 'zh' ? this.categoryList[idx].name : this.categoryList[idx]
                    .name_en),
                icon: 'none'
            })
        },
        onActivityClick(item) {
            // 根据活动状态决定跳转
            if (item.status === 'not_started') {
                // 未开始的活动，跳转到活动详情页
                uni.navigateTo({
                    url: `/pages/events/detail?id=${item.id}`
                });
            } else if (item.status === 'answered') {
                // 已答题的活动，跳转到答题结果反馈页面
                uni.navigateTo({
                    url: `/pages/survey/result?id=${item.id}`
                });
            } else {
                // 其他状态，跳转到调查问卷页面
                uni.navigateTo({
                    url: `/pages/survey/index?id=${item.id}`
                });
            }
        },
        onLangChange(newLang) {
            this.lang = newLang
            this.setNavTitle() // 新增：语言切换时设置标题
        },
        // 新增：设置导航栏标题的方法
        setNavTitle() {
            const title = this.lang === 'zh' ? '我参与的' : 'My Joined'
            uni.setNavigationBarTitle({
                title: title
            })
        }
    }
}
</script>

<style scoped>
.card-img-wrap {
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    width: 100%;
}

.status-tag {
    position: absolute;
    top: 16rpx;
    right: 16rpx;
    z-index: 2;
}

.poster-popup {
    /* 消除 u-popup 内部的默认 padding */
    line-height: 1;
}

.search-bar {
    margin: 24rpx;
    margin-top: 0rpx;
    padding-top: 24rpx;
    box-sizing: border-box;
}

.index-page {
    min-height: 100vh;
    padding-bottom: 0;
}

.banner-swiper {
    margin: 24rpx;
}

.category-grid {
    margin-top: 24rpx;
}

.cat-title {
    font-size: 24rpx;
    margin-top: 8rpx;
    color: #333;
}

.section-title {
    font-size: 32rpx;
    font-weight: bold;
    margin: 32rpx 24rpx 16rpx 24rpx;
    color: #222;
}

.activity-list {
    padding: 0 24rpx;
}

.activity-card {
    margin-bottom: 24rpx;
    background: #fff;
    border-radius: 16rpx;
    width: 100%;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.card-content {
    padding: 24rpx;
}

.card-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #303133;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-subtitle {
    font-size: 24rpx;
    color: #909399;
    margin-top: 8rpx;
}

.card-desc {
    font-size: 26rpx;
    color: #606266;
    margin-top: 16rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.popup-content {
    padding: 48rpx 32rpx;
    text-align: center;
}
</style>