<template>
  <view class="past-events-page">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <u-search :placeholder="lang === 'zh' ? '搜索往期风采' : 'Search past events'" v-model="searchValue" @search="onSearch" :show-action="false" />
    </view>

    <!-- 搜索结果 -->
    <view class="result-info" v-if="searchValue">
      <text class="result-text">
        {{ lang === 'zh' ? 
          `"${searchValue}" 的搜索结果 (${filteredEvents.length})` : 
          `Search results for "${searchValue}" (${filteredEvents.length})` 
        }}
      </text>
    </view>

    <!-- 往期风采列表 -->
    <view class="event-list">
      <view v-for="item in filteredEvents" :key="item.id" class="event-card" @click="navigateToDetail(item.id)">
        <u-image :src="item.img" width="100%" height="250rpx" border-radius="16rpx 16rpx 0 0" />
        <view class="card-content">
          <view class="card-title">{{ item.title }}</view>
          <view class="card-desc">{{ item.desc }}</view>
          <view class="card-footer">
            <view class="card-date">
              <u-icon name="calendar" size="24rpx" color="#909399"></u-icon>
              <text>{{ item.date }}</text>
            </view>
            <view class="card-views">
              <u-icon name="eye" size="24rpx" color="#909399"></u-icon>
              <text>{{ item.views }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 无搜索结果 -->
    <view class="no-results" v-if="searchValue && filteredEvents.length === 0">
      <u-empty 
        mode="search" 
        :text="lang === 'zh' ? '没有找到相关内容' : 'No results found'" 
      ></u-empty>
    </view>
    
    <!-- 加载更多 -->
    <u-loadmore 
      :status="loadMoreStatus" 
      :loading-text="lang === 'zh' ? '正在加载...' : 'Loading...'" 
      :loadmore-text="lang === 'zh' ? '点击加载更多' : 'Load more'" 
      :nomore-text="lang === 'zh' ? '没有更多了' : 'No more data'"
      @loadmore="loadMore"
      margin-top="20"
      margin-bottom="20"
      v-if="filteredEvents.length > 0"
    ></u-loadmore>
    
    <LangSwitch />
  </view>
</template>

<script>
import LangSwitch from '@/components/LangSwitch.vue';

export default {
  components: {
    LangSwitch
  },
  data() {
    return {
      lang: uni.getStorageSync('lang') || 'zh',
      searchValue: '',
      pastEvents: [],
      page: 1,
      limit: 10,
      loadMoreStatus: 'loadmore',
      allEventsLoaded: false
    }
  },
  computed: {
    filteredEvents() {
      if (!this.searchValue) {
        return this.pastEvents;
      }
      
      const val = this.searchValue.toLowerCase();
      return this.pastEvents.filter(item => 
        item.title.toLowerCase().includes(val) || 
        item.desc.toLowerCase().includes(val)
      );
    }
  },
  onLoad(options) {
    // 从本地存储获取语言设置
    this.lang = uni.getStorageSync('lang') || 'zh';
    
    // 从全局获取语言设置
    if (getApp().globalData && getApp().globalData.lang) {
      this.lang = getApp().globalData.lang;
    }
    
    // 监听语言变化
    uni.$on('lang-change', this.onLangChange);
    
    // 设置导航栏标题
    this.setNavigationBarTitle();
    
    // 如果有搜索关键词，则设置
    if (options.keyword) {
      this.searchValue = decodeURIComponent(options.keyword);
    }
    
    // 加载往期风采数据
    this.loadPastEvents();
  },
  onUnload() {
    // 取消监听语言变化
    uni.$off('lang-change', this.onLangChange);
  },
  methods: {
    onLangChange(newLang) {
      this.lang = newLang;
      this.setNavigationBarTitle();
    },
    setNavigationBarTitle() {
      uni.setNavigationBarTitle({
        title: this.lang === 'zh' ? '往期风采' : 'Past Events'
      });
    },
    onSearch(val) {
      // 如果是事件对象，则从输入框获取值
      if (typeof val === 'object' && val !== null) {
        val = this.searchValue;
      }
      
      // 更新搜索值
      this.searchValue = val;
      
      // 重置页码和加载状态
      this.page = 1;
      this.loadMoreStatus = 'loadmore';
      this.allEventsLoaded = false;
      
      // 重新加载数据
      this.loadPastEvents();
    },
    loadPastEvents() {
      // 模拟加载数据，实际应用中应该通过API获取
      // 这里使用setTimeout模拟异步请求
      if (this.loadMoreStatus === 'loading' || this.allEventsLoaded) return;
      
      this.loadMoreStatus = 'loading';
      
      setTimeout(() => {
        // 模拟数据
        const mockData = [
          { 
            id: 1, 
            title: '2023夏季编程营精彩回顾', 
            img: 'https://images.unsplash.com/photo-1530124566582-a618bc2615dc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', 
            desc: '2023年夏令营圆满结束，120名学员参与，收获满满。',
            date: '2023-09-01',
            views: 1286
          },
          { 
            id: 2, 
            title: '2022冬令营精彩瞬间', 
            img: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', 
            desc: '2022冬令营活动丰富，学员们在学习中收获快乐。',
            date: '2022-12-28',
            views: 962
          },
          { 
            id: 3, 
            title: '2021青少年科技展回顾', 
            img: 'https://images.unsplash.com/photo-1581472723648-909f4851d4ae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', 
            desc: '青少年科技展展示了学生们的创新能力和科学素养。',
            date: '2021-11-15',
            views: 754
          },
          { 
            id: 4, 
            title: '2021春季音乐会回顾', 
            img: 'https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', 
            desc: '春季音乐会上，学生们展示了精彩的音乐才艺。',
            date: '2021-04-20',
            views: 683
          },
          { 
            id: 5, 
            title: '2020冬季艺术节回顾', 
            img: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', 
            desc: '冬季艺术节展示了学生们在艺术领域的才华和创意。',
            date: '2020-12-15',
            views: 592
          },
          { 
            id: 6, 
            title: '2020夏季运动会回顾', 
            img: 'https://images.unsplash.com/photo-1517649763962-0c623066013b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', 
            desc: '夏季运动会上，学生们展现了团队协作和体育精神。',
            date: '2020-07-30',
            views: 547
          }
        ];
        
        // 模拟搜索
        let filteredData = mockData;
        if (this.searchValue) {
          const val = this.searchValue.toLowerCase();
          filteredData = mockData.filter(item => 
            item.title.toLowerCase().includes(val) || 
            item.desc.toLowerCase().includes(val)
          );
        }
        
        // 模拟分页
        const start = (this.page - 1) * this.limit;
        const end = this.page * this.limit;
        const pageData = filteredData.slice(start, end);
        
        if (this.page === 1) {
          this.pastEvents = pageData;
        } else {
          this.pastEvents = [...this.pastEvents, ...pageData];
        }
        
        // 更新加载状态
        if (this.pastEvents.length >= filteredData.length) {
          this.loadMoreStatus = 'nomore';
          this.allEventsLoaded = true;
        } else {
          this.loadMoreStatus = 'loadmore';
        }
      }, 1000);
    },
    loadMore() {
      this.page++;
      this.loadPastEvents();
    },
    navigateToDetail(id) {
      uni.navigateTo({
        url: `/pages/past-events/detail?id=${id}`
      });
    }
  }
}
</script>

<style scoped>
.past-events-page {
  min-height: 100vh;
  padding-bottom: 30rpx;
  box-sizing: border-box;
}

.search-bar {
  margin: 24rpx;
  margin-top: 0rpx;
  padding-top: 24rpx;
  box-sizing: border-box;
}

.result-info {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.result-text {
  font-size: 28rpx;
  color: #909399;
}

.event-list {
  padding: 0 30rpx;
}

.event-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.card-content {
  padding: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10rpx;
}

.card-desc {
  font-size: 28rpx;
  color: #606266;
  margin-bottom: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #909399;
}

.card-date, .card-views {
  display: flex;
  align-items: center;
}

.card-date text, .card-views text {
  margin-left: 8rpx;
}

.no-results {
  padding: 100rpx 0;
}
</style> 