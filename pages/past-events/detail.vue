<template>
  <view class="past-event-detail">
    <!-- 顶部图片 -->
    <image class="cover-image" :src="eventData.coverImage" mode="widthFix"></image>
    
    <!-- 标题区域 -->
    <view class="header">
      <view class="title">{{ lang === 'zh' ? eventData.title : eventData.title_en }}</view>
      <view class="meta">
        <text class="date">{{ eventData.date }}</text>
        <text class="views">{{ eventData.views }}{{ lang === 'zh' ? '次浏览' : ' views' }}</text>
      </view>
    </view>
    
    <!-- 内容区域 -->
    <view class="content">
      <!-- 摘要 -->
      <view class="summary" v-if="eventData.summary">
        <text>{{ lang === 'zh' ? eventData.summary : eventData.summary_en }}</text>
      </view>
      
      <!-- 正文内容 -->
      <block v-for="(section, index) in eventData.sections" :key="index">
        <!-- 段落标题 -->
        <view class="section-title" v-if="section.title">
          {{ lang === 'zh' ? section.title : section.title_en }}
        </view>
        
        <!-- 段落文本 -->
        <view class="paragraph" v-if="section.content">
          <text>{{ lang === 'zh' ? section.content : section.content_en }}</text>
        </view>
        
        <!-- 段落图片 -->
        <view class="section-image" v-if="section.image">
          <image :src="section.image" mode="widthFix"></image>
          <view class="image-caption" v-if="section.caption">
            {{ lang === 'zh' ? section.caption : section.caption_en }}
          </view>
        </view>
      </block>
      
      <!-- 活动总结 -->
      <view class="conclusion" v-if="eventData.conclusion">
        <view class="conclusion-title">
          {{ lang === 'zh' ? '活动总结' : 'Event Summary' }}
        </view>
        <view class="conclusion-content">
          {{ lang === 'zh' ? eventData.conclusion : eventData.conclusion_en }}
        </view>
      </view>
    </view>
    
    <!-- 底部分享和参与 -->
    <!-- <view class="footer">
      <view class="share-btn" @click="shareEvent">
        <u-icon name="share" size="40rpx" color="#2979ff"></u-icon>
        <text>{{ lang === 'zh' ? '分享' : 'Share' }}</text>
      </view>
      <view class="join-btn" @click="navigateToEvents">
        <text>{{ lang === 'zh' ? '查看更多活动' : 'More Events' }}</text>
        <u-icon name="arrow-right" size="32rpx" color="#ffffff"></u-icon>
      </view>
    </view> -->
    
    <!-- 语言切换按钮 -->
    <view class="lang-switch" @click="switchLanguage">
      <text>{{ lang === 'zh' ? 'EN' : '中' }}</text>
    </view>
    
    <!-- 分享弹窗 -->
    <u-popup :show="showSharePopup" mode="bottom" @close="showSharePopup = false">
      <view class="share-popup">
        <view class="share-title">{{ lang === 'zh' ? '分享到' : 'Share to' }}</view>
        <view class="share-options">
          <view class="share-option" @click="handleShare('wechat')">
            <u-icon name="weixin-fill" size="60rpx" color="#09BB07"></u-icon>
            <text>{{ lang === 'zh' ? '微信' : 'WeChat' }}</text>
          </view>
          <view class="share-option" @click="handleShare('moments')">
            <u-icon name="moment" size="60rpx" color="#09BB07"></u-icon>
            <text>{{ lang === 'zh' ? '朋友圈' : 'Moments' }}</text>
          </view>
          <view class="share-option" @click="handleShare('link')">
            <u-icon name="link" size="60rpx" color="#2979ff"></u-icon>
            <text>{{ lang === 'zh' ? '复制链接' : 'Copy Link' }}</text>
          </view>
        </view>
        <view class="cancel-btn" @click="showSharePopup = false">
          {{ lang === 'zh' ? '取消' : 'Cancel' }}
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      lang: 'zh',
      eventId: null,
      showSharePopup: false,
      eventData: {
        id: 1,
        title: '2023夏令营精彩回顾',
        title_en: '2023 Summer Camp Highlights',
        coverImage: 'https://images.unsplash.com/photo-1530124566582-a618bc2615dc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
        date: '2023-09-01',
        views: 1286,
        summary: '2023年夏令营圆满结束，本次活动共有120名学员参与，通过丰富多彩的课程和活动，学员们不仅学到了知识，还收获了友谊和成长。',
        summary_en: 'The 2023 Summer Camp has successfully concluded with 120 students participating. Through a variety of courses and activities, students not only gained knowledge but also friendship and personal growth.',
        sections: [
          {
            title: '活动亮点',
            title_en: 'Event Highlights',
            content: '本次夏令营为期两周，涵盖了编程、艺术、体育和科学探索等多个领域的课程。学员们在专业教师的指导下，完成了多个实践项目，展示了自己的创造力和团队协作能力。',
            content_en: 'This two-week summer camp covered courses in programming, arts, sports, and scientific exploration. Under the guidance of professional teachers, students completed multiple practical projects, showcasing their creativity and teamwork.'
          },
          {
            image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
            caption: '学员们在编程课上专注学习',
            caption_en: 'Students focusing on programming class'
          },
          {
            title: '编程课程',
            title_en: 'Programming Courses',
            content: '编程课程是本次夏令营的重点之一。学员们从基础的编程概念开始学习，逐步掌握了算法思维和问题解决能力。在课程结束时，每位学员都能独立完成一个小型项目，如简单游戏或网页应用。',
            content_en: 'Programming was one of the highlights of this summer camp. Students started with basic programming concepts and gradually mastered algorithmic thinking and problem-solving skills. By the end of the course, each student could independently complete a small project such as a simple game or web application.'
          },
          {
            image: 'https://images.unsplash.com/photo-1517164850305-99a3e65bb47e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
            caption: '团队合作完成科学实验',
            caption_en: 'Team collaboration on scientific experiments'
          },
          {
            title: '科学探索',
            title_en: 'Scientific Exploration',
            content: '科学探索课程让学员们亲身体验了科学实验的乐趣。从物理现象到化学反应，从生物观察到天文知识，学员们在动手实践中理解了科学原理，培养了科学思维和探索精神。',
            content_en: 'The scientific exploration courses allowed students to experience the joy of scientific experiments firsthand. From physical phenomena to chemical reactions, from biological observations to astronomical knowledge, students understood scientific principles through hands-on practice and developed scientific thinking and exploratory spirit.'
          }
        ],
        conclusion: '本次夏令营不仅丰富了学员们的暑假生活，也为他们提供了一个展示才能、发掘潜力的平台。我们看到了每位学员的成长和进步，也期待在未来的活动中再次相见。感谢所有参与和支持本次活动的家长、老师和工作人员，让我们共同为孩子们的成长助力！',
        conclusion_en: 'This summer camp not only enriched the students\' summer vacation but also provided them with a platform to showcase their talents and discover their potential. We witnessed the growth and progress of each student and look forward to meeting them again in future activities. Thanks to all parents, teachers, and staff who participated in and supported this event, let\'s continue to help our children grow together!'
      }
    };
  },
  onLoad(options) {
    // 从全局获取语言设置
    if (getApp().globalData.lang) {
      this.lang = getApp().globalData.lang;
    }
    
    // 监听语言变化
    uni.$on('languageChanged', (data) => {
      this.lang = data.lang;
      this.setNavigationBarTitle();
    });
    
    // 获取活动ID
    if (options.id) {
      this.eventId = options.id;
      // 实际应用中，这里应该通过API获取活动详情
      // this.fetchEventDetail(this.eventId);
    }
    
    // 设置导航栏标题
    this.setNavigationBarTitle();
    
    // 模拟增加浏览量
    this.eventData.views++;
  },
  onUnload() {
    // 取消监听语言变化
    uni.$off('languageChanged');
  },
  methods: {
    setNavigationBarTitle() {
      uni.setNavigationBarTitle({
        title: this.lang === 'zh' ? '活动回顾' : 'Event Review'
      });
    },
    switchLanguage() {
      const newLang = this.lang === 'zh' ? 'en' : 'zh';
      this.lang = newLang;
      
      // 更新全局语言设置
      if (getApp().globalData) {
        getApp().globalData.lang = newLang;
      }
      
      // 发送语言变化事件
      uni.$emit('languageChanged', { lang: newLang });
      
      // 设置导航栏标题
      this.setNavigationBarTitle();
    },
    shareEvent() {
      this.showSharePopup = true;
    },
    handleShare(platform) {
      // 模拟分享功能
      uni.showToast({
        title: this.lang === 'zh' ? '分享成功' : 'Shared successfully',
        icon: 'success'
      });
      this.showSharePopup = false;
      
      // 实际应用中，这里应该调用相应的分享API
      if (platform === 'link') {
        uni.setClipboardData({
          data: 'https://example.com/past-events/detail?id=' + this.eventId,
          success: () => {
            uni.showToast({
              title: this.lang === 'zh' ? '链接已复制' : 'Link copied',
              icon: 'success'
            });
          }
        });
      }
    },
    navigateToEvents() {
      uni.navigateTo({
        url: '/pages/events/index'
      });
    },
    fetchEventDetail(id) {
      // 实际应用中，这里应该通过API获取活动详情
      // 这里仅作为示例，使用静态数据
    }
  }
};
</script>

<style lang="scss" scoped>
.past-event-detail {
  padding-bottom: 40rpx;
  background-color: #ffffff;
}

.cover-image {
  width: 100%;
  height: auto;
}

.header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f2f2f2;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #303133;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

.meta {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #909399;
}

.date {
  margin-right: 20rpx;
}

.content {
  padding: 30rpx;
}

.summary {
  padding: 20rpx;
  background-color: #f8f8f8;
  border-left: 8rpx solid #2979ff;
  margin-bottom: 30rpx;
  font-size: 28rpx;
  color: #606266;
  line-height: 1.6;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  margin: 30rpx 0 20rpx;
  position: relative;
  padding-left: 20rpx;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 8rpx;
    bottom: 8rpx;
    width: 8rpx;
    background-color: #2979ff;
    border-radius: 4rpx;
  }
}

.paragraph {
  font-size: 28rpx;
  color: #606266;
  line-height: 1.8;
  margin-bottom: 30rpx;
  text-align: justify;
}

.section-image {
  margin: 20rpx 0 30rpx;
  
  image {
    width: 100%;
    height: auto;
    border-radius: 8rpx;
  }
}

.image-caption {
  font-size: 24rpx;
  color: #909399;
  text-align: center;
  margin-top: 10rpx;
}

.conclusion {
  margin-top: 40rpx;
  padding: 30rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.conclusion-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 20rpx;
}

.conclusion-content {
  font-size: 28rpx;
  color: #606266;
  line-height: 1.8;
  text-align: justify;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  margin-top: 20rpx;
  border-top: 1rpx solid #f2f2f2;
}

.share-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  text {
    font-size: 24rpx;
    color: #606266;
    margin-top: 8rpx;
  }
}

.join-btn {
  display: flex;
  align-items: center;
  background-color: #2979ff;
  color: #ffffff;
  padding: 20rpx 30rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  
  text {
    margin-right: 10rpx;
  }
}

.lang-switch {
  position: fixed;
  right: 30rpx;
  top: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  z-index: 10;
}

.share-popup {
  padding: 30rpx;
}

.share-title {
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 30rpx;
}

.share-options {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0 40rpx;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  text {
    font-size: 24rpx;
    color: #606266;
    margin-top: 16rpx;
  }
}

.cancel-btn {
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 30rpx;
  color: #303133;
  border-top: 1rpx solid #f2f2f2;
}
</style> 