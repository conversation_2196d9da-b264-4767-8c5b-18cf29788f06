<template>
  <view class="event-detail">
    <!-- 顶部活动轮播图 -->
    <swiper class="event-swiper" circular indicator-dots autoplay :interval="3000" :duration="500">
      <swiper-item v-for="(item, index) in eventData.images" :key="index">
        <image class="event-image" :src="item" mode="aspectFill"></image>
      </swiper-item>
    </swiper>
    
    <!-- 活动标题和状态 -->
    <view class="event-header">
      <view class="event-title">{{ lang === 'zh' ? eventData.title : eventData.title_en }}</view>
      <view class="event-status" :class="eventData.status">
        {{ lang === 'zh' ? statusTextZh[eventData.status] : statusTextEn[eventData.status] }}
      </view>
    </view>
    
    <!-- 活动信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <view class="card-title">{{ lang === 'zh' ? '活动信息' : 'Event Information' }}</view>
      </view>
      <view class="info-item">
        <u-icon name="calendar" size="36rpx" color="#2979ff"></u-icon>
        <text class="info-label">{{ lang === 'zh' ? '开始：' : 'Start: ' }}</text>
        <text class="info-content">{{ eventData.startTime }}</text>
      </view>
      <view class="info-item">
        <u-icon name="calendar" size="36rpx" color="#2979ff"></u-icon>
        <text class="info-label">{{ lang === 'zh' ? '结束：' : 'End: ' }}</text>
        <text class="info-content">{{ eventData.endTime }}</text>
      </view>
      <view class="info-item">
        <u-icon name="map" size="36rpx" color="#2979ff"></u-icon>
        <text class="info-label">{{ lang === 'zh' ? '地点：' : 'Location: ' }}</text>
        <text class="info-content">{{ lang === 'zh' ? eventData.location : eventData.location_en }}</text>
      </view>
      <view class="info-item">
        <u-icon name="account" size="36rpx" color="#2979ff"></u-icon>
        <text class="info-label">{{ lang === 'zh' ? '人数：' : 'Capacity: ' }}</text>
        <text class="info-content">{{ eventData.currentParticipants }}/{{ eventData.maxParticipants }}</text>
      </view>
      <view class="info-item">
        <u-icon name="rmb-circle" size="36rpx" color="#2979ff"></u-icon>
        <text class="info-label">{{ lang === 'zh' ? '费用：' : 'Fee: ' }}</text>
        <text class="info-content">{{ eventData.fee > 0 ? '¥' + eventData.fee : (lang === 'zh' ? '免费' : 'Free') }}</text>
      </view>
      <view class="info-item">
        <u-icon name="star" size="36rpx" color="#2979ff"></u-icon>
        <text class="info-label">{{ lang === 'zh' ? '积分：' : 'Points: ' }}</text>
        <text class="info-content">+{{ eventData.points }}</text>
      </view>
    </view>
    
    <!-- 活动详情 -->
    <view class="info-card">
      <view class="card-header">
        <view class="card-title">{{ lang === 'zh' ? '活动详情' : 'Event Details' }}</view>
      </view>
      <view class="event-description">{{ lang === 'zh' ? eventData.description : eventData.description_en }}</view>
    </view>
    
    <!-- 报名信息 -->
    <view class="info-card" v-if="eventData.status !== 'ended'">
      <view class="card-header">
        <view class="card-title">{{ lang === 'zh' ? '报名信息' : 'Registration' }}</view>
      </view>
      
      <!-- 选择报名信息 -->
      <view class="select-profile">
        <text class="select-label">{{ lang === 'zh' ? '选择报名信息：' : 'Select Profile:' }}</text>
        <view class="profile-selector" @click="showProfileSelector = true">
          <text>{{ selectedProfile ? selectedProfile.name : (lang === 'zh' ? '请选择' : 'Please select') }}</text>
          <u-icon name="arrow-right" size="28rpx" color="#909399"></u-icon>
        </view>
      </view>
      
      <!-- 显示选中的报名信息 -->
      <view class="selected-profile-info" v-if="selectedProfile">
        <view class="profile-detail-item">
          <text class="profile-detail-label">{{ lang === 'zh' ? '姓名：' : 'Name:' }}</text>
          <text class="profile-detail-value">{{ selectedProfile.name }}</text>
        </view>
        <view class="profile-detail-item">
          <text class="profile-detail-label">{{ lang === 'zh' ? '手机：' : 'Phone:' }}</text>
          <text class="profile-detail-value">{{ selectedProfile.phone }}</text>
        </view>
        <view class="profile-detail-item" v-if="selectedProfile.email">
          <text class="profile-detail-label">{{ lang === 'zh' ? '邮箱：' : 'Email:' }}</text>
          <text class="profile-detail-value">{{ selectedProfile.email }}</text>
        </view>
        <view class="profile-detail-item" v-if="selectedProfile.idCard">
          <text class="profile-detail-label">{{ lang === 'zh' ? '身份证：' : 'ID Card:' }}</text>
          <text class="profile-detail-value">{{ selectedProfile.idCard }}</text>
        </view>
        <view class="profile-detail-item" v-if="selectedProfile.address">
          <text class="profile-detail-label">{{ lang === 'zh' ? '地址：' : 'Address:' }}</text>
          <text class="profile-detail-value">{{ selectedProfile.address }}</text>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-bar" v-if="eventData.status !== 'ended'">
      <view class="bottom-price">
        <text class="price-label">{{ lang === 'zh' ? '费用：' : 'Fee: ' }}</text>
        <text class="price-value" v-if="eventData.fee > 0">¥{{ eventData.fee }}</text>
        <text class="price-value" v-else>{{ lang === 'zh' ? '免费' : 'Free' }}</text>
      </view>
      <view class="bottom-button" :class="{ disabled: !selectedProfile || eventData.status === 'full' }" @click="handleSignup">
        {{ lang === 'zh' ? '立即报名' : 'Sign Up' }}
      </view>
    </view>
    <!-- <view class="safe-area-inset-bottom"></view> -->
    
    <!-- 报名信息选择弹窗 -->
    <u-popup :show="showProfileSelector" mode="bottom" @close="showProfileSelector = false">
      <view class="popup-content">
        <view class="popup-header">
          <text>{{ lang === 'zh' ? '选择报名信息' : 'Select Profile' }}</text>
        </view>
        <scroll-view scroll-y class="profile-list">
          <view 
            v-for="(profile, index) in signupProfiles" 
            :key="index" 
            class="profile-item"
            :class="{ 'selected': selectedProfileIndex === index, 'default': profile.isDefault }"
            @click="selectProfile(index)"
          >
            <view class="profile-info">
              <text class="profile-name">{{ profile.name }}</text>
              <text class="profile-phone">{{ profile.phone }}</text>
            </view>
            <u-icon v-if="selectedProfileIndex === index" name="checkmark" color="#2979ff" size="40rpx"></u-icon>
          </view>
        </scroll-view>
        <view class="popup-buttons">
          <view class="cancel-button" @click="showProfileSelector = false">
            {{ lang === 'zh' ? '取消' : 'Cancel' }}
          </view>
          <view class="confirm-button" @click="confirmProfileSelection">
            {{ lang === 'zh' ? '确认' : 'Confirm' }}
          </view>
        </view>
      </view>
    </u-popup>
    
    <!-- 支付弹窗 -->
    <u-popup :show="showPayment" mode="bottom" @close="showPayment = false">
      <view class="popup-content">
        <view class="popup-header">
          <text>{{ lang === 'zh' ? '支付费用' : 'Payment' }}</text>
        </view>
        <view class="payment-info">
          <view class="payment-item">
            <text class="payment-label">{{ lang === 'zh' ? '活动名称：' : 'Event: ' }}</text>
            <text class="payment-value">{{ lang === 'zh' ? eventData.title : eventData.title_en }}</text>
          </view>
          <view class="payment-item">
            <text class="payment-label">{{ lang === 'zh' ? '报名人：' : 'Participant: ' }}</text>
            <text class="payment-value">{{ selectedProfile ? selectedProfile.name : '' }}</text>
          </view>
          <view class="payment-item">
            <text class="payment-label">{{ lang === 'zh' ? '应付金额：' : 'Amount: ' }}</text>
            <text class="payment-value payment-amount">¥{{ eventData.fee }}</text>
          </view>
        </view>
        <view class="payment-methods">
          <view class="payment-title">{{ lang === 'zh' ? '支付方式' : 'Payment Method' }}</view>
          <view 
            class="payment-method-item"
            :class="{ 'selected': paymentMethod === 'wechat' }"
            @click="paymentMethod = 'wechat'"
          >
            <view class="payment-method-icon wechat">
              <u-icon name="weixin-fill" color="#09BB07" size="40rpx"></u-icon>
            </view>
            <view class="payment-method-name">
              {{ lang === 'zh' ? '微信支付' : 'WeChat Pay' }}
            </view>
            <u-icon v-if="paymentMethod === 'wechat'" name="checkmark" color="#2979ff" size="40rpx"></u-icon>
          </view>
        </view>
        <view class="popup-buttons">
          <view class="cancel-button" @click="showPayment = false">
            {{ lang === 'zh' ? '取消' : 'Cancel' }}
          </view>
          <view class="confirm-button" @click="processPayment">
            {{ lang === 'zh' ? '确认支付' : 'Pay Now' }}
          </view>
        </view>
      </view>
    </u-popup>
    
    <!-- 支付成功弹窗 -->
    <u-popup :show="showPaymentSuccess" mode="center" @close="handlePaymentSuccessDone" :round="16" :safeAreaInsetBottom="false">
      <view class="success-popup">
        <u-icon name="checkmark-circle" color="#09BB07" size="120rpx"></u-icon>
        <text class="success-title">{{ lang === 'zh' ? '支付成功' : 'Payment Successful' }}</text>
        <text class="success-message">{{ lang === 'zh' ? '您已成功报名参加活动' : 'You have successfully signed up for the event' }}</text>
        <view class="success-button" @click="handlePaymentSuccessDone">
          {{ lang === 'zh' ? '完成' : 'Done' }}
        </view>
      </view>
    </u-popup>
    
    <!-- 语言切换按钮 -->
    <view class="lang-switch" @click="switchLanguage">
      <text>{{ lang === 'zh' ? 'EN' : '中' }}</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      lang: 'zh', // 默认语言
      eventId: null,
      eventData: {
        id: 1,
        title: '2023夏季编程训练营',
        title_en: '2023 Summer Coding Camp',
        images: [
          'https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
          'https://images.unsplash.com/photo-1531482615713-2afd69097998?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
          'https://images.unsplash.com/photo-1581472723648-909f4851d4ae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80'
        ],
        startTime: '2023-07-15 09:00',
        endTime: '2023-07-20 17:00',
        location: '深圳市南山区科技园',
        location_en: 'Nanshan District, Shenzhen',
        currentParticipants: 45,
        maxParticipants: 50,
        fee: 1980,
        points: 200,
        status: 'ongoing', // ongoing, coming, full, ended
        description: '本次训练营将带领学员深入学习人工智能和机器学习的核心概念和实践应用。课程包括Python编程基础、数据分析、机器学习算法以及深度学习入门等内容。学员将通过实际项目来巩固所学知识，提升编程能力和解决问题的能力。',
        description_en: 'This training camp will lead students to deeply learn the core concepts and practical applications of artificial intelligence and machine learning. The course includes Python programming basics, data analysis, machine learning algorithms, and an introduction to deep learning. Students will consolidate their knowledge and improve their programming and problem-solving abilities through practical projects.'
      },
      showProfileSelector: false,
      showPayment: false,
      showPaymentSuccess: false,
      selectedProfileIndex: -1,
      selectedProfile: null,
      paymentMethod: 'wechat',
      statusTextZh: {
        'ongoing': '进行中',
        'coming': '即将开始',
        'full': '已满员',
        'ended': '已结束'
      },
      statusTextEn: {
        'ongoing': 'Ongoing',
        'coming': 'Coming Soon',
        'full': 'Full',
        'ended': 'Ended'
      },
      signupProfiles: [
        {
          id: 1,
          name: '张三',
          phone: '138****1234',
          email: '<EMAIL>',
          idCard: '440******1234',
          address: '广东省深圳市南山区科技园',
          isDefault: true
        },
        {
          id: 2,
          name: '李四',
          phone: '139****5678',
          email: '<EMAIL>',
          idCard: '440******5678',
          address: '广东省深圳市福田区中心区',
          isDefault: false
        },
        {
          id: 3,
          name: 'John Doe',
          phone: '135****9012',
          email: '<EMAIL>',
          idCard: '440******9012',
          address: 'Nanshan District, Shenzhen',
          isDefault: false
        }
      ]
    };
  },
  onLoad(options) {
    // 从全局获取语言设置
    if (getApp().globalData.lang) {
      this.lang = getApp().globalData.lang;
    }
    
    // 监听语言变化
    uni.$on('languageChanged', (data) => {
      this.lang = data.lang;
      this.setNavigationBarTitle();
    });
    
    // 获取活动ID
    if (options.id) {
      this.eventId = options.id;
      // 实际应用中，这里应该通过API获取活动详情
      // this.fetchEventDetail(this.eventId);
    }
    
    // 设置默认选中的报名信息
    this.setDefaultProfile();
    
    // 设置导航栏标题
    this.setNavigationBarTitle();
  },
  onUnload() {
    // 取消监听语言变化
    uni.$off('languageChanged');
  },
  methods: {
    setNavigationBarTitle() {
      uni.setNavigationBarTitle({
        title: this.lang === 'zh' ? '活动详情' : 'Event Details'
      });
    },
    setDefaultProfile() {
      // 设置默认选中的报名信息
      const defaultIndex = this.signupProfiles.findIndex(profile => profile.isDefault);
      if (defaultIndex !== -1) {
        this.selectedProfileIndex = defaultIndex;
        this.selectedProfile = this.signupProfiles[defaultIndex];
      }
    },
    selectProfile(index) {
      this.selectedProfileIndex = index;
    },
    confirmProfileSelection() {
      if (this.selectedProfileIndex !== -1) {
        this.selectedProfile = this.signupProfiles[this.selectedProfileIndex];
      }
      this.showProfileSelector = false;
    },
    handleSignup() {
      // 检查是否已选择报名信息
      if (!this.selectedProfile) {
        uni.showToast({
          title: this.lang === 'zh' ? '请选择报名信息' : 'Please select a profile',
          icon: 'none'
        });
        return;
      }
      
      // 检查活动状态
      if (this.eventData.status === 'full') {
        uni.showToast({
          title: this.lang === 'zh' ? '活动已满员' : 'Event is full',
          icon: 'none'
        });
        return;
      }
      
      // 如果活动免费，直接完成报名
      if (this.eventData.fee <= 0) {
        this.showPaymentSuccess = true;
      } else {
        // 否则显示支付弹窗
        this.showPayment = true;
      }
    },
    processPayment() {
      // 模拟支付过程
      uni.showLoading({
        title: this.lang === 'zh' ? '处理中...' : 'Processing...'
      });
      
      // 延迟2秒模拟支付过程
      setTimeout(() => {
        uni.hideLoading();
        this.showPayment = false;
        this.showPaymentSuccess = true;
      }, 2000);
    },
    handlePaymentSuccessDone() {
      this.showPaymentSuccess = false;
      // 返回上一页或跳转到我的活动页面
      uni.navigateTo({
        url: '/pages/my-join/index'
      });
    },
    fetchEventDetail(id) {
      // 实际应用中，这里应该通过API获取活动详情
      // 这里仅作为示例，使用静态数据
    },
    switchLanguage() {
      const newLang = this.lang === 'zh' ? 'en' : 'zh';
      this.lang = newLang;
      
      // 更新全局语言设置
      if (getApp().globalData) {
        getApp().globalData.lang = newLang;
      }
      
      // 发送语言变化事件
      uni.$emit('languageChanged', { lang: newLang });
      
      // 设置导航栏标题
      this.setNavigationBarTitle();
    }
  }
};
</script>

<style lang="scss" scoped>
.event-detail {
  padding-bottom: calc(120rpx + constant(safe-area-inset-bottom)); /* iOS 11.0 */
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom)); /* iOS 11.2+ */
  background-color: #f5f5f5;
}

.event-swiper {
  width: 100%;
  height: 450rpx;
}

.event-image {
  width: 100%;
  height: 100%;
}

.event-header {
  padding: 30rpx;
  background-color: #ffffff;
}

.event-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #303133;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.event-status {
  display: inline-block;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #ffffff;
  
  &.ongoing {
    background-color: #2979ff;
  }
  
  &.coming {
    background-color: #ff9900;
  }
  
  &.full {
    background-color: #909399;
  }
  
  &.ended {
    background-color: #c0c4cc;
  }
}

.info-card {
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-header {
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f2f2f2;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  position: relative;
  padding-left: 20rpx;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 8rpx;
    bottom: 8rpx;
    width: 8rpx;
    background-color: #2979ff;
    border-radius: 4rpx;
  }
}

.info-item {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
  
  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  margin-left: 16rpx;
  color: #606266;
  font-size: 28rpx;
  width: 120rpx;
}

.info-content {
  flex: 1;
  font-size: 28rpx;
  color: #303133;
}

.event-description {
  padding: 20rpx 30rpx 30rpx;
  font-size: 28rpx;
  color: #606266;
  line-height: 1.6;
}

.select-profile {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx 30rpx;
}

.select-label {
  font-size: 28rpx;
  color: #606266;
}

.profile-selector {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  margin-left: 20rpx;
  
  text {
    font-size: 28rpx;
    color: #303133;
  }
}

.selected-profile-info {
  padding: 20rpx 30rpx 30rpx;
  border-top: 1rpx solid #f8f8f8;
}

.profile-detail-item {
  display: flex;
  margin-bottom: 16rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.profile-detail-label {
  font-size: 28rpx;
  color: #606266;
  width: 120rpx;
}

.profile-detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #303133;
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */
  padding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */
}

.safe-area-inset-bottom {
  height: constant(safe-area-inset-bottom); /* iOS 11.0 */
  height: env(safe-area-inset-bottom); /* iOS 11.2+ */
}

.bottom-price {
  flex: 1;
}

.price-label {
  font-size: 28rpx;
  color: #606266;
}

.price-value {
  font-size: 36rpx;
  color: #ff5722;
  font-weight: bold;
}

.bottom-button {
  width: 240rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  background-color: #2979ff;
  color: #ffffff;
  border-radius: 35rpx;
  font-size: 30rpx;
  
  &.disabled {
    background-color: #a0cfff;
  }
}

.popup-content {
  padding: 30rpx;
}

.popup-header {
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 30rpx;
}

.profile-list {
  max-height: 600rpx;
}

.profile-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f2f2f2;
  
  &.selected {
    background-color: #f5f7fa;
  }
  
  &.default {
    border: 2rpx solid #2979ff;
    border-radius: 8rpx;
    margin-bottom: 10rpx;
  }
}

.profile-info {
  display: flex;
  flex-direction: column;
}

.profile-name {
  font-size: 28rpx;
  color: #303133;
  margin-bottom: 8rpx;
}

.profile-phone {
  font-size: 24rpx;
  color: #909399;
}

.popup-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}

.cancel-button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #f2f2f2;
  color: #606266;
  border-radius: 8rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
}

.confirm-button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #2979ff;
  color: #ffffff;
  border-radius: 8rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}

.payment-info {
  margin-bottom: 30rpx;
}

.payment-item {
  display: flex;
  margin-bottom: 16rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.payment-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #606266;
}

.payment-value {
  flex: 1;
  font-size: 28rpx;
  color: #303133;
  
  &.payment-amount {
    color: #ff5722;
    font-weight: bold;
    font-size: 32rpx;
  }
}

.payment-methods {
  margin-bottom: 30rpx;
}

.payment-title {
  font-size: 28rpx;
  color: #606266;
  margin-bottom: 20rpx;
}

.payment-method-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border: 1rpx solid #f2f2f2;
  border-radius: 8rpx;
  
  &.selected {
    border-color: #2979ff;
    background-color: #f5f7fa;
  }
}

.payment-method-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.payment-method-name {
  flex: 1;
  font-size: 28rpx;
  color: #303133;
}

.success-popup {
  width: 500rpx;
  padding: 50rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #303133;
  margin: 30rpx 0 20rpx;
}

.success-message {
  font-size: 28rpx;
  color: #606266;
  margin-bottom: 40rpx;
  text-align: center;
}

.success-button {
  width: 60%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #2979ff;
  color: #ffffff;
  border-radius: 40rpx;
  font-size: 30rpx;
}

.lang-switch {
  position: fixed;
  right: 30rpx;
  top: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  z-index: 10;
}
</style> 