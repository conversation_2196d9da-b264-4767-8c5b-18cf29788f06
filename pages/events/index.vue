<template>
  <view class="events-page">
    <view class="main-content">
      <!-- 左侧分类导航，独立滚动 -->
      <scroll-view class="sidebar-scroll" scroll-y :show-scrollbar="true" :style="{ height: contentHeight + 'px' }">
        <view class="sidebar">
          <view v-for="(item, idx) in categoryList" :key="item.id"
            :class="['sidebar-item', idx === currentCategory ? 'active' : '']" @click="onCategoryClick(idx)">
            <text class="sidebar-title">{{ lang === 'zh' ? item.name : item.name_en }}</text>
          </view>
        </view>
      </scroll-view>
      <!-- 右侧内容区，独立滚动 -->
      <scroll-view class="activity-panel-scroll" scroll-y :show-scrollbar="true"
        :style="{ height: contentHeight + 'px' }">
        <view class="activity-panel">
          <view class="search-bar-fix">
            <u-search :placeholder="lang === 'zh' ? '搜索活动' : 'Search Events'" v-model="searchValue" @search="onSearch"
              :show-action="false" />
          </view>
          <!-- <view class="sort-tabs">
            <u-tabs :list="sortTabs" :current="currentSort" @change="onSortChange" lineColor="#2979ff" />
          </view> -->
          <view class="activity-list">
            <view v-for="item in filteredActivities" :key="item.id" class="activity-card"
              @click="navigateToEventDetail(item)">
              <u-image :src="item.img" width="100%" height="200rpx" border-radius="16rpx 16rpx 0 0" />
              <view class="card-content">
                <view class="card-title">{{ lang === 'zh' ? item.title : (item.title_en || item.title) }}</view>
                <view class="card-subtitle">{{ item.time }}</view>
                <view class="card-desc">{{ lang === 'zh' ? item.desc : (item.desc_en || item.desc) }}</view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    <view class="custom-tabbar">
      <CustomTabbar :current="1" />
    </view>
    <LangSwitch />
  </view>
</template>

<script>
import CustomTabbar from '../../components/CustomTabbar.vue'
import LangSwitch from '../../components/LangSwitch.vue'
export default {
  components: { CustomTabbar, LangSwitch },
  data() {
    return {
      lang: uni.getStorageSync('lang') || 'zh',
      searchValue: '',
      currentCategory: 0,
      currentSort: 0,
      contentHeight: 0, // 动态内容区高度
      categoryList: [
        { id: 0, name: '全部', name_en: 'All' },
        { id: 1, name: '体育', name_en: 'Sports' },
        { id: 2, name: '艺术', name_en: 'Art' },
        { id: 3, name: '科技', name_en: 'Tech' },
        { id: 4, name: '户外', name_en: 'Outdoor' },
        { id: 5, name: '体育', name_en: 'Sports' },
        { id: 6, name: '艺术', name_en: 'Art' },
        { id: 7, name: '科技', name_en: 'Tech' },
        { id: 8, name: '户外', name_en: 'Outdoor' },
        { id: 9, name: '体育', name_en: 'Sports' },
        { id: 10, name: '艺2222术', name_en: 'Art' },
        { id: 11, name: '科技', name_en: 'Tech' },
        { id: 12, name: '户外', name_en: 'Outdoor' },
        { id: 13, name: '体育', name_en: 'Sports' },
        { id: 14, name: '艺术', name_en: 'Art' },
        { id: 15, name: '科技', name_en: 'Tech' },
        { id: 16, name: '户外', name_en: 'Outdoor' },
        { id: 17, name: '体育', name_en: 'Sports' },
        { id: 18, name: '艺术', name_en: 'Art' },
        { id: 19, name: '科技', name_en: 'Tech' },
        { id: 20, name: '户外', name_en: 'Outdoor' },
        { id: 21, name: '体育', name_en: 'Sports' },
        { id: 22, name: '艺术', name_en: 'Art' },
        { id: 23, name: '科技', name_en: 'Tech' },
        { id: 24, name: '户fff外', name_en: 'Outdoor' },
      ],
      activityList: [
        { id: 1, category: 1, title: '篮球训练营', title_en: 'Basketball Training Camp', time: '2024-07-10', img: 'https://picsum.photos/800/600?random=31', desc: '专业教练指导，提升篮球技能。', desc_en: 'Professional coaching to improve basketball skills.', top: true, views: 120 },
        { id: 2, category: 2, title: '少儿美术班', title_en: 'Children\'s Art Class', time: '2024-07-15', img: 'https://picsum.photos/800/600?random=32', desc: '培养孩子艺术素养，激发创造力。', desc_en: 'Cultivate children\'s artistic literacy and inspire creativity.', top: false, views: 80 },
        { id: 3, category: 3, title: '机器人编程', title_en: 'Robot Programming', time: '2024-07-20', img: 'https://picsum.photos/800/600?random=33', desc: '动手实践，启发科技兴趣。', desc_en: 'Hands-on practice to inspire interest in technology.', top: false, views: 150 },
        { id: 4, category: 4, title: '户外拓展营', title_en: 'Outdoor Development Camp', time: '2024-07-25', img: 'https://picsum.photos/800/600?random=34', desc: '团队协作，挑战自我。', desc_en: 'Team collaboration and self-challenge.', top: true, views: 60 },
        { id: 5, category: 1, title: '足球训练营', title_en: 'Football Training Camp', time: '2024-08-01', img: 'https://picsum.photos/800/600?random=35', desc: '专业足球教练，快乐成长。', desc_en: 'Professional football coaching for happy growth.', top: false, views: 200 }
      ]
    }
  },
  computed: {
    filteredActivities() {
      // 分类过滤
      let list = this.currentCategory === 0 ? this.activityList : this.activityList.filter(item => item.category === this.currentCategory)
      // 搜索过滤
      if (this.searchValue) {
        const val = this.searchValue.toLowerCase()
        list = list.filter(item => item.title.toLowerCase().includes(val) || item.desc.toLowerCase().includes(val))
      }
      // 排序
      if (this.currentSort === 0) {
        // 推荐优先（top=true）
        list = list.slice().sort((a, b) => (b.top === true) - (a.top === true))
      } else if (this.currentSort === 1) {
        // 时间倒序
        list = list.slice().sort((a, b) => new Date(b.time) - new Date(a.time))
      } else if (this.currentSort === 2) {
        // 点击量倒序
        list = list.slice().sort((a, b) => b.views - a.views)
      }
      return list
    }
  },
  onLoad(options) {
    console.log(options)
    // 从全局获取语言设置
    if (getApp().globalData && getApp().globalData.lang) {
      this.lang = getApp().globalData.lang;
    } else {
      this.lang = uni.getStorageSync('lang') || 'zh';
    }

    // 监听语言变化
    uni.$on('lang-change', (newLang) => {
      this.lang = newLang;
      this.setNavigationBarTitle();
      this.onLangChange(newLang);
    });

    // 设置导航栏标题
    this.setNavigationBarTitle();

    // 计算滚动区域高度
    setTimeout(() => {
      this.calcContentHeight();
    }, 500);
  },
  onShow() {
    // 检查是否有从首页传递过来的分类选择
    let currentCategory = 0
    // 从本地存储获取
    const storedCategory = uni.getStorageSync('selectedCategory');
    if (storedCategory !== '' && storedCategory !== undefined) {
      currentCategory = storedCategory;
      // 使用后清除
      uni.removeStorageSync('selectedCategory');
      // 如果找到有效的分类索引，则设置当前分类
      if (currentCategory !== -1 && currentCategory >= 0) {
        this.currentCategory = currentCategory;
      }
    }
  },
  onUnload() {
    // 取消监听语言变化
    uni.$off('languageChanged');
    uni.$off('lang-change');
  },
  methods: {
    calcContentHeight() {
      const sys = uni.getSystemInfoSync()
      console.log(sys)
      // 获取tabbar实际高度
      uni.createSelectorQuery().select('.custom-tabbar').boundingClientRect(rect => {
        console.log("大考方便", rect)
        let tabbarPx = rect ? rect.height : 0
        this.contentHeight = sys.windowHeight - tabbarPx
      }).exec()
    },
    onCategoryClick(idx) {
      this.currentCategory = idx
    },
    onSortChange(idx) {
      this.currentSort = idx
    },
    onSearch(val) {
      this.searchValue = val
    },
    navigateToEventDetail(item) {
      uni.navigateTo({
        url: `/pages/events/detail?id=${item.id}`
      });
    },
    onLangChange(newLang) {
      this.lang = newLang
      this.setNavigationBarTitle()
      // 切换语言时，不需要重新创建categoryList，因为每个项目已经包含了中英文
      // 只需要刷新界面，Vue的响应式会自动更新显示
    },
    setNavigationBarTitle() {
      uni.setNavigationBarTitle({
        title: this.lang === 'zh' ? '活动列表' : 'Events'
      });
    }
  }
}
</script>

<style scoped>
.events-page {
  min-height: 100vh;
  box-sizing: border-box;
}

.main-content {
  display: flex;
  flex-direction: row;
}

.sidebar-scroll {
  width: 160rpx;
  background: #fff;
  border-radius: 0 24rpx 24rpx 0;
  box-shadow: 2rpx 0 8rpx rgba(0, 0, 0, 0.03);
  padding: 0;
}

.sidebar {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 0;
}

.sidebar-item {
  width: 100%;
  padding: 24rpx 0;
  text-align: center;
  cursor: pointer;
  border-left: 8rpx solid transparent;
  transition: border-color 0.2s;
}

.sidebar-item.active {
  background: #f0f7ff;
  border-left: 8rpx solid #2979ff;
}

.sidebar-title {
  font-size: 24rpx;
  color: #333;
}

.activity-panel-scroll {
  flex: 1;
  background: transparent;
}

.activity-panel {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.search-bar-fix {
  padding: 24rpx;
  box-sizing: border-box;
}

.sort-tabs {
  margin-bottom: 16rpx;
  background: #f7f8fa;
}

.activity-list {
  flex: 1;
  padding: 0 24rpx 24rpx 24rpx;
  box-sizing: border-box;
}

.activity-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.activity-card:not(:first-child) {
  margin-top: 24rpx;
}

.card-content {
  padding: 24rpx;
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-subtitle {
  font-size: 24rpx;
  color: #909399;
  margin-top: 8rpx;
}

.card-desc {
  font-size: 26rpx;
  color: #606266;
  margin-top: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
</style>