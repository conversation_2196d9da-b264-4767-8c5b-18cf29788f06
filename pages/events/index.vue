<template>
  <view class="events-page">
    <view class="main-content">
      <!-- 左侧分类导航，独立滚动 -->
      <scroll-view class="sidebar-scroll" scroll-y :show-scrollbar="true" :style="{ height: contentHeight + 'px' }">
        <view class="sidebar">
          <view v-for="(item, idx) in categoryList" :key="item.id"
            :class="['sidebar-item', idx === currentCategory ? 'active' : '']" @click="onCategoryClick(idx)">
            <text class="sidebar-title">{{ lang === 'zh' ? item.name : item.name_en }}</text>
          </view>
        </view>
      </scroll-view>
      <!-- 右侧内容区，独立滚动 -->
      <scroll-view class="activity-panel-scroll" scroll-y :show-scrollbar="true"
        :style="{ height: contentHeight + 'px' }">
        <view class="activity-panel">
          <view class="search-bar-fix">
            <u-search :placeholder="lang === 'zh' ? '搜索活动' : 'Search Events'" v-model="searchValue" @search="onSearch"
              :show-action="false" />
          </view>
          <!-- <view class="sort-tabs">
            <u-tabs :list="sortTabs" :current="currentSort" @change="onSortChange" lineColor="#2979ff" />
          </view> -->
          <view class="activity-list">
            <view v-for="item in filteredActivities" :key="item.id" class="activity-card"
              @click="navigateToEventDetail(item)">
              <u-image :src="item.img" width="100%" height="200rpx" border-radius="16rpx 16rpx 0 0" />
              <view class="card-content">
                <view class="card-title">{{ lang === 'zh' ? item.title : (item.titleEN || item.title) }}</view>
                <view class="card-subtitle">
                  <text>{{ item.beginTime ? item.beginTime.split(' ')[0] : '' }}</text>
                  <text v-if="item.endTime"> - {{ item.endTime.split(' ')[0] }}</text>
                </view>
                <view class="card-desc">{{ lang === 'zh' ? item.contents : (item.contentsEN || item.contents) }}</view>
                <view class="card-info">
                  <text class="price">¥{{ item.price }}</text>
                  <text class="nums">{{ item.regNums }}/{{ item.nums }}人</text>
                </view>
              </view>
            </view>

            <!-- 加载更多 -->
            <u-loadmore
              :status="loadMoreStatus"
              :loading-text="lang === 'zh' ? '正在加载...' : 'Loading...'"
              :loadmore-text="lang === 'zh' ? '点击加载更多' : 'Load more'"
              :nomore-text="lang === 'zh' ? '没有更多了' : 'No more data'"
              @loadmore="loadMore"
              margin-top="20"
              margin-bottom="20"
              v-if="activityList.length > 0"
            ></u-loadmore>
          </view>
        </view>
      </scroll-view>
    </view>
    <view class="custom-tabbar">
      <CustomTabbar :current="1" />
    </view>
    <LangSwitch />
  </view>
</template>

<script>
import CustomTabbar from '../../components/CustomTabbar.vue'
import LangSwitch from '../../components/LangSwitch.vue'
import { getOperationList, getCategoryList } from '@/api/user.js'
import { formatImageUrl } from '@/utils/http.js'
export default {
  components: { CustomTabbar, LangSwitch },
  data() {
    return {
      lang: uni.getStorageSync('lang') || 'zh',
      searchValue: '',
      currentCategory: 0,
      currentSort: 0,
      contentHeight: 0, // 动态内容区高度
      categoryList: [
        { id: 0, name: '全部', name_en: 'All' } // 默认的"全部"分类
      ],
      activityList: [],
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      hasMore: true,
      loadMoreStatus: 'loadmore'
    }
  },
  computed: {
    filteredActivities() {
      // 现在搜索和分类筛选都通过API完成，这里只需要返回活动列表
      // 可以在这里添加前端排序逻辑
      let list = [...this.activityList];

      // 排序（如果需要的话）
      if (this.currentSort === 0) {
        // 按状态排序（可用的优先）
        list = list.slice().sort((a, b) => b.state - a.state);
      } else if (this.currentSort === 1) {
        // 按时间倒序
        list = list.slice().sort((a, b) => new Date(b.beginTime) - new Date(a.beginTime));
      } else if (this.currentSort === 2) {
        // 按价格排序
        list = list.slice().sort((a, b) => a.price - b.price);
      }

      return list;
    }
  },
  onLoad(options) {
    console.log(options)
    // 从全局获取语言设置
    if (getApp().globalData && getApp().globalData.lang) {
      this.lang = getApp().globalData.lang;
    } else {
      this.lang = uni.getStorageSync('lang') || 'zh';
    }

    // 监听语言变化
    uni.$on('lang-change', (newLang) => {
      this.lang = newLang;
      this.setNavigationBarTitle();
      this.onLangChange(newLang);
    });

    // 设置导航栏标题
    this.setNavigationBarTitle();

    // 计算滚动区域高度
    setTimeout(() => {
      this.calcContentHeight();
    }, 500);

    // 获取分类列表
    this.getCategoryList();

    // 获取活动列表
    this.getActivityList();
  },
  onShow() {
    // 检查是否有从首页传递过来的分类选择
    let currentCategory = 0
    // 从本地存储获取
    const storedCategory = uni.getStorageSync('selectedCategory');
    if (storedCategory !== '' && storedCategory !== undefined) {
      currentCategory = storedCategory;
      // 使用后清除
      uni.removeStorageSync('selectedCategory');
      // 如果找到有效的分类索引，则设置当前分类
      if (currentCategory !== -1 && currentCategory >= 0) {
        this.currentCategory = currentCategory;
      }
    }
  },
  onUnload() {
    // 取消监听语言变化
    uni.$off('languageChanged');
    uni.$off('lang-change');
  },
  methods: {
    calcContentHeight() {
      const sys = uni.getSystemInfoSync()
      console.log(sys)
      // 获取tabbar实际高度
      uni.createSelectorQuery().select('.custom-tabbar').boundingClientRect(rect => {
        console.log("大考方便", rect)
        let tabbarPx = rect ? rect.height : 0
        this.contentHeight = sys.windowHeight - tabbarPx
      }).exec()
    },
    onCategoryClick(idx) {
      this.currentCategory = idx;
      // 切换分类时重新获取数据
      this.getActivityList(true);
    },
    onSortChange(idx) {
      this.currentSort = idx
    },
    onSearch(val) {
      this.searchValue = val;
      // 搜索时重新获取数据
      this.getActivityList(true);
    },
    navigateToEventDetail(item) {
      uni.navigateTo({
        url: `/pages/events/detail?id=${item.id}`
      });
    },
    onLangChange(newLang) {
      this.lang = newLang;
      this.setNavigationBarTitle();
      // 切换语言时，如果有搜索关键词，需要重新获取数据（因为搜索字段不同）
      if (this.searchValue && this.searchValue.trim()) {
        this.getActivityList(true);
      }
    },
    setNavigationBarTitle() {
      uni.setNavigationBarTitle({
        title: this.lang === 'zh' ? '活动列表' : 'Events'
      });
    },

    // 获取分类列表
    async getCategoryList() {
      try {
        const response = await getCategoryList({});

        console.log('获取分类列表响应:', response);

        if (response && response.code === 0 && response.data) {
          // 将API返回的分类数据转换为页面需要的格式
          const categories = response.data.map(item => ({
            id: item.id,
            name: item.name || '',
            name_en: item.nameEN || item.name || '',
            types: item.types,
            logo: formatImageUrl(item.logo || ''),
            sort: item.sort || 0
          }));

          // 在分类列表前面添加"全部"选项
          this.categoryList = [
            { id: 0, name: '全部', name_en: 'All', types: 0 },
            ...categories
          ];
        } else {
          console.error('获取分类列表失败:', response);
        }
      } catch (error) {
        console.error('获取分类列表异常:', error);
      }
    },

    // 获取活动列表
    async getActivityList(isRefresh = true) {
      try {
        if (isRefresh) {
          this.currentPage = 1;
          this.activityList = [];
          uni.showLoading({
            title: this.lang === 'zh' ? '加载中...' : 'Loading...'
          });
        } else {
          this.loadMoreStatus = 'loading';
        }

        // 准备API参数
        const apiParams = {
          pg: this.currentPage,
          size: this.pageSize
        };

        // 如果选择了具体分类（不是"全部"），添加分类参数
        if (this.currentCategory > 0 && this.categoryList[this.currentCategory]) {
          const selectedCategory = this.categoryList[this.currentCategory];
          // 只有当分类ID不为0时才传递分类参数（0表示"全部"）
          if (selectedCategory.id !== 0) {
            apiParams.types = selectedCategory.types || selectedCategory.id;
          }
        }

        // 如果有搜索关键词，添加搜索参数
        if (this.searchValue && this.searchValue.trim()) {
          // 根据当前语言选择搜索字段
          if (this.lang === 'zh') {
            apiParams.title = this.searchValue.trim();
          } else {
            apiParams.titleEN = this.searchValue.trim();
          }
        }

        const response = await getOperationList(apiParams);

        console.log('获取活动列表响应:', response);

        if (response && response.code === 0 && response.data) {
          // 将API返回的数据转换为页面需要的格式
          const newList = response.data.map(item => ({
            id: item.id,
            types: item.types, // 分类ID
            title: item.title || '',
            titleEN: item.titleEN || '',
            beginTime: item.beginTime || '',
            endTime: item.endTime || '',
            img: formatImageUrl(item.img || ''),
            contents: item.contents || '',
            contentsEN: item.contentsEN || '',
            address: item.address || '',
            addressEN: item.addressEN || '',
            price: item.price || 0,
            nums: item.nums || 0,
            regNums: item.regNums || 0,
            integral: item.integral || 0,
            state: item.state,
            typesName: item.typesName || ''
          }));

          if (isRefresh) {
            this.activityList = newList;
          } else {
            this.activityList = [...this.activityList, ...newList];
          }

          this.total = response.count || 0;

          // 判断是否还有更多数据
          if (newList.length < this.pageSize) {
            this.loadMoreStatus = 'nomore';
            this.hasMore = false;
          } else {
            this.loadMoreStatus = 'loadmore';
            this.hasMore = true;
          }
        } else {
          console.error('获取活动列表失败:', response);
          uni.showToast({
            title: this.lang === 'zh' ? '获取数据失败' : 'Failed to load data',
            icon: 'none'
          });
          this.loadMoreStatus = 'loadmore';
        }
      } catch (error) {
        console.error('获取活动列表异常:', error);
        uni.showToast({
          title: this.lang === 'zh' ? '网络错误' : 'Network error',
          icon: 'none'
        });
        this.loadMoreStatus = 'loadmore';
      } finally {
        if (isRefresh) {
          uni.hideLoading();
        }
      }
    },

    // 加载更多
    async loadMore() {
      if (this.loadMoreStatus === 'loading' || !this.hasMore) return;

      this.currentPage++;
      await this.getActivityList(false);
    }
  }
}
</script>

<style scoped>
.events-page {
  min-height: 100vh;
  box-sizing: border-box;
}

.main-content {
  display: flex;
  flex-direction: row;
}

.sidebar-scroll {
  width: 160rpx;
  background: #fff;
  border-radius: 0 24rpx 24rpx 0;
  box-shadow: 2rpx 0 8rpx rgba(0, 0, 0, 0.03);
  padding: 0;
}

.sidebar {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 0;
}

.sidebar-item {
  width: 100%;
  padding: 24rpx 0;
  text-align: center;
  cursor: pointer;
  border-left: 8rpx solid transparent;
  transition: border-color 0.2s;
}

.sidebar-item.active {
  background: #f0f7ff;
  border-left: 8rpx solid #2979ff;
}

.sidebar-title {
  font-size: 24rpx;
  color: #333;
}

.activity-panel-scroll {
  flex: 1;
  background: transparent;
}

.activity-panel {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.search-bar-fix {
  padding: 24rpx;
  box-sizing: border-box;
}

.sort-tabs {
  margin-bottom: 16rpx;
  background: #f7f8fa;
}

.activity-list {
  flex: 1;
  padding: 0 24rpx 24rpx 24rpx;
  box-sizing: border-box;
}

.activity-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.activity-card:not(:first-child) {
  margin-top: 24rpx;
}

.card-content {
  padding: 24rpx;
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-subtitle {
  font-size: 24rpx;
  color: #909399;
  margin-top: 8rpx;
}

.card-desc {
  font-size: 26rpx;
  color: #606266;
  margin-top: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.card-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #eee;
}

.price {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff5252;
}

.nums {
  font-size: 24rpx;
  color: #909399;
}
</style>