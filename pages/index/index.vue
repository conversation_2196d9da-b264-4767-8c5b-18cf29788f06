<template>
  <view class="index-page">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <u-search :placeholder="lang === 'zh' ? '搜索往期风采' : 'Search past events'" v-model="searchValue" @search="onSearch" :show-action="false" />
    </view>

    <!-- Banner轮播 -->
    <view class="banner-swiper">
      <u-swiper :list="bannerList" height="300rpx" border-radius="16rpx" indicator indicator-active-color="#2979ff" />
    </view>

    <!-- 活动分类宫格 -->
    <u-grid :col="4" :border="false" class="category-grid">
      <u-grid-item v-for="(item, idx) in categoryList" :key="item.name" @click="navigateToCategory(idx)">
        <u-image :src="item.image" width="96rpx" height="96rpx" shape="circle" />
        <text class="cat-title">{{ lang === 'zh' ? item.name : item.name_en }}</text>
      </u-grid-item>
    </u-grid>

    <!-- 推荐活动 -->
    <view class="section-title">{{ lang === 'zh' ? '推荐活动' : 'Recommended' }}</view>
    <view class="activity-list">
      <view v-for="item in activityList" :key="item.id" class="activity-card" @click="navigateToEventDetail(item.id)">
        <u-image :src="item.img" width="100%" height="250rpx" border-radius="16rpx 16rpx 0 0" />
        <view class="card-content">
          <view class="card-title">{{ item.title }}</view>
          <view class="card-subtitle">{{ item.time }}</view>
          <view class="card-desc">{{ item.desc }}</view>
        </view>
      </view>
    </view>

    <!-- 往期风采 -->
    <view class="section-title">{{ lang === 'zh' ? '往期风采' : 'Past Events' }}</view>
    <view class="activity-list">
      <view v-for="item in pastList" :key="item.id" class="activity-card" @click="navigateToPastEventDetail(item.id)">
        <u-image :src="item.img" width="100%" height="250rpx" border-radius="16rpx 16rpx 0 0" />
        <view class="card-content">
          <view class="card-title">{{ item.title }}</view>
          <view class="card-desc">{{ item.desc }}</view>
          <view class="card-footer">
            <view class="card-date">
              <u-icon name="calendar" size="24rpx" color="#909399"></u-icon>
              <text>{{ item.date }}</text>
            </view>
            <view class="card-views">
              <u-icon name="eye" size="24rpx" color="#909399"></u-icon>
              <text>{{ item.views }}</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 使用 u-loadmore 组件 -->
      <u-loadmore 
        :status="loadMoreStatus" 
        :loading-text="lang === 'zh' ? '正在加载...' : 'Loading...'" 
        :loadmore-text="lang === 'zh' ? '点击加载更多' : 'Load more'" 
        :nomore-text="lang === 'zh' ? '没有更多了' : 'No more data'"
        @loadmore="loadMorePast"
        margin-top="20"
        margin-bottom="20"
      ></u-loadmore>
    </view>

    <!-- 全屏弹窗 -->
    <u-popup :safeAreaInsetBottom="false" round="16" :show="showPopup" mode="center" :mask-close-able="true" @close="showPopup = false">
      <view class="poster-popup">
        <u-image 
          :src="popupImage || 'https://picsum.photos/100/100?random=10'" 
          width="500rpx" 
          height="750rpx" 
          :fade="true" 
          duration="450" 
          @click="onPopupClick"
        ></u-image>
      </view>
    </u-popup>

    <CustomTabbar :current="0" />
    <LangSwitch />
  </view>
</template>

<script>
import CustomTabbar from '@/components/CustomTabbar.vue'
import LangSwitch from '@/components/LangSwitch.vue'
import { weChatLogin } from '@/api/weChat.js'
import { postHomePage, postWindowAdvert } from '@/api/apiApplet.js'
import { formatImageUrl } from '@/utils/http.js'
export default {
  components: { CustomTabbar, LangSwitch },
  data() {
    return {
      lang: uni.getStorageSync('lang') || 'zh',
      searchValue: '',
      showPopup: false, // 改为默认不显示
      popupImage: '', // 弹窗图片URL
      popupLink: '', // 弹窗点击跳转链接
      bannerList: [
        'https://picsum.photos/800/600?random=1',
        'https://picsum.photos/800/600?random=2',
        'https://picsum.photos/800/600?random=3'
      ],
      categoryList: [
        { name: '体育', name_en: 'Sports', image: 'https://picsum.photos/100/100?random=10' },
        { name: '艺术', name_en: 'Art', image: 'https://picsum.photos/100/100?random=11' },
        { name: '科技', name_en: 'Tech', image: 'https://picsum.photos/100/100?random=12' },
        { name: '户外', name_en: 'Outdoor', image: 'https://picsum.photos/100/100?random=13' },
      ],
      activityList: [
        { id: 1, title: '篮球训练营', time: '2024-07-10', img: 'https://picsum.photos/800/600?random=4', desc: '专业教练指导，提升篮球技能。' },
        { id: 2, title: '少儿美术班', time: '2024-07-15', img: 'https://picsum.photos/800/600?random=5', desc: '培养孩子艺术素养，激发创造力。' }
      ],
      pastList: [
        { 
          id: 1, 
          title: '2023夏令营精彩回顾', 
          img: 'https://images.unsplash.com/photo-1530124566582-a618bc2615dc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', 
          desc: '2023年夏令营圆满结束，120名学员参与，收获满满。',
          date: '2023-09-01',
          views: 1286
        },
        { 
          id: 2, 
          title: '2022冬令营精彩瞬间', 
          img: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', 
          desc: '2022冬令营活动丰富，学员们在学习中收获快乐。',
          date: '2022-12-28',
          views: 962
        }
      ],
      // 额外的往期风采数据，用于模拟加载更多
      pastListMore: [
        { 
          id: 3, 
          title: '2021青少年科技展回顾', 
          img: 'https://images.unsplash.com/photo-1581472723648-909f4851d4ae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', 
          desc: '青少年科技展展示了学生们的创新能力和科学素养。',
          date: '2021-11-15',
          views: 754
        },
        { 
          id: 4, 
          title: '2021春季音乐会回顾', 
          img: 'https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', 
          desc: '春季音乐会上，学生们展示了精彩的音乐才艺。',
          date: '2021-04-20',
          views: 683
        },
        { 
          id: 5, 
          title: '2020冬季艺术节回顾', 
          img: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', 
          desc: '冬季艺术节展示了学生们在艺术领域的才华和创意。',
          date: '2020-12-15',
          views: 592
        },
        { 
          id: 6, 
          title: '2020夏季运动会回顾', 
          img: 'https://images.unsplash.com/photo-1517649763962-0c623066013b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', 
          desc: '夏季运动会上，学生们展现了团队协作和体育精神。',
          date: '2020-07-30',
          views: 547
        },
        { 
          id: 7, 
          title: '2019科学实验日回顾', 
          img: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', 
          desc: '科学实验日让学生们亲身体验科学的奇妙与乐趣。',
          date: '2019-10-25',
          views: 486
        },
        { 
          id: 8, 
          title: '2019春游活动回顾', 
          img: 'https://images.unsplash.com/photo-1517164850305-99a3e65bb47e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', 
          desc: '春游活动中，学生们亲近自然，增进了友谊。',
          date: '2019-04-18',
          views: 423
        }
      ],
      page: 1, // 当前页码
      limit: 2, // 每页加载数量
      loadMoreStatus: 'loadmore' // u-loadmore 组件的状态
    }
  },
  mounted() {
    uni.$on('lang-change', this.onLangChange)
    this.setNavTitle() // 新增：页面加载时设置标题
    this.initLogin() // 初始化登录
    this.getHomePageData() // 获取首页数据
    this.getWindowAdvert() // 获取弹窗广告
  },
  beforeDestroy() {
    uni.$off('lang-change', this.onLangChange)
  },
  methods: {
    // 初始化登录
    async initLogin() {
      const result = await weChatLogin();
      
      if (result.success) {
        if (!result.hasToken) {
          // 新登录成功
          // uni.showToast({
          //   title: this.lang === 'zh' ? result.message : 'Login successful',
          //   icon: 'success'
          // });
        }
      } else {
        // 登录失败
        uni.showToast({
          title: this.lang === 'zh' ? result.message : 'Login failed',
          icon: 'none'
        });
      }
    },
    
    onSearch(val) {
      // 搜索逻辑
      if (val) {
        // 跳转到往期风采列表页面，并传递搜索关键词
        uni.navigateTo({
          url: `/pages/past-events/list?keyword=${encodeURIComponent(val)}`
        });
      } else {
        uni.showToast({ 
          title: this.lang === 'zh' ? '请输入搜索内容' : 'Please enter search keyword', 
          icon: 'none' 
        });
      }
    },
    onCategoryClick(idx) {
      // 分类点击逻辑
      uni.showToast({ title: '点击分类：' + (this.lang === 'zh' ? this.categoryList[idx].name : this.categoryList[idx].name_en), icon: 'none' })
    },
    onActivityClick(item) {
      // 活动卡片点击逻辑
      uni.showToast({ title: '点击活动：' + item.title, icon: 'none' })
    },
    onLangChange(newLang) {
      this.lang = newLang
      this.setNavTitle() // 新增：语言切换时设置标题
      this.getHomePageData() // 语言切换时重新获取首页数据
    },
    // 新增：设置导航栏标题的方法
    setNavTitle() {
      const title = this.lang === 'zh' ? '培训班夏令营' : 'Summer Camp'
      uni.setNavigationBarTitle({
        title: title
      })
    },
    // 新增：加载更多往期风采
    async loadMorePast() {
      if (this.loadMoreStatus === 'loading') return
      this.loadMoreStatus = 'loading'
      try {
        const newItems = await this.fetchPastItems()
        if (newItems.length > 0) {
          this.pastList = [...this.pastList, ...newItems]
          this.page++
          // 检查是否还有更多数据
          if (this.page * this.limit < this.pastListMore.length) {
            this.loadMoreStatus = 'loadmore'
          } else {
            this.loadMoreStatus = 'nomore'
          }
        } else {
          this.loadMoreStatus = 'nomore'
        }
      } catch (error) {
        console.error('加载更多失败:', error)
        this.loadMoreStatus = 'loadmore'
        uni.showToast({
          title: this.lang === 'zh' ? '加载失败，请重试' : 'Failed to load, please try again',
          icon: 'none'
        })
      }
    },
    // 新增：模拟获取往期风采数据
    async fetchPastItems() {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 计算当前页应该获取的数据范围
      const start = (this.page * this.limit) - this.limit
      const end = this.page * this.limit
      
      // 从额外数据中获取指定范围的数据
      return this.pastListMore.slice(start, end)
    },
    navigateToPastEventDetail(id) {
      uni.navigateTo({
        url: `/pages/past-events/detail?id=${id}`
      })
    },
    navigateToEventDetail(id) {
      uni.navigateTo({
        url: `/pages/events/detail?id=${id}`
      })
    },
    // 获取首页数据
    async getHomePageData() {
      try {
        console.log('开始获取首页数据...');
        const response = await postHomePage({});
        console.log('首页接口返回数据:', response);
        
        if (response && response.code === 0 && response.data) {
          console.log('首页数据解析成功:', response.data);
          
          // 更新 banner 数据
          if (response.data.banner && response.data.banner.length > 0) {
            this.bannerList = response.data.banner.map(item => {
              return formatImageUrl(item.contents);
            });
          }
          
          // 更新分类数据
          if (response.data.picture && response.data.picture.length > 0) {
            this.categoryList = response.data.picture.map(item => {
              return {
                name: item.title,
                name_en: item.title,
                image: formatImageUrl(item.contents)
              };
            });
          }
          
          // 更新活动数据
          if (response.data.productHot && response.data.productHot.length > 0) {
            this.activityList = response.data.productHot.map(item => {
              const imgUrl = item.img && item.img !== '|' ?
                formatImageUrl(item.img) :
                'https://picsum.photos/800/600?random=' + item.id;

              return {
                id: item.id,
                title: item.name,
                time: item.addDate ? item.addDate.split(' ')[0] : '2024-01-01',
                img: imgUrl,
                desc: item.des || '暂无描述'
              };
            });
          }
          
        } else {
          console.error('获取首页数据失败:', response);
        }
      } catch (error) {
        console.error('获取首页数据异常:', error);
      }
    },
    
    navigateToCategory(idx) {
      // 使用本地存储
      uni.setStorageSync('selectedCategory', idx);
      
      // 使用switchTab跳转到tabbar页面
      uni.switchTab({
        url: '/pages/events/index'
      });
    },
    // 获取全屏弹窗广告
    async getWindowAdvert() {
      try {
        const response = await postWindowAdvert({});
        console.log('弹窗广告接口返回:', response);

        if (response && response.code === 0 && response.data) {
          const advertData = response.data;

          if (advertData.contents) {
            // 使用全局图片URL处理函数
            this.popupImage = formatImageUrl(advertData.contents);

            // 设置跳转链接（如果有的话）
            this.popupLink = advertData.link || '';

            // 显示弹窗
            this.showPopup = true;

            console.log('弹窗图片URL:', this.popupImage);
          }
        }
      } catch (error) {
        console.error('获取弹窗广告失败:', error);
      }
    },
    // 弹窗点击处理
    onPopupClick() {
      if (this.popupLink) {
        // 如果有跳转链接，进行跳转
        uni.navigateTo({
          url: this.popupLink
        });
      }
      this.showPopup = false;
    }
  }
}
</script>

<style scoped>
.poster-popup {
  /* 消除 u-popup 内部的默认 padding */
  line-height: 1; 
}
.search-bar {
  margin: 24rpx;
  margin-top: 0rpx;
  padding-top: 24rpx;
  box-sizing: border-box;
}
.index-page {
  min-height: 100vh;
  padding-bottom: 0;
}
.banner-swiper {
  margin: 24rpx;
}
.category-grid {
  margin-top: 24rpx;
}
.cat-title {
  font-size: 24rpx;
  margin-top: 8rpx;
  color: #333;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin: 32rpx 24rpx 16rpx 24rpx;
  color: #222;
}

.activity-list {
  padding: 0 24rpx;
}

.activity-card {
  margin-bottom: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.card-content {
  padding: 24rpx;
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-subtitle {
  font-size: 24rpx;
  color: #909399;
  margin-top: 8rpx;
}

.card-desc {
  font-size: 26rpx;
  color: #606266;
  margin-top: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #eee;
}

.card-date, .card-views {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #909399;
}

.card-date text, .card-views text {
  margin-left: 6rpx;
}

.popup-content {
  padding: 48rpx 32rpx;
  text-align: center;
}
</style>
