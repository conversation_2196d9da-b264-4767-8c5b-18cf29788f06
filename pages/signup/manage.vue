<template>
  <view class="signup-manage-page">
    <!-- 顶部提示 -->
    <view class="tip-bar">
      <u-icon name="info-circle" size="28" color="#4285f4"></u-icon>
      <text class="tip-text">{{ lang === 'zh' ? '报名资料用于活动报名，可添加多个资料' : 'Registration info for activities, you can add multiple profiles' }}</text>
    </view>
    
    <!-- 资料列表 -->
    <view class="profile-list">
      <view 
        class="profile-item" 
        v-for="(item, index) in profileList" 
        :key="index" 
        @click="editProfile(index)"
        :class="{'default-profile': item.isDefault}"
      >
        <view class="profile-main">
          <view class="profile-info">
            <view class="profile-name">
              {{ item.name }}
              <text class="default-tag" v-if="item.isDefault">{{ lang === 'zh' ? '默认' : 'Default' }}</text>
            </view>
            <view class="profile-phone">{{ item.phone }}</view>
          </view>
          <view class="profile-actions">
            <view class="action-btn edit-btn" @click.stop="editProfile(index)">
              <u-icon name="edit-pen" size="24" color="#4285f4"></u-icon>
            </view>
            <view class="action-btn delete-btn" @click.stop="confirmDelete(index)">
              <u-icon name="trash" size="24" color="#ff5252"></u-icon>
            </view>
          </view>
        </view>
        <view class="profile-detail">
          <view class="detail-item" v-if="item.idCard">
            <text class="detail-label">{{ lang === 'zh' ? '身份证' : 'ID Card' }}:</text>
            <text class="detail-value">{{ formatIdCard(item.idCard) }}</text>
          </view>
          <view class="detail-item" v-if="item.email">
            <text class="detail-label">{{ lang === 'zh' ? '邮箱' : 'Email' }}:</text>
            <text class="detail-value">{{ item.email }}</text>
          </view>
          <view class="detail-item" v-if="item.school">
            <text class="detail-label">{{ lang === 'zh' ? '学校' : 'School' }}:</text>
            <text class="detail-value">{{ item.school }}</text>
          </view>
          <view class="detail-item" v-if="item.address">
            <text class="detail-label">{{ lang === 'zh' ? '地址' : 'Address' }}:</text>
            <text class="detail-value">{{ item.address }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 添加按钮 -->
    <view class="add-btn" @click="addProfile">
      <text>{{ lang === 'zh' ? '添加报名资料' : 'Add Profile' }}</text>
    </view>
    
    <!-- 编辑/添加弹窗 -->
    <u-popup :show="showForm" mode="bottom" @close="closeForm" :safeAreaInsetBottom="true" round="16">
      <view class="form-popup">
        <view class="form-header">
          <text class="form-title">{{ isEdit ? (lang === 'zh' ? '编辑资料' : 'Edit Profile') : (lang === 'zh' ? '添加资料' : 'Add Profile') }}</text>
          <u-icon name="close" size="24" color="#999" @click="closeForm"></u-icon>
        </view>
        
        <scroll-view scroll-y class="form-scroll">
          <view class="form-content">
            <view class="form-item">
              <text class="form-label required">{{ lang === 'zh' ? '姓名' : 'Name' }}</text>
              <u-input v-model="formData.name" :placeholder="lang === 'zh' ? '请输入姓名' : 'Enter name'" border="bottom"></u-input>
            </view>
            
            <view class="form-item">
              <text class="form-label required">{{ lang === 'zh' ? '手机号' : 'Phone' }}</text>
              <u-input v-model="formData.phone" :placeholder="lang === 'zh' ? '请输入手机号' : 'Enter phone'" border="bottom" type="number"></u-input>
            </view>
            
            <view class="form-item">
              <text class="form-label">{{ lang === 'zh' ? '身份证号' : 'ID Card' }}</text>
              <u-input v-model="formData.idCard" :placeholder="lang === 'zh' ? '请输入身份证号' : 'Enter ID card'" border="bottom"></u-input>
            </view>
            
            <view class="form-item">
              <text class="form-label">{{ lang === 'zh' ? '邮箱' : 'Email' }}</text>
              <u-input v-model="formData.email" :placeholder="lang === 'zh' ? '请输入邮箱' : 'Enter email'" border="bottom"></u-input>
            </view>
            
            <view class="form-item">
              <text class="form-label">{{ lang === 'zh' ? '学校' : 'School' }}</text>
              <u-input v-model="formData.school" :placeholder="lang === 'zh' ? '请输入学校' : 'Enter school'" border="bottom"></u-input>
            </view>
            
            <view class="form-item">
              <text class="form-label">{{ lang === 'zh' ? '地址' : 'Address' }}</text>
              <u-input v-model="formData.address" :placeholder="lang === 'zh' ? '请输入地址' : 'Enter address'" border="bottom"></u-input>
            </view>
            
            <view class="form-item">
              <view class="checkbox-item">
                <u-checkbox
                  v-model="formData.isDefault"
                  :disabled="isDefaultDisabled"
                  activeColor="#4285f4"
                  @change="onDefaultChange"
                ></u-checkbox>
                <text class="checkbox-text">{{ lang === 'zh' ? '设为默认资料' : 'Set as default' }}</text>
              </view>
              <text class="debug-text" style="font-size: 24rpx; color: #999; margin-top: 10rpx;">
                调试: isDefault = {{ formData.isDefault }}
              </text>
            </view>
          </view>
        </scroll-view>
        
        <view class="form-footer">
          <u-button type="primary" @click="saveProfile">
            {{ lang === 'zh' ? '保存' : 'Save' }}
          </u-button>
        </view>
      </view>
    </u-popup>
    
    <!-- 语言切换 -->
    <LangSwitch />
  </view>
</template>

<script>
import LangSwitch from '@/components/LangSwitch.vue';
import { addUserInfoReg, updateUserInfoReg, delUserInfoReg, getUserInfoRegList } from '@/api/user.js';

export default {
  components: {
    LangSwitch
  },
  data() {
    return {
      lang: 'zh',
      profileList: [],
      showForm: false,
      isEdit: false,
      currentIndex: -1,
      formData: {
        id: null,
        name: '',
        phone: '',
        idCard: '',
        email: '',
        school: '',
        address: '',
        isDefault: false
      },
      isDefaultDisabled: false
    };
  },
  computed: {
    hasDefaultProfile() {
      return this.profileList.some(item => item.isDefault);
    }
  },
  onReady() {
    // 在页面渲染完成后初始化
  },
  mounted() {
    // 获取语言设置
    const lang = uni.getStorageSync('lang') || 'zh';
    this.lang = lang;
    
    // 监听语言变化
    uni.$on('lang-change', this.onLangChange);
    
    // 设置导航栏标题
    this.setNavTitle();
    
    // 获取报名资料列表
    this.getProfileList();
  },
  beforeDestroy() {
    uni.$off('lang-change', this.onLangChange);
  },
  methods: {
    onLangChange(newLang) {
      this.lang = newLang;
      this.setNavTitle();
    },
    setNavTitle() {
      const title = this.lang === 'zh' ? '报名资料管理' : 'Registration Info';
      uni.setNavigationBarTitle({ title });
    },
    async getProfileList() {
      try {
        uni.showLoading({
          title: this.lang === 'zh' ? '加载中...' : 'Loading...'
        });

        const response = await getUserInfoRegList({
          pg: 1,
          size: 100 // 获取所有数据
        });

        console.log('获取报名资料列表响应:', response);

        if (response && response.code === 0 && response.data) {
          // 将API返回的数据转换为页面需要的格式
          this.profileList = response.data.map(item => ({
            id: item.id,
            name: item.userName || '',
            phone: item.mobile || '',
            idCard: item.idCard || '',
            email: item.email || '',
            school: item.school || '',
            address: item.address || '',
            isDefault: item.isDefault === 1
          }));
        } else {
          console.error('获取报名资料列表失败:', response);
          uni.showToast({
            title: this.lang === 'zh' ? '获取数据失败' : 'Failed to load data',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取报名资料列表异常:', error);
        uni.showToast({
          title: this.lang === 'zh' ? '网络错误' : 'Network error',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },
    formatIdCard(idCard) {
      if (!idCard) return '';
      // 保留前4位和后4位，中间用*代替
      return idCard.substring(0, 4) + '************' + idCard.substring(idCard.length - 4);
    },
    addProfile() {
      this.isEdit = false;
      this.currentIndex = -1;
      
      // 检查是否已有默认资料
      const hasDefault = this.profileList.some(item => item.isDefault);
      
      this.formData = {
        id: null,
        name: '',
        phone: '',
        idCard: '',
        email: '',
        school: '',
        address: '',
        isDefault: !hasDefault // 如果没有默认资料，则自动设为默认
      };
      
      // 如果没有默认资料，则不能取消默认选项
      this.isDefaultDisabled = !hasDefault;
      
      this.showForm = true;
    },
    editProfile(index) {
      this.isEdit = true;
      this.currentIndex = index;
      const profile = this.profileList[index];

      // 明确映射字段，确保数据类型正确
      this.formData = {
        id: profile.id,
        name: profile.name || '',
        phone: profile.phone || '',
        idCard: profile.idCard || '',
        email: profile.email || '',
        school: profile.school || '',
        address: profile.address || '',
        isDefault: Boolean(profile.isDefault) // 确保是布尔值
      };

      console.log('编辑资料 - formData:', this.formData);

      // 修改逻辑：如果当前编辑的是默认资料，则禁用复选框（不能取消默认）
      // 如果不是默认资料，且已有其他默认资料，则不禁用（可以切换默认）
      this.isDefaultDisabled = profile.isDefault;

      this.showForm = true;
    },
    closeForm() {
      this.showForm = false;
    },
    onDefaultChange(value) {
      console.log('复选框变化:', value);
      this.formData.isDefault = value;
    },
    async saveProfile() {
      // 表单验证
      if (!this.formData.name) {
        return uni.showToast({
          title: this.lang === 'zh' ? '请输入姓名' : 'Please enter name',
          icon: 'none'
        });
      }
      if (!this.formData.phone) {
        return uni.showToast({
          title: this.lang === 'zh' ? '请输入手机号' : 'Please enter phone',
          icon: 'none'
        });
      }

      // 显示加载中
      uni.showLoading({
        title: this.lang === 'zh' ? '保存中...' : 'Saving...'
      });

      try {
        // 处理默认资料逻辑
        if (this.formData.isDefault) {
          // 如果当前资料设为默认，则其他资料取消默认
          this.profileList.forEach(item => {
            item.isDefault = false;
          });
        } else if (this.isEdit && this.profileList[this.currentIndex].isDefault) {
          // 如果正在编辑的是默认资料，但取消了默认，则强制保持默认
          this.formData.isDefault = true;
        } else if (!this.profileList.some(item => item.isDefault) && (this.profileList.length === 0 || this.isEdit)) {
          // 如果没有默认资料，则当前资料设为默认
          this.formData.isDefault = true;
        }

        // 准备API数据
        const apiData = {
          userName: this.formData.name,
          mobile: this.formData.phone,
          idCard: this.formData.idCard || '',
          email: this.formData.email || '',
          school: this.formData.school || '',
          address: this.formData.address || '',
          isDefault: this.formData.isDefault ? 1 : 0
        };

        console.log('保存前 - formData.isDefault:', this.formData.isDefault);
        console.log('保存前 - apiData.isDefault:', apiData.isDefault);

        let response;
        if (this.isEdit) {
          // 修改资料
          apiData.id = this.formData.id;
          response = await updateUserInfoReg(apiData);
        } else {
          // 添加资料
          response = await addUserInfoReg(apiData);
        }

        console.log('保存报名资料响应:', response);

        if (response && response.code === 0) {
          uni.showToast({
            title: this.lang === 'zh' ? '保存成功' : 'Saved successfully',
            icon: 'success'
          });

          // 关闭弹窗
          this.closeForm();

          // 重新获取列表
          this.getProfileList();
        } else {
          uni.showToast({
            title: response?.msg || (this.lang === 'zh' ? '保存失败' : 'Save failed'),
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('保存报名资料异常:', error);
        uni.showToast({
          title: this.lang === 'zh' ? '网络错误' : 'Network error',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },
    confirmDelete(index) {
      uni.showModal({
        title: this.lang === 'zh' ? '确认删除' : 'Confirm Delete',
        content: this.lang === 'zh' ? '确定要删除该报名资料吗？' : 'Are you sure you want to delete this profile?',
        success: (res) => {
          if (res.confirm) {
            this.deleteProfile(index);
          }
        }
      });
    },
    async deleteProfile(index) {
      const profile = this.profileList[index];

      try {
        uni.showLoading({
          title: this.lang === 'zh' ? '删除中...' : 'Deleting...'
        });

        const response = await delUserInfoReg({
          id: profile.id
        });

        console.log('删除报名资料响应:', response);

        if (response && response.code === 0) {
          uni.showToast({
            title: this.lang === 'zh' ? '删除成功' : 'Deleted successfully',
            icon: 'success'
          });

          // 重新获取列表
          this.getProfileList();
        } else {
          uni.showToast({
            title: response?.msg || (this.lang === 'zh' ? '删除失败' : 'Delete failed'),
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('删除报名资料异常:', error);
        uni.showToast({
          title: this.lang === 'zh' ? '网络错误' : 'Network error',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    }
  }
}
</script>

<style scoped>
.signup-manage-page {
  min-height: 100vh;
  box-sizing: border-box;
  padding-bottom: calc(120rpx + constant(safe-area-inset-bottom)); /* iOS 11.2 以下 */
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom)); /* iOS 11.2 及以上 */
  background-color: #f5f7fa;
}
.tip-bar {
  display: flex;
  align-items: center;
  background-color: #e6f2ff;
  padding: 20rpx 30rpx;
}
.tip-text {
  font-size: 26rpx;
  color: #4285f4;
  margin-left: 10rpx;
}
.profile-list {
  padding: 20rpx;
}
.profile-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
  border: 2rpx solid transparent; /* 添加透明边框，避免选中时的跳动 */
}
.profile-item.default-profile {
  border: 2rpx solid #4285f4; /* 默认资料的边框 */
}
.profile-main {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.profile-info {
  flex: 1;
}
.profile-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.profile-name .default-tag {
  background-color: #4285f4;
  color: #fff;
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: 0 12rpx 0 12rpx;
  margin-left: 10rpx;
}
.profile-phone {
  font-size: 28rpx;
  color: #666;
}
.profile-actions {
  display: flex;
}
.action-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}
.profile-detail {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
}
.detail-item {
  display: flex;
  margin-bottom: 10rpx;
}
.detail-item:last-child {
  margin-bottom: 0;
}
.detail-label {
  width: 120rpx;
  font-size: 26rpx;
  color: #999;
}
.detail-value {
  flex: 1;
  font-size: 26rpx;
  color: #666;
}
.add-btn {
  position: fixed;
  bottom: calc(40rpx + constant(safe-area-inset-bottom)); /* iOS 11.2 以下 */
  bottom: calc(40rpx + env(safe-area-inset-bottom)); /* iOS 11.2 及以上 */
  left: 50%;
  transform: translateX(-50%);
  background-color: #4285f4;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90%;
  height: 80rpx;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(66,133,244,0.3);
  z-index: 9;
}
.add-btn text {
  margin-left: 10rpx;
  font-size: 28rpx;
}
.form-popup {
  padding: 30rpx;
  box-sizing: border-box;
  height: 80vh;
  display: flex;
  flex-direction: column;
}
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.form-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.form-scroll {
  flex: 1;
  height: 0;
}
.form-content {
  padding-bottom: 30rpx;
}
.form-item {
  margin-bottom: 30rpx;
}
.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}
.required::before {
  content: '*';
  color: #ff5252;
  margin-right: 6rpx;
}
.checkbox-item {
  display: flex;
  align-items: center;
}
.checkbox-text {
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}
.form-footer {
  padding-top: 20rpx;
  border-top: 1px solid #eee;
}
</style> 