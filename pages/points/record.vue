<template>
  <view class="points-record-page">
    <!-- 积分记录类型选择 -->
    <view class="record-tabs">
      <u-tabs
        :list="tabList"
        :current="currentTab"
        @click="onTabChange"
        :activeStyle="{
          color: '#2979ff',
          fontWeight: 'bold'
        }"
        :inactiveStyle="{
          color: '#666',
          fontWeight: 'normal'
        }"
        itemStyle="padding-left: 30rpx; padding-right: 30rpx; height: 80rpx;"
        :scrollable="false"
      ></u-tabs>
    </view>
    
    <!-- 记录列表 -->
    <view class="record-list">
      <block v-if="currentTab === 0">
        <!-- 全部记录 -->
        <view class="record-item" v-for="(item, index) in allRecords" :key="index" @click="viewDetail(item)">
          <view class="record-main">
            <view class="record-title">{{ item.title }}</view>
            <view class="record-time">{{ item.time }}</view>
          </view>
          <view class="record-points" :class="{ 'points-add': item.type === 'add', 'points-minus': item.type === 'minus' }">
            {{ item.type === 'add' ? '+' : '-' }}{{ item.points }}
          </view>
          <u-icon name="arrow-right" size="24" color="#c0c4cc"></u-icon>
        </view>
      </block>
      
      <block v-if="currentTab === 1">
        <!-- 活动记录 -->
        <view class="record-item" v-for="(item, index) in activityRecords" :key="index" @click="viewDetail(item)">
          <view class="record-main">
            <view class="record-title">{{ item.title }}</view>
            <view class="record-time">{{ item.time }}</view>
          </view>
          <view class="record-points" :class="{ 'points-add': item.type === 'add', 'points-minus': item.type === 'minus' }">
            {{ item.type === 'add' ? '+' : '-' }}{{ item.points }}
          </view>
          <u-icon name="arrow-right" size="24" color="#c0c4cc"></u-icon>
        </view>
      </block>
      
      <block v-if="currentTab === 2">
        <!-- 礼品记录 -->
        <view class="record-item" v-for="(item, index) in giftRecords" :key="index" @click="viewDetail(item)">
          <view class="record-main">
            <view class="record-title">{{ item.title }}</view>
            <view class="record-time">{{ item.time }}</view>
          </view>
          <view class="record-points points-minus">
            -{{ item.points }}
          </view>
          <u-icon name="arrow-right" size="24" color="#c0c4cc"></u-icon>
        </view>
      </block>
      
      <!-- 无记录提示 -->
      <view class="empty-tip" v-if="getCurrentRecords.length === 0">
        <u-empty mode="data" :text="lang === 'zh' ? '暂无积分记录' : 'No records yet'"></u-empty>
      </view>
    </view>
    
    <!-- 记录详情弹窗 -->
    <u-popup :show="showDetail" :safeAreaInsetBottom="false" mode="center" @close="closeDetail" round="16">
      <view class="detail-popup">
        <view class="detail-header">
          <text class="detail-title">{{ lang === 'zh' ? '记录详情' : 'Record Detail' }}</text>
          <u-icon name="close" size="24" color="#999" @click="closeDetail"></u-icon>
        </view>
        
        <view class="detail-content">
          <!-- 通用信息 -->
          <view class="detail-item">
            <text class="detail-label">{{ lang === 'zh' ? '类型' : 'Type' }}</text>
            <text class="detail-value">{{ getRecordTypeName(currentDetail.category) }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">{{ lang === 'zh' ? '名称' : 'Name' }}</text>
            <text class="detail-value">{{ currentDetail.title }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">{{ lang === 'zh' ? '时间' : 'Time' }}</text>
            <text class="detail-value">{{ currentDetail.time }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">{{ lang === 'zh' ? '积分变动' : 'Points' }}</text>
            <text class="detail-value" :class="{ 'points-add': currentDetail.type === 'add', 'points-minus': currentDetail.type === 'minus' }">
              {{ currentDetail.type === 'add' ? '+' : '-' }}{{ currentDetail.points }}
            </text>
          </view>
          
          <!-- 活动类特有信息 -->
          <block v-if="currentDetail.category === 'activity'">
            <view class="detail-item">
              <text class="detail-label">{{ lang === 'zh' ? '活动地点' : 'Location' }}</text>
              <text class="detail-value">{{ currentDetail.location }}</text>
            </view>
            
            <view class="detail-item">
              <text class="detail-label">{{ lang === 'zh' ? '活动时长' : 'Duration' }}</text>
              <text class="detail-value">{{ currentDetail.duration }}</text>
            </view>
          </block>
          
          <!-- 礼品类特有信息 -->
          <block v-if="currentDetail.category === 'gift'">
            <view class="detail-item">
              <text class="detail-label">{{ lang === 'zh' ? '兑换时间' : 'Exchange Time' }}</text>
              <text class="detail-value">{{ currentDetail.exchangeTime }}</text>
            </view>
            
            <view class="detail-item">
              <text class="detail-label">{{ lang === 'zh' ? '物流状态' : 'Delivery Status' }}</text>
              <text class="detail-value">{{ getDeliveryStatus(currentDetail.deliveryStatus) }}</text>
            </view>
            
            <view class="detail-item">
              <text class="detail-label">{{ lang === 'zh' ? '快递单号' : 'Tracking No.' }}</text>
              <text class="detail-value">{{ currentDetail.trackingNo || '-' }}</text>
            </view>
            
            <view class="detail-item" v-if="currentDetail.trackingNo">
              <text class="detail-label">{{ lang === 'zh' ? '收货地址' : 'Address' }}</text>
              <text class="detail-value address-value">{{ currentDetail.address }}</text>
            </view>
          </block>
        </view>
        
        <!-- 查看物流按钮 -->
        <view class="detail-footer" v-if="currentDetail.category === 'gift' && currentDetail.trackingNo">
          <u-button type="primary" @click="checkDelivery">{{ lang === 'zh' ? '查看物流' : 'Check Delivery' }}</u-button>
        </view>
      </view>
    </u-popup>
    
    <!-- 语言切换 -->
    <LangSwitch />
  </view>
</template>

<script>
import LangSwitch from '@/components/LangSwitch.vue';

export default {
  components: {
    LangSwitch
  },
  data() {
    return {
      lang: 'zh',
      currentTab: 0,
      tabList: [],
      allRecords: [],
      activityRecords: [],
      giftRecords: [],
      showDetail: false,
      currentDetail: {}
    };
  },
  computed: {
    getCurrentRecords() {
      switch (this.currentTab) {
        case 0:
          return this.allRecords;
        case 1:
          return this.activityRecords;
        case 2:
          return this.giftRecords;
        default:
          return [];
      }
    }
  },
  onReady() {
    // 在页面渲染完成后初始化标签页
    this.initTabs();
  },
  mounted() {
    // 获取语言设置
    const lang = uni.getStorageSync('lang') || 'zh';
    this.lang = lang;
    
    // 监听语言变化
    uni.$on('lang-change', this.onLangChange);
    
    // 设置导航栏标题
    this.setNavTitle();
    
    // 获取积分记录
    this.getPointsRecords();
  },
  beforeDestroy() {
    uni.$off('lang-change', this.onLangChange);
  },
  methods: {
    onLangChange(newLang) {
      this.lang = newLang;
      this.setNavTitle();
      this.initTabs();
    },
    setNavTitle() {
      const title = this.lang === 'zh' ? '积分记录' : 'Points Record';
      uni.setNavigationBarTitle({ title });
    },
    initTabs() {
      this.tabList = [
        { name: this.lang === 'zh' ? '全部' : 'All' },
        { name: this.lang === 'zh' ? '活动' : 'Activities' },
        { name: this.lang === 'zh' ? '礼品' : 'Gifts' }
      ];
    },
    onTabChange(res) {
      console.log(res,"大家访")
      this.currentTab = res.index;
    },
    getPointsRecords() {
      // 这里应该是从服务器获取积分记录
      // 这里使用模拟数据
      
      // 活动类记录
      this.activityRecords = [
        {
          id: 'act001',
          category: 'activity',
          type: 'add',
          title: this.lang === 'zh' ? '参与夏令营活动' : 'Summer Camp Activity',
          time: '2023-07-15 14:30',
          points: 200,
          location: this.lang === 'zh' ? '深圳市南山区科技园' : 'Nanshan District, Shenzhen',
          duration: this.lang === 'zh' ? '3小时' : '3 hours'
        },
        {
          id: 'act002',
          category: 'activity',
          type: 'add',
          title: this.lang === 'zh' ? '完成编程挑战赛' : 'Coding Challenge',
          time: '2023-07-20 10:15',
          points: 150,
          location: this.lang === 'zh' ? '线上' : 'Online',
          duration: this.lang === 'zh' ? '2小时' : '2 hours'
        },
        {
          id: 'act003',
          category: 'activity',
          type: 'add',
          title: this.lang === 'zh' ? '分享学习心得' : 'Learning Experience Sharing',
          time: '2023-08-05 16:00',
          points: 50,
          location: this.lang === 'zh' ? '深圳市福田区会展中心' : 'Futian District, Shenzhen',
          duration: this.lang === 'zh' ? '1小时' : '1 hour'
        }
      ];
      
      // 礼品类记录
      this.giftRecords = [
        {
          id: 'gift001',
          category: 'gift',
          type: 'minus',
          title: this.lang === 'zh' ? '兑换定制T恤' : 'Custom T-shirt',
          time: '2023-08-10 09:45',
          points: 100,
          exchangeTime: '2023-08-10 09:45',
          deliveryStatus: 'delivered',
          trackingNo: 'SF1234567890',
          address: this.lang === 'zh' ? '广东省深圳市南山区科技园南区T3栋5楼' : 'Floor 5, Building T3, South Area of Science Park, Nanshan District, Shenzhen, Guangdong Province'
        },
        {
          id: 'gift002',
          category: 'gift',
          type: 'minus',
          title: this.lang === 'zh' ? '兑换蓝牙耳机' : 'Bluetooth Headphones',
          time: '2023-08-15 14:20',
          points: 300,
          exchangeTime: '2023-08-15 14:20',
          deliveryStatus: 'shipping',
          trackingNo: 'YT9876543210',
          address: this.lang === 'zh' ? '广东省深圳市南山区科技园南区T3栋5楼' : 'Floor 5, Building T3, South Area of Science Park, Nanshan District, Shenzhen, Guangdong Province'
        },
        {
          id: 'gift003',
          category: 'gift',
          type: 'minus',
          title: this.lang === 'zh' ? '兑换电影票券' : 'Movie Tickets',
          time: '2023-08-20 11:30',
          points: 80,
          exchangeTime: '2023-08-20 11:30',
          deliveryStatus: 'virtual',
          trackingNo: '',
          address: ''
        }
      ];
      
      // 合并所有记录
      this.allRecords = [...this.activityRecords, ...this.giftRecords].sort((a, b) => {
        return new Date(b.time) - new Date(a.time);
      });
    },
    viewDetail(item) {
      this.currentDetail = item;
      this.showDetail = true;
    },
    closeDetail() {
      this.showDetail = false;
    },
    getRecordTypeName(category) {
      if (category === 'activity') {
        return this.lang === 'zh' ? '活动' : 'Activity';
      } else if (category === 'gift') {
        return this.lang === 'zh' ? '礼品' : 'Gift';
      }
      return '';
    },
    getDeliveryStatus(status) {
      switch (status) {
        case 'pending':
          return this.lang === 'zh' ? '待发货' : 'Pending';
        case 'shipping':
          return this.lang === 'zh' ? '运输中' : 'Shipping';
        case 'delivered':
          return this.lang === 'zh' ? '已送达' : 'Delivered';
        case 'virtual':
          return this.lang === 'zh' ? '虚拟物品' : 'Virtual Item';
        default:
          return '';
      }
    },
    checkDelivery() {
      // 这里应该跳转到物流查询页面或调用物流查询API
      // 这里简单模拟
      uni.showToast({
        title: this.lang === 'zh' ? '查询物流信息...' : 'Checking delivery info...',
        icon: 'none'
      });
      
      setTimeout(() => {
        uni.showModal({
          title: this.lang === 'zh' ? '物流信息' : 'Delivery Information',
          content: this.lang === 'zh' 
            ? `快递单号: ${this.currentDetail.trackingNo}\n状态: ${this.getDeliveryStatus(this.currentDetail.deliveryStatus)}\n最新位置: 深圳市南山区配送中心` 
            : `Tracking No.: ${this.currentDetail.trackingNo}\nStatus: ${this.getDeliveryStatus(this.currentDetail.deliveryStatus)}\nLatest Location: Nanshan Distribution Center, Shenzhen`,
          showCancel: false
        });
      }, 1000);
    }
  }
}
</script>

<style scoped>
.points-record-page {
  min-height: 100vh;
  box-sizing: border-box;
  padding-bottom: 100rpx;
  background-color: #f5f7fa;
}
.record-tabs {
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}
.record-list {
  padding: 20rpx;
}
.record-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
}
.record-main {
  flex: 1;
}
.record-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}
.record-time {
  font-size: 24rpx;
  color: #999;
}
.record-points {
  font-size: 32rpx;
  font-weight: bold;
  margin: 0 20rpx;
}
.points-add {
  color: #19be6b;
}
.points-minus {
  color: #ff9900;
}
.empty-tip {
  padding: 100rpx 0;
}
.detail-popup {
  width: 600rpx;
  padding: 30rpx;
  box-sizing: border-box;
}
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.detail-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.detail-content {
}
.detail-item {
  display: flex;
  margin-top: 20rpx;
}
.detail-item:first-child {
  margin-top: 0;
}
.detail-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
}
.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.address-value {
  word-break: break-all;
}
.detail-footer {
  margin-top: 40rpx;
}
</style> 