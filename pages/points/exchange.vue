<template>
  <view class="exchange-page">
    <!-- 顶部积分信息 -->
    <view class="points-info">
      <view class="points-title">{{ lang === 'zh' ? '我的积分' : 'My Points' }}</view>
      <view class="points-value">{{ points }}</view>
    </view>
    
    <!-- 兑换类型选择 -->
    <view class="exchange-tabs">
      <u-tabs
        :list="tabList"
        :current="currentTab"
        @click="onTabChange"
        :activeStyle="{
          color: '#4285f4',
          fontWeight: 'bold',
          transform: 'scale(1.05)'
        }"
        :inactiveStyle="{
          color: '#666',
          fontWeight: 'normal',
          transform: 'scale(1)'
        }"
        itemStyle="padding-left: 60rpx; padding-right: 60rpx; height: 80rpx;"
        :scrollable="false"
        lineColor="#4285f4"
        lineWidth="40"
      ></u-tabs>
    </view>
    
    <!-- 兑换列表 -->
    <view class="exchange-list">
      <block v-if="currentTab === 0">
        <!-- 活动列表 -->
        <view class="exchange-item" v-for="(item, index) in activityList" :key="index" @click="viewActivityDetail(item)">
          <view class="item-main">
            <u-image :src="item.image" width="200rpx" height="160rpx" radius="8" class="item-image"></u-image>
            <view class="item-content">
              <view class="item-title">{{ item.title }}</view>
              <view class="item-desc">{{ item.desc }}</view>
            </view>
          </view>
          <view class="item-footer">
            <view class="item-points">{{ item.points }} {{ lang === 'zh' ? '积分' : 'points' }}</view>
            <view class="exchange-btn" @click.stop="joinActivity(item)">
              {{ lang === 'zh' ? '立即报名' : 'Join Now' }}
            </view>
          </view>
        </view>
      </block>
      
      <block v-if="currentTab === 1">
        <!-- 礼品列表 -->
        <view class="exchange-item" v-for="(item, index) in giftList" :key="index" @click="viewGiftDetail(item)">
          <view class="item-main">
            <u-image :src="item.image" width="200rpx" height="160rpx" radius="8" class="item-image"></u-image>
            <view class="item-content">
              <view class="item-title">{{ item.title }}</view>
              <view class="item-desc">{{ item.desc }}</view>
            </view>
          </view>
          <view class="item-footer">
            <view class="item-points">{{ item.points }} {{ lang === 'zh' ? '积分' : 'points' }}</view>
            <view class="exchange-btn" @click.stop="exchangeGift(item)">
              {{ lang === 'zh' ? '立即兑换' : 'Exchange' }}
            </view>
          </view>
        </view>
      </block>
      
      <!-- 无记录提示 -->
      <view class="empty-tip" v-if="getCurrentList.length === 0">
        <u-empty mode="data" :text="lang === 'zh' ? '暂无可兑换项目' : 'No items available'"></u-empty>
      </view>
    </view>
    
    <!-- 活动详情弹窗 -->
    <u-popup :show="showActivityDetail" mode="center" @close="closeActivityDetail" round="16" :safeAreaInsetBottom="false">
      <view class="detail-popup">
        <view class="detail-header">
          <text class="detail-title">{{ lang === 'zh' ? '活动详情' : 'Activity Detail' }}</text>
          <u-icon name="close" size="24" color="#999" @click="closeActivityDetail"></u-icon>
        </view>
        
        <view class="detail-content">
          <u-image :src="currentActivity.image" width="100%" height="300rpx" radius="8"></u-image>
          
          <view class="detail-item">
            <text class="detail-label">{{ lang === 'zh' ? '活动名称' : 'Name' }}</text>
            <text class="detail-value">{{ currentActivity.title }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">{{ lang === 'zh' ? '所需积分' : 'Points' }}</text>
            <text class="detail-value">{{ currentActivity.points }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">{{ lang === 'zh' ? '活动时间' : 'Time' }}</text>
            <text class="detail-value">{{ currentActivity.time }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">{{ lang === 'zh' ? '活动地点' : 'Location' }}</text>
            <text class="detail-value">{{ currentActivity.location }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">{{ lang === 'zh' ? '活动详情' : 'Description' }}</text>
            <text class="detail-value">{{ currentActivity.desc }}</text>
          </view>
        </view>
        
        <view class="detail-footer">
          <u-button type="primary" @click="joinActivity(currentActivity)">
            {{ lang === 'zh' ? '立即报名' : 'Join Now' }}
          </u-button>
        </view>
      </view>
    </u-popup>
    
    <!-- 礼品详情弹窗 -->
    <u-popup :show="showGiftDetail" mode="center" @close="closeGiftDetail" round="16" :safeAreaInsetBottom="fakse">
      <view class="detail-popup">
        <view class="detail-header">
          <text class="detail-title">{{ lang === 'zh' ? '礼品详情' : 'Gift Detail' }}</text>
          <u-icon name="close" size="24" color="#999" @click="closeGiftDetail"></u-icon>
        </view>
        
        <view class="detail-content">
          <u-image :src="currentGift.image" width="100%" height="300rpx" radius="8"></u-image>
          
          <view class="detail-item">
            <text class="detail-label">{{ lang === 'zh' ? '礼品名称' : 'Name' }}</text>
            <text class="detail-value">{{ currentGift.title }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">{{ lang === 'zh' ? '所需积分' : 'Points' }}</text>
            <text class="detail-value">{{ currentGift.points }}</text>
          </view>
          
          <view class="detail-item">
            <text class="detail-label">{{ lang === 'zh' ? '礼品详情' : 'Description' }}</text>
            <text class="detail-value">{{ currentGift.desc }}</text>
          </view>
        </view>
        
        <view class="detail-footer">
          <u-button type="primary" @click="exchangeGift(currentGift)">
            {{ lang === 'zh' ? '立即兑换' : 'Exchange' }}
          </u-button>
        </view>
      </view>
    </u-popup>
    
    <!-- 活动报名弹窗 -->
    <u-popup :show="showActivityJoin" mode="center" @close="closeActivityJoin" round="16" :safeAreaInsetBottom="false">
      <view class="form-popup">
        <view class="form-header">
          <text class="form-title">{{ lang === 'zh' ? '活动报名' : 'Activity Registration' }}</text>
          <u-icon name="close" size="24" color="#999" @click="closeActivityJoin"></u-icon>
        </view>
        
        <view class="form-content">
          <!-- 选择报名资料 -->
          <view class="form-section">
            <view class="form-section-title">{{ lang === 'zh' ? '选择报名信息：' : 'Select Profile:' }}</view>
            <view class="profile-selector-new" @click="showProfileSelect = true">
              <text>{{ selectedProfile ? selectedProfile.name : (lang === 'zh' ? '请选择报名资料' : 'Please select a profile') }}</text>
              <u-icon name="arrow-right" size="36rpx" color="#909399"></u-icon>
            </view>
          </view>
          
          <!-- 显示选中的报名信息 -->
          <view class="selected-profile-info-new" v-if="selectedProfile">
            <view class="profile-item-row">
              <text class="profile-item-label">{{ lang === 'zh' ? '姓名：' : 'Name:' }}</text>
              <text class="profile-item-value">{{ selectedProfile.name }}</text>
            </view>
            <view class="profile-item-row">
              <text class="profile-item-label">{{ lang === 'zh' ? '手机：' : 'Phone:' }}</text>
              <text class="profile-item-value">{{ selectedProfile.phone }}</text>
            </view>
            <view class="profile-item-row" v-if="selectedProfile.email">
              <text class="profile-item-label">{{ lang === 'zh' ? '邮箱：' : 'Email:' }}</text>
              <text class="profile-item-value">{{ selectedProfile.email }}</text>
            </view>
            <view class="profile-item-row" v-if="selectedProfile.idCard">
              <text class="profile-item-label">{{ lang === 'zh' ? '身份证：' : 'ID Card:' }}</text>
              <text class="profile-item-value">{{ selectedProfile.idCard }}</text>
            </view>
            <view class="profile-item-row" v-if="selectedProfile.address">
              <text class="profile-item-label">{{ lang === 'zh' ? '地址：' : 'Address:' }}</text>
              <text class="profile-item-value">{{ selectedProfile.address }}</text>
            </view>
            <view class="profile-item-row" v-if="selectedProfile.school">
              <text class="profile-item-label">{{ lang === 'zh' ? '学校：' : 'School:' }}</text>
              <text class="profile-item-value">{{ selectedProfile.school }}</text>
            </view>
          </view>
          
          <view class="form-tips">
            <u-icon name="info-circle" size="32rpx" color="#ff9900" style="margin-right: 10rpx;"></u-icon>
            <text>{{ lang === 'zh' ? '报名成功后将消耗' : 'Registration will consume' }} {{ currentActivity.points }} {{ lang === 'zh' ? '积分' : 'points' }}</text>
          </view>
          
          <view class="form-link" @click="goToManageProfiles">
            {{ lang === 'zh' ? '管理报名资料 >' : 'Manage profiles >' }}
          </view>
        </view>
        
        <view class="form-footer">
          <u-button type="primary" @click="submitActivityJoin" :disabled="!selectedProfile">
            {{ lang === 'zh' ? '确认报名' : 'Confirm' }}
          </u-button>
        </view>
      </view>
    </u-popup>
    
    <!-- 报名信息选择弹窗 -->
    <u-popup :show="showProfileSelect" mode="bottom" @close="showProfileSelect = false" :safeAreaInsetBottom="true">
      <view class="popup-content">
        <view class="popup-header">
          <text>{{ lang === 'zh' ? '选择报名资料' : 'Select Profile' }}</text>
        </view>
        <scroll-view scroll-y class="profile-list">
          <view 
            v-for="(profile, index) in profileList" 
            :key="index" 
            class="profile-item"
            :class="{ 'selected': selectedProfileIndex === index, 'default': profile.isDefault }"
            @click="selectProfile(profile)"
          >
            <view class="profile-info">
              <text class="profile-name">{{ profile.name }}</text>
              <text class="profile-phone">{{ profile.phone }}</text>
            </view>
            <u-icon v-if="selectedProfileIndex === index" name="checkmark" color="#2979ff" size="40rpx"></u-icon>
          </view>
        </scroll-view>
        <view class="popup-buttons">
          <view class="cancel-button" @click="showProfileSelect = false">
            {{ lang === 'zh' ? '取消' : 'Cancel' }}
          </view>
          <view class="confirm-button" @click="confirmProfileSelection">
            {{ lang === 'zh' ? '确认' : 'Confirm' }}
          </view>
        </view>
        <view class="manage-profiles-link" @click="goToManageProfiles">
          {{ lang === 'zh' ? '管理报名资料 >' : 'Manage Profiles >' }}
        </view>
      </view>
    </u-popup>
    
    <!-- 礼品兑换弹窗 -->
    <u-popup :show="showGiftExchange" mode="center" @close="closeGiftExchange" round="16" :safeAreaInsetBottom="fakse">
      <view class="form-popup">
        <view class="form-header">
          <text class="form-title">{{ lang === 'zh' ? '礼品兑换' : 'Gift Exchange' }}</text>
          <u-icon name="close" size="24" color="#999" @click="closeGiftExchange"></u-icon>
        </view>
        
        <view class="form-content">
          <view class="form-item">
            <text class="form-label">{{ lang === 'zh' ? '收件人' : 'Recipient' }}</text>
            <u-input v-model="giftForm.name" :placeholder="lang === 'zh' ? '请输入收件人姓名' : 'Enter recipient name'" border="bottom"></u-input>
          </view>
          
          <view class="form-item">
            <text class="form-label">{{ lang === 'zh' ? '手机号' : 'Phone' }}</text>
            <u-input v-model="giftForm.phone" :placeholder="lang === 'zh' ? '请输入联系电话' : 'Enter contact phone'" border="bottom" type="number"></u-input>
          </view>
          
          <view class="form-item">
            <text class="form-label">{{ lang === 'zh' ? '收货地址' : 'Address' }}</text>
            <u-input v-model="giftForm.address" :placeholder="lang === 'zh' ? '请输入详细地址' : 'Enter detailed address'" border="bottom"></u-input>
          </view>
          
          <view class="form-item">
            <text class="form-label">{{ lang === 'zh' ? '备注' : 'Remarks' }}</text>
            <u-input v-model="giftForm.remarks" :placeholder="lang === 'zh' ? '请输入备注信息（选填）' : 'Enter remarks (optional)'" border="bottom"></u-input>
          </view>
          
          <view class="form-tips">
            {{ lang === 'zh' ? '兑换成功后将消耗' : 'This will cost' }} {{ currentGift.points }} {{ lang === 'zh' ? '积分' : 'points' }}
          </view>
        </view>
        
        <view class="form-footer">
          <u-button type="primary" @click="submitGiftExchange">
            {{ lang === 'zh' ? '确认兑换' : 'Confirm' }}
          </u-button>
        </view>
      </view>
    </u-popup>
    
    <!-- 语言切换 -->
    <LangSwitch />
  </view>
</template>

<script>
import LangSwitch from '@/components/LangSwitch.vue';

export default {
  components: {
    LangSwitch
  },
  data() {
    return {
      lang: 'zh',
      points: 1280,
      currentTab: 0,
      tabList: [],
      activityList: [],
      giftList: [],
      showActivityDetail: false,
      showGiftDetail: false,
      showActivityJoin: false,
      showGiftExchange: false,
      showProfileSelect: false, // 控制选择报名资料弹窗
      profileList: [
        {
          name: '张三',
          phone: '13812345678',
          idCard: '******************',
          email: '<EMAIL>',
          school: '深圳大学',
          address: '广东省深圳市南山区科技园南区',
          isDefault: true
        },
        {
          name: '李四',
          phone: '13987654321',
          idCard: '******************',
          email: '<EMAIL>',
          school: '华南理工大学',
          address: '广东省广州市天河区五山路',
          isDefault: false
        },
        {
          name: '王五',
          phone: '13500001111',
          idCard: '******************',
          email: '<EMAIL>',
          school: '北京大学',
          address: '北京市海淀区颐和园路5号',
          isDefault: false
        },
        {
          name: '赵六',
          phone: '13600002222',
          idCard: '******************',
          email: '<EMAIL>',
          school: '清华大学',
          address: '北京市海淀区清华园1号',
          isDefault: false
        },
        {
          name: '钱七',
          phone: '13700003333',
          idCard: '******************',
          email: '<EMAIL>',
          school: '复旦大学',
          address: '上海市杨浦区邯郸路220号',
          isDefault: false
        },
        {
          name: '孙八',
          phone: '13800004444',
          idCard: '******************',
          email: '<EMAIL>',
          school: '浙江大学',
          address: '浙江省杭州市西湖区余杭塘路866号',
          isDefault: false
        },
        {
          name: '周九',
          phone: '13900005555',
          idCard: '******************',
          email: '<EMAIL>',
          school: '南京大学',
          address: '江苏省南京市栖霞区仙林大道163号',
          isDefault: false
        }
      ], // 报名资料列表
      selectedProfile: null, // 当前选中的报名资料
      currentActivity: {},
      currentGift: {},
      activityForm: {
        name: '',
        phone: '',
        remarks: ''
      },
      giftForm: {
        name: '',
        phone: '',
        address: '',
        remarks: ''
      }
    };
  },
  computed: {
    getCurrentList() {
      return this.currentTab === 0 ? this.activityList : this.giftList;
    }
  },
  onReady() {
    // 在页面渲染完成后初始化标签页
    this.initTabs();
  },
  mounted() {
    // 获取语言设置
    const lang = uni.getStorageSync('lang') || 'zh';
    this.lang = lang;
    
    // 监听语言变化
    uni.$on('lang-change', this.onLangChange);
    
    // 设置导航栏标题
    this.setNavTitle();
    
    // 获取用户积分
    this.getUserPoints();
    
    // 获取兑换列表
    this.getExchangeList();
    
    // 初始化默认资料
    this.initDefaultProfile();
  },
  beforeDestroy() {
    uni.$off('lang-change', this.onLangChange);
  },
  methods: {
    onLangChange(newLang) {
      this.lang = newLang;
      this.setNavTitle();
      this.initTabs();
      this.getExchangeList(); // 重新获取列表以更新语言
    },
    setNavTitle() {
      const title = this.lang === 'zh' ? '积分兑换' : 'Points Exchange';
      uni.setNavigationBarTitle({ title });
    },
    initTabs() {
      this.tabList = [
        { name: this.lang === 'zh' ? '活动' : 'Activities' },
        { name: this.lang === 'zh' ? '礼品' : 'Gifts' }
      ];
    },
    onTabChange(res) {
      console.log(res, "切换标签");
      this.currentTab = res.index;
    },
    getUserPoints() {
      // 优先从本地存储的积分数据获取
      const userPoints = uni.getStorageSync('userPoints');
      if (userPoints !== undefined && userPoints !== null) {
        this.points = userPoints;
        return;
      }
      
      // 如果没有单独存储的积分，从用户信息中获取
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo && userInfo.points !== undefined) {
        this.points = userInfo.points;
      }
    },
    getExchangeList() {
      // 这里应该是从服务器获取兑换列表
      // 这里使用模拟数据
      
      // 活动列表
      this.activityList = [
        {
          id: 'act001',
          title: this.lang === 'zh' ? '编程技能工作坊' : 'Coding Skills Workshop',
          desc: this.lang === 'zh' ? '学习最新的编程技术和实践' : 'Learn the latest programming techniques and practices',
          points: 100,
          image: 'https://picsum.photos/400/300?random=1',
          time: '2023-09-15 14:00-17:00',
          location: this.lang === 'zh' ? '深圳市南山区科技园' : 'Nanshan District, Shenzhen'
        },
        {
          id: 'act002',
          title: this.lang === 'zh' ? '人工智能讲座' : 'AI Lecture Series',
          desc: this.lang === 'zh' ? '了解AI最新发展和应用场景' : 'Learn about the latest developments in AI and its applications',
          points: 150,
          image: 'https://picsum.photos/400/300?random=2',
          time: '2023-09-20 19:00-21:00',
          location: this.lang === 'zh' ? '线上直播' : 'Online Live'
        },
        {
          id: 'act003',
          title: this.lang === 'zh' ? '创业经验分享会' : 'Entrepreneurship Sharing',
          desc: this.lang === 'zh' ? '成功创业者分享经验和教训' : 'Successful entrepreneurs share their experiences and lessons',
          points: 200,
          image: 'https://picsum.photos/400/300?random=3',
          time: '2023-09-25 15:00-17:30',
          location: this.lang === 'zh' ? '深圳市福田区会展中心' : 'Futian Convention Center, Shenzhen'
        }
      ];
      
      // 礼品列表
      this.giftList = [
        {
          id: 'gift001',
          title: this.lang === 'zh' ? '定制T恤' : 'Custom T-shirt',
          desc: this.lang === 'zh' ? '舒适透气的纯棉T恤，印有夏令营Logo' : 'Comfortable cotton T-shirt with Summer Camp Logo',
          points: 100,
          image: 'https://picsum.photos/400/300?random=4',
          stock: 50
        },
        {
          id: 'gift002',
          title: this.lang === 'zh' ? '蓝牙耳机' : 'Bluetooth Headphones',
          desc: this.lang === 'zh' ? '高音质无线蓝牙耳机，续航时间长' : 'High-quality wireless headphones with long battery life',
          points: 300,
          image: 'https://picsum.photos/400/300?random=5',
          stock: 20
        },
        {
          id: 'gift003',
          title: this.lang === 'zh' ? '电影票券' : 'Movie Tickets',
          desc: this.lang === 'zh' ? '全国通用电影票兑换券，可在线选座' : 'Movie ticket voucher valid nationwide, online seat selection available',
          points: 80,
          image: 'https://picsum.photos/400/300?random=6',
          stock: 100
        },
        {
          id: 'gift004',
          title: this.lang === 'zh' ? '移动电源' : 'Power Bank',
          desc: this.lang === 'zh' ? '10000mAh大容量，支持快充' : '10000mAh capacity, supports fast charging',
          points: 200,
          image: 'https://picsum.photos/400/300?random=7',
          stock: 30
        }
      ];
    },
    viewActivityDetail(item) {
      this.currentActivity = item;
      this.showActivityDetail = true;
    },
    closeActivityDetail() {
      this.showActivityDetail = false;
    },
    viewGiftDetail(item) {
      this.currentGift = item;
      this.showGiftDetail = true;
    },
    closeGiftDetail() {
      this.showGiftDetail = false;
    },
    joinActivity(item) {
      // 检查积分是否足够
      if (this.points < item.points) {
        return uni.showToast({
          title: this.lang === 'zh' ? '积分不足' : 'Insufficient points',
          icon: 'none'
        });
      }
      
      this.currentActivity = item;
      this.showActivityDetail = false;
      this.showActivityJoin = true;
    },
    closeActivityJoin() {
      this.showActivityJoin = false;
      this.activityForm = {
        name: '',
        phone: '',
        remarks: ''
      };
      this.selectedProfile = null; // 关闭弹窗时清空选中资料
    },
    exchangeGift(item) {
      // 检查积分是否足够
      if (this.points < item.points) {
        return uni.showToast({
          title: this.lang === 'zh' ? '积分不足' : 'Insufficient points',
          icon: 'none'
        });
      }
      
      this.currentGift = item;
      this.showGiftDetail = false;
      this.showGiftExchange = true;
    },
    closeGiftExchange() {
      this.showGiftExchange = false;
      this.giftForm = {
        name: '',
        phone: '',
        address: '',
        remarks: ''
      };
    },
    submitActivityJoin() {
      // 表单验证
      if (!this.selectedProfile) {
        return uni.showToast({
          title: this.lang === 'zh' ? '请选择报名资料' : 'Please select a profile',
          icon: 'none'
        });
      }
      
      // 显示加载中
      uni.showLoading({
        title: this.lang === 'zh' ? '提交中...' : 'Submitting...'
      });
      
      // 这里应该是提交到服务器
      // 这里使用模拟提交
      setTimeout(() => {
        // 扣除积分
        this.points -= this.currentActivity.points;
        
        // 更新本地存储
        const userInfo = uni.getStorageSync('userInfo') || {};
        userInfo.points = this.points;
        uni.setStorageSync('userInfo', userInfo);
        
        uni.hideLoading();
        uni.showToast({
          title: this.lang === 'zh' ? '报名成功' : 'Registration successful',
          icon: 'success'
        });
        
        // 关闭弹窗
        this.closeActivityJoin();
      }, 1000);
    },
    submitGiftExchange() {
      // 表单验证
      if (!this.giftForm.name) {
        return uni.showToast({
          title: this.lang === 'zh' ? '请输入收件人姓名' : 'Please enter recipient name',
          icon: 'none'
        });
      }
      if (!this.giftForm.phone) {
        return uni.showToast({
          title: this.lang === 'zh' ? '请输入联系电话' : 'Please enter contact phone',
          icon: 'none'
        });
      }
      if (!this.giftForm.address) {
        return uni.showToast({
          title: this.lang === 'zh' ? '请输入收货地址' : 'Please enter delivery address',
          icon: 'none'
        });
      }
      
      // 显示加载中
      uni.showLoading({
        title: this.lang === 'zh' ? '提交中...' : 'Submitting...'
      });
      
      // 这里应该是提交到服务器
      // 这里使用模拟提交
      setTimeout(() => {
        // 扣除积分
        this.points -= this.currentGift.points;
        
        // 更新本地存储
        const userInfo = uni.getStorageSync('userInfo') || {};
        userInfo.points = this.points;
        uni.setStorageSync('userInfo', userInfo);
        
        uni.hideLoading();
        uni.showToast({
          title: this.lang === 'zh' ? '兑换成功' : 'Exchange successful',
          icon: 'success'
        });
        
        // 关闭弹窗
        this.closeGiftExchange();
      }, 1000);
    },
    // 新增：获取所有报名资料
    // getProfileList() {
    //   const profiles = uni.getStorageSync('signupProfiles') || [];
      
    //   // 如果没有数据，先添加一些测试数据
    //   if (profiles.length === 0) {
    //     const testProfiles = [
    //       {
    //         name: '张三',
    //         phone: '13812345678',
    //         idCard: '******************',
    //         email: '<EMAIL>',
    //         school: '深圳大学',
    //         address: '广东省深圳市南山区科技园南区',
    //         isDefault: true
    //       },
    //       {
    //         name: '李四',
    //         phone: '13987654321',
    //         idCard: '******************',
    //         email: '<EMAIL>',
    //         school: '华南理工大学',
    //         address: '广东省广州市天河区五山路',
    //         isDefault: false
    //       },
    //       {
    //         name: '王五',
    //         phone: '13500001111',
    //         idCard: '******************',
    //         email: '<EMAIL>',
    //         school: '北京大学',
    //         address: '北京市海淀区颐和园路5号',
    //         isDefault: false
    //       },
    //       {
    //         name: '赵六',
    //         phone: '13600002222',
    //         idCard: '******************',
    //         email: '<EMAIL>',
    //         school: '清华大学',
    //         address: '北京市海淀区清华园1号',
    //         isDefault: false
    //       },
    //       {
    //         name: '钱七',
    //         phone: '13700003333',
    //         idCard: '******************',
    //         email: '<EMAIL>',
    //         school: '复旦大学',
    //         address: '上海市杨浦区邯郸路220号',
    //         isDefault: false
    //       },
    //       {
    //         name: '孙八',
    //         phone: '13800004444',
    //         idCard: '******************',
    //         email: '<EMAIL>',
    //         school: '浙江大学',
    //         address: '浙江省杭州市西湖区余杭塘路866号',
    //         isDefault: false
    //       },
    //       {
    //         name: '周九',
    //         phone: '13900005555',
    //         idCard: '******************',
    //         email: '<EMAIL>',
    //         school: '南京大学',
    //         address: '江苏省南京市栖霞区仙林大道163号',
    //         isDefault: false
    //       }
    //     ];
        
    //     uni.setStorageSync('signupProfiles', testProfiles);
    //     this.profileList = testProfiles;
    //   } else {
    //     this.profileList = profiles;
    //   }
      
    //   // 如果有默认资料，自动选中
    //   const defaultProfile = this.profileList.find(profile => profile.isDefault);
    //   if (defaultProfile) {
    //     this.selectedProfile = defaultProfile;
    //     this.activityForm.name = defaultProfile.name;
    //     this.activityForm.phone = defaultProfile.phone;
    //   }
    // },
    // 新增：选择报名资料
    selectProfile(profile) {
      // 查找对应的索引
      this.selectedProfileIndex = this.profileList.findIndex(item => item.phone === profile.phone);
      this.selectedProfile = profile;
    },
    // 新增：跳转到管理报名资料页面
    goToManageProfiles() {
      // 关闭当前弹窗
      this.showProfileSelect = false;
      
      uni.navigateTo({
        url: '/pages/signup/manage'
      });
    },
    // 新增：初始化默认资料
    initDefaultProfile() {
      // 找到默认资料
      const defaultProfile = this.profileList.find(profile => profile.isDefault);
      if (defaultProfile) {
        this.selectedProfile = defaultProfile;
      }
    },
    // 新增：确认选择报名资料
    confirmProfileSelection() {
      if (this.selectedProfileIndex === -1) {
        uni.showToast({
          title: this.lang === 'zh' ? '请选择报名资料' : 'Please select a profile',
          icon: 'none'
        });
        return;
      }
      
      // 确认选择并关闭弹窗
      this.showProfileSelect = false;
    }
  }
}
</script>

<style scoped lang="scss">
.popup-content {
  padding: 30rpx;
  .popup-header {
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #303133;
    margin-bottom: 30rpx;
  }
  .profile-list {
    max-height: 600rpx;
  }
  .profile-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
    border-bottom: 1rpx solid #f2f2f2;
    
    &.selected {
      background-color: #f5f7fa;
    }
    
    &.default {
      border: 2rpx solid #2979ff;
      border-radius: 8rpx;
      margin-bottom: 10rpx;
    }
  }
  .profile-info {
    display: flex;
    flex-direction: column;
  }
  
  .profile-name {
    font-size: 28rpx;
    color: #303133;
    margin-bottom: 8rpx;
  }
  
  .profile-phone {
    font-size: 24rpx;
    color: #909399;
  }
  
  .popup-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 30rpx;
  }
  
  .cancel-button {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background-color: #f2f2f2;
    color: #606266;
    border-radius: 8rpx;
    margin-right: 20rpx;
    font-size: 28rpx;
  }.confirm-button {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background-color: #2979ff;
    color: #ffffff;
    border-radius: 8rpx;
    margin-left: 20rpx;
    font-size: 28rpx;
  }
}
.exchange-page {
  min-height: 100vh;
  box-sizing: border-box;
  padding-bottom: 100rpx;
  background-color: #f5f7fa;
}
.points-info {
  background-color: #4285f4;
  color: #fff;
  padding: 40rpx 30rpx;
  text-align: center;
}
.points-title {
  font-size: 30rpx;
  margin-bottom: 16rpx;
}
.points-value {
  font-size: 72rpx;
  font-weight: bold;
}
.exchange-tabs {
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
}
.exchange-list {
  padding: 0;
  background-color: #f5f7fa;
}
.exchange-item {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding: 30rpx;
  margin-top: 20rpx;
  border-radius: 0;
}
.item-main {
  display: flex;
  margin-bottom: 16rpx;
  position: relative;
}
.item-image {
  margin-right: 0;
  border-radius: 8rpx;
  overflow: hidden;
  width: 200rpx;
  height: 160rpx;
  flex-shrink: 0;
}
.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-left: 30rpx;
  position: relative;
}
.item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.item-desc {
  font-size: 26rpx;
  color: #666;
}
.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 30rpx;
  border-top: 1px solid #f5f5f5;
  height: 50rpx;
}
.item-points {
  font-size: 32rpx;
  color: #ff9900;
  font-weight: bold;
  line-height: 1;
}
.exchange-btn {
  background-color: #4285f4;
  color: #fff;
  padding: 15rpx 24rpx;
  border-radius: 6rpx;
  font-size: 26rpx;
  text-align: center;
  width: 160rpx;
  line-height: 1.4;
}
.empty-tip {
  padding: 100rpx 0;
}
.detail-popup {
  width: 600rpx;
  padding: 30rpx;
  box-sizing: border-box;
}
.detail-header, .form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.detail-title, .form-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.detail-content, .form-content {
  margin-bottom: 30rpx;
}
.detail-item {
  display: flex;
  margin-top: 20rpx;
}
.detail-item:first-child {
  margin-top: 0;
}
.detail-label, .form-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
}
.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.detail-footer, .form-footer {
  margin-top: 40rpx;
}
.form-popup {
  width: 650rpx;
  padding: 30rpx;
  box-sizing: border-box;
}
.form-item {
  margin-bottom: 30rpx;
}
.form-tips {
  font-size: 24rpx;
  color: #ff9900;
  text-align: center;
  margin-top: 20rpx;
}
.profile-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1px solid #eee;
}
.profile-selector .placeholder {
  color: #999;
  font-size: 28rpx;
}
.profile-selector-new {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}
.profile-selector-left {
  display: none;
}

.profile-info-header {
  display: none;
}

.profile-info-name {
  display: none;
}

.profile-info-tag {
  display: none;
}

.profile-card-item {
  display: none;
}

.profile-card-text {
  display: none;
}

.profile-popup {
  width: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  box-sizing: border-box;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
}

.profile-popup-header {
  text-align: center;
  padding-bottom: 20rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.profile-list {
  max-height: 60vh;
  overflow-y: auto;
}

.profile-select-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #f2f2f2;
}

.profile-select-item.profile-selected {
  background-color: #f0f7ff;
  border: 2rpx solid #4285f4;
  border-radius: 12rpx;
  padding: 30rpx 20rpx;
  margin: 10rpx 0;
}

.profile-select-info {
  flex: 1;
}

.profile-select-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.profile-select-phone {
  font-size: 28rpx;
  color: #999;
}

.profile-popup-buttons {
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0;
}

.profile-cancel-button, .profile-confirm-button {
  width: 320rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 32rpx;
}

.profile-cancel-button {
  background-color: #f5f5f5;
  color: #333;
}

.profile-confirm-button {
  background-color: #4285f4;
  color: #fff;
}

.manage-profiles-link {
  text-align: center;
  font-size: 28rpx;
  color: #2979ff;
  margin-top: 20rpx;
  padding-bottom: 20rpx;
}
.selected-profile-info-new {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx 30rpx;
  margin-top: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.profile-info-header {
  display: none;
}

.profile-info-name {
  display: none;
}

.profile-info-tag {
  display: none;
}

.profile-card-item {
  display: none;
}

.profile-card-text {
  display: none;
}

.form-link {
  font-size: 26rpx;
  color: #4285f4;
  text-align: center;
  margin-top: 20rpx;
}
.form-section {
  margin-bottom: 20rpx;
}

.form-section-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.profile-item-new.selected {
  background-color: #ecf5ff;
}

.profile-item-new.default {
  border-left: 8rpx solid #4285f4;
}

.selected-profile-info-new {
  border-left: 8rpx solid #4285f4;
}

.form-tips {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fffbe6;
  border-radius: 8rpx;
  margin-top: 20rpx;
}

.form-tips text {
  font-size: 28rpx;
  color: #ff9900;
  flex: 1;
}

.profile-item-row {
  display: flex;
  padding: 10rpx 0;
}

.profile-item-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #666;
}

.profile-item-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
</style> 