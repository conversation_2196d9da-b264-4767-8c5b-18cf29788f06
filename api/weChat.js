import { http } from '@/utils/http.js';

export function postSmallProgramAuth(code) {
  return http({
    url: '/WeChat/SmallProgramAuth',
    method: 'POST',
    data: {
      code: code
    }
  });
}

// 微信登录主函数 - 封装所有登录逻辑
export async function weChatLogin() {
  try {
    // 检查本地是否已有 token
    const token = uni.getStorageSync('token');
    if (token) {
      console.log('已有 token，无需重新登录');
      return { success: true, message: '已登录', hasToken: true };
    }

    console.log('开始微信小程序授权...');
    
    // 获取微信登录凭证
    const loginRes = await getWxLogin();
    if (!loginRes || !loginRes.code) {
      return { success: false, message: '获取微信授权失败' };
    }

    // 调用微信小程序授权接口
    const response = await postSmallProgramAuth(loginRes.code);
    
    if (response && response.code === 0 && response.data) {
      // 登录成功，保存 token
      uni.setStorageSync('token', response.data);
      console.log('微信登录成功，token已保存');
      return { success: true, message: '登录成功', token: response.data };
    } else {
      console.error('微信登录失败:', response);
      return { success: false, message: '登录失败，请重试' };
    }
  } catch (error) {
    console.error('微信登录异常:', error);
    return { success: false, message: '登录异常，请检查网络' };
  }
}

// 获取微信登录凭证
function getWxLogin() {
  return new Promise((resolve, reject) => {
    uni.login({
      provider: 'weixin',
      success: (loginRes) => {
        console.log('微信登录凭证获取成功:', loginRes);
        resolve(loginRes);
      },
      fail: (error) => {
        console.error('微信登录凭证获取失败:', error);
        reject(error);
      }
    });
  });
}
