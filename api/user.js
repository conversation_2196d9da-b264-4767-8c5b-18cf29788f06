import { http } from '@/utils/http.js';

// 编辑资料
export function postUpdateMe(data) {
  return http({
    url: '/User/UpdateMe',
    method: 'POST',
    data: data
  });
}

// 获取用户信息
export function postUserInfo(data) {
    return http({
      url: '/User/GetUserInfo',
      method: 'POST',
      data: data
    });
  }

// ==================== 个人报名资料维护 API ====================

// 添加报名资料
export function addUserInfoReg(data) {
  return http({
    url: '/User/AddUserInfoReg',
    method: 'POST',
    data: data
  });
}

// 修改报名资料
export function updateUserInfoReg(data) {
  return http({
    url: '/User/UpdateUserInfoReg',
    method: 'POST',
    data: data
  });
}

// 删除报名资料
export function delUserInfoReg(data) {
  return http({
    url: '/User/DelUserInfoReg',
    method: 'POST',
    data: data
  });
}

// 获取报名资料列表
export function getUserInfoRegList(data) {
  return http({
    url: '/User/GetUserInfoRegList',
    method: 'POST',
    data: data
  });
}

// ==================== 活动列表 API ====================

// 获取活动列表
export function getOperationList(data) {
  return http({
    url: '/User/GetOperationList',
    method: 'POST',
    data: data
  });
}
