{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/signup/manage.vue?7f13", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/signup/manage.vue?1e41", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/signup/manage.vue?2d9e", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/signup/manage.vue?da06", "uni-app:///pages/signup/manage.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/signup/manage.vue?b2bc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uIcon", "uPopup", "uInput", "uCheckbox", "uButton", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "l0", "_self", "_c", "__map", "profileList", "item", "index", "$orig", "__get_orig", "m0", "idCard", "formatIdCard", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "i", "LangSwitch", "lang", "showForm", "isEdit", "currentIndex", "formData", "name", "phone", "email", "school", "address", "isDefault", "isDefaultDisabled", "computed", "hasDefaultProfile", "onReady", "mounted", "uni", "<PERSON><PERSON><PERSON><PERSON>", "methods", "onLangChange", "setNavTitle", "title", "getProfileList", "pg", "size", "response", "id", "icon", "addProfile", "editProfile", "closeForm", "saveProfile", "apiData", "userName", "mobile", "confirmDelete", "content", "success", "deleteProfile", "profile"], "mappings": "8IAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,0BACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,MAAO,WACL,OAAO,kHAITC,OAAQ,WACN,OAAO,oHAITC,OAAQ,WACN,OAAO,oHAITC,UAAW,WACT,OAAO,0HAITC,QAAS,WACP,OAAO,uHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,MAAMP,EAAIQ,aAAa,SAAUC,EAAMC,GAClD,IAAIC,EAAQX,EAAIY,WAAWH,GACvBI,EAAKJ,EAAKK,OAASd,EAAIe,aAAaN,EAAKK,QAAU,KACvD,MAAO,CACLH,MAAOA,EACPE,GAAIA,OAGRb,EAAIgB,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLhB,GAAIA,MAKRiB,GAAmB,EACnBC,EAAkB,GACtBvB,EAAOwB,eAAgB,G,iCCrEvB,yHAAi4B,eAAG,G,gKC4Hp4B,6PAAAC,EAAA,EAAAA,EAAA,iBAAAA,IAAA,uBAAAA,GAAA,UAAAA,GAAA,GAAAA,EAAA,mYAEA,CACApC,YACAqC,cAEAR,gBACA,OACAS,UACAlB,eACAmB,YACAC,UACAC,gBACAC,UACAC,QACAC,SACAlB,UACAmB,SACAC,UACAC,WACAC,cAEAC,uBAGAC,UACAC,6BACA,kEAGAC,qBAGAC,mBAEA,qCACA,YAGAC,uCAGA,mBAGA,uBAEAC,yBACAD,yCAEAE,SACAC,yBACA,YACA,oBAEAC,uBACA,oDACAJ,yBAAAK,WAEAC,0BAAA,0IAIA,OAJA,SAEAN,eACAK,4CACA,UAEA,yBACAE,KACAC,WACA,OAHAC,SAKAtD,6BAEA,sBAEA,6CACAuD,QACArB,oBACAC,mBACAlB,oBACAmB,kBACAC,oBACAC,sBACAC,+BAGAvC,+BACA6C,aACAK,mDACAM,eAEA,mDAEAxD,kCACA6C,aACAK,2CACAM,cACA,QAEA,OAFA,UAEAX,gBAAA,2EAvCA,IA0CA3B,yBACA,SAEA,wDAFA,IAIAuC,sBACA,eACA,qBAGA,+DAEA,eACAvB,QACAC,SACAlB,UACAmB,SACAC,UACAC,WACAC,cAIA,0BAEA,kBAEAmB,wBACA,eACA,oBACA,0BACA,sBAIA,mCAEA,kBAEAC,qBACA,kBAEAC,uBAAA,iJAEA,yDACAf,aACAK,gDACAM,eACA,UAEA,0DACAX,aACAK,kDACAM,eACA,OAgCA,GA5BAX,eACAK,2CACA,SAIA,qBAEA,mCACAtC,kBAEA,kDAEA,wBACA,6FAEA,yBAIAiD,GACAC,yBACAC,wBACA9C,6BACAmB,2BACAC,6BACAC,+BACAC,qCAIA,0BAEA,OAAAsB,mBAAA,WACA,kCAAAP,SAAA,0CAGA,+BAAAA,SAAA,QAGAtD,2BAEA,eACA6C,aACAK,gDACAM,iBAIA,cAGA,oBAEAX,aACAK,oFACAM,cAEA,qDAEAxD,gCACA6C,aACAK,2CACAM,cACA,QAEA,OAFA,UAEAX,gBAAA,4EAlFA,IAqFAmB,0BAAA,WACAnB,aACAK,+CACAe,wFACAC,oBACA,WACA,uBAKAC,0BAAA,4IAMA,OALAC,mBAAA,SAGAvB,eACAK,6CACA,UAEA,qBACAK,UACA,OAFAD,SAIAtD,2BAEA,eACA6C,aACAK,kDACAM,iBAIA,oBAEAX,aACAK,kFACAM,cAEA,qDAEAxD,gCACA6C,aACAK,2CACAM,cACA,QAEA,OAFA,UAEAX,gBAAA,4EAnCA,MAuCA,c,6DClZA,yHAAwxC,eAAG,G", "file": "pages/signup/manage.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/signup/manage.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./manage.vue?vue&type=template&id=28aa4093&scoped=true&\"\nvar renderjs\nimport script from \"./manage.vue?vue&type=script&lang=js&\"\nexport * from \"./manage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./manage.vue?vue&type=style&index=0&id=28aa4093&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"28aa4093\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/signup/manage.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./manage.vue?vue&type=template&id=28aa4093&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-input/u-input\" */ \"uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-checkbox/u-checkbox\" */ \"uview-ui/components/u-checkbox/u-checkbox.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-button/u-button\" */ \"uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.profileList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = item.idCard ? _vm.formatIdCard(item.idCard) : null\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./manage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./manage.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"signup-manage-page\">\n    <!-- 顶部提示 -->\n    <view class=\"tip-bar\">\n      <u-icon name=\"info-circle\" size=\"28\" color=\"#4285f4\"></u-icon>\n      <text class=\"tip-text\">{{ lang === 'zh' ? '报名资料用于活动报名，可添加多个资料' : 'Registration info for activities, you can add multiple profiles' }}</text>\n    </view>\n    \n    <!-- 资料列表 -->\n    <view class=\"profile-list\">\n      <view \n        class=\"profile-item\" \n        v-for=\"(item, index) in profileList\" \n        :key=\"index\" \n        @click=\"editProfile(index)\"\n        :class=\"{'default-profile': item.isDefault}\"\n      >\n        <view class=\"profile-main\">\n          <view class=\"profile-info\">\n            <view class=\"profile-name\">\n              {{ item.name }}\n              <text class=\"default-tag\" v-if=\"item.isDefault\">{{ lang === 'zh' ? '默认' : 'Default' }}</text>\n            </view>\n            <view class=\"profile-phone\">{{ item.phone }}</view>\n          </view>\n          <view class=\"profile-actions\">\n            <view class=\"action-btn edit-btn\" @click.stop=\"editProfile(index)\">\n              <u-icon name=\"edit-pen\" size=\"24\" color=\"#4285f4\"></u-icon>\n            </view>\n            <view class=\"action-btn delete-btn\" @click.stop=\"confirmDelete(index)\">\n              <u-icon name=\"trash\" size=\"24\" color=\"#ff5252\"></u-icon>\n            </view>\n          </view>\n        </view>\n        <view class=\"profile-detail\">\n          <view class=\"detail-item\" v-if=\"item.idCard\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '身份证' : 'ID Card' }}:</text>\n            <text class=\"detail-value\">{{ formatIdCard(item.idCard) }}</text>\n          </view>\n          <view class=\"detail-item\" v-if=\"item.email\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '邮箱' : 'Email' }}:</text>\n            <text class=\"detail-value\">{{ item.email }}</text>\n          </view>\n          <view class=\"detail-item\" v-if=\"item.school\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '学校' : 'School' }}:</text>\n            <text class=\"detail-value\">{{ item.school }}</text>\n          </view>\n          <view class=\"detail-item\" v-if=\"item.address\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '地址' : 'Address' }}:</text>\n            <text class=\"detail-value\">{{ item.address }}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 添加按钮 -->\n    <view class=\"add-btn\" @click=\"addProfile\">\n      <text>{{ lang === 'zh' ? '添加报名资料' : 'Add Profile' }}</text>\n    </view>\n    \n    <!-- 编辑/添加弹窗 -->\n    <u-popup :show=\"showForm\" mode=\"bottom\" @close=\"closeForm\" :safeAreaInsetBottom=\"true\" round=\"16\">\n      <view class=\"form-popup\">\n        <view class=\"form-header\">\n          <text class=\"form-title\">{{ isEdit ? (lang === 'zh' ? '编辑资料' : 'Edit Profile') : (lang === 'zh' ? '添加资料' : 'Add Profile') }}</text>\n          <u-icon name=\"close\" size=\"24\" color=\"#999\" @click=\"closeForm\"></u-icon>\n        </view>\n        \n        <scroll-view scroll-y class=\"form-scroll\">\n          <view class=\"form-content\">\n            <view class=\"form-item\">\n              <text class=\"form-label required\">{{ lang === 'zh' ? '姓名' : 'Name' }}</text>\n              <u-input v-model=\"formData.name\" :placeholder=\"lang === 'zh' ? '请输入姓名' : 'Enter name'\" border=\"bottom\"></u-input>\n            </view>\n            \n            <view class=\"form-item\">\n              <text class=\"form-label required\">{{ lang === 'zh' ? '手机号' : 'Phone' }}</text>\n              <u-input v-model=\"formData.phone\" :placeholder=\"lang === 'zh' ? '请输入手机号' : 'Enter phone'\" border=\"bottom\" type=\"number\"></u-input>\n            </view>\n            \n            <view class=\"form-item\">\n              <text class=\"form-label\">{{ lang === 'zh' ? '身份证号' : 'ID Card' }}</text>\n              <u-input v-model=\"formData.idCard\" :placeholder=\"lang === 'zh' ? '请输入身份证号' : 'Enter ID card'\" border=\"bottom\"></u-input>\n            </view>\n            \n            <view class=\"form-item\">\n              <text class=\"form-label\">{{ lang === 'zh' ? '邮箱' : 'Email' }}</text>\n              <u-input v-model=\"formData.email\" :placeholder=\"lang === 'zh' ? '请输入邮箱' : 'Enter email'\" border=\"bottom\"></u-input>\n            </view>\n            \n            <view class=\"form-item\">\n              <text class=\"form-label\">{{ lang === 'zh' ? '学校' : 'School' }}</text>\n              <u-input v-model=\"formData.school\" :placeholder=\"lang === 'zh' ? '请输入学校' : 'Enter school'\" border=\"bottom\"></u-input>\n            </view>\n            \n            <view class=\"form-item\">\n              <text class=\"form-label\">{{ lang === 'zh' ? '地址' : 'Address' }}</text>\n              <u-input v-model=\"formData.address\" :placeholder=\"lang === 'zh' ? '请输入地址' : 'Enter address'\" border=\"bottom\"></u-input>\n            </view>\n            \n            <view class=\"form-item\">\n              <view class=\"checkbox-item\">\n                <u-checkbox v-model=\"formData.isDefault\" :disabled=\"isDefaultDisabled\" activeColor=\"#4285f4\"></u-checkbox>\n                <text class=\"checkbox-text\">{{ lang === 'zh' ? '设为默认资料' : 'Set as default' }}</text>\n              </view>\n            </view>\n          </view>\n        </scroll-view>\n        \n        <view class=\"form-footer\">\n          <u-button type=\"primary\" @click=\"saveProfile\">\n            {{ lang === 'zh' ? '保存' : 'Save' }}\n          </u-button>\n        </view>\n      </view>\n    </u-popup>\n    \n    <!-- 语言切换 -->\n    <LangSwitch />\n  </view>\n</template>\n\n<script>\nimport LangSwitch from '@/components/LangSwitch.vue';\nimport { addUserInfoReg, updateUserInfoReg, delUserInfoReg, getUserInfoRegList } from '@/api/user.js';\n\nexport default {\n  components: {\n    LangSwitch\n  },\n  data() {\n    return {\n      lang: 'zh',\n      profileList: [],\n      showForm: false,\n      isEdit: false,\n      currentIndex: -1,\n      formData: {\n        name: '',\n        phone: '',\n        idCard: '',\n        email: '',\n        school: '',\n        address: '',\n        isDefault: false\n      },\n      isDefaultDisabled: false\n    };\n  },\n  computed: {\n    hasDefaultProfile() {\n      return this.profileList.some(item => item.isDefault);\n    }\n  },\n  onReady() {\n    // 在页面渲染完成后初始化\n  },\n  mounted() {\n    // 获取语言设置\n    const lang = uni.getStorageSync('lang') || 'zh';\n    this.lang = lang;\n    \n    // 监听语言变化\n    uni.$on('lang-change', this.onLangChange);\n    \n    // 设置导航栏标题\n    this.setNavTitle();\n    \n    // 获取报名资料列表\n    this.getProfileList();\n  },\n  beforeDestroy() {\n    uni.$off('lang-change', this.onLangChange);\n  },\n  methods: {\n    onLangChange(newLang) {\n      this.lang = newLang;\n      this.setNavTitle();\n    },\n    setNavTitle() {\n      const title = this.lang === 'zh' ? '报名资料管理' : 'Registration Info';\n      uni.setNavigationBarTitle({ title });\n    },\n    async getProfileList() {\n      try {\n        uni.showLoading({\n          title: this.lang === 'zh' ? '加载中...' : 'Loading...'\n        });\n\n        const response = await getUserInfoRegList({\n          pg: 1,\n          size: 100 // 获取所有数据\n        });\n\n        console.log('获取报名资料列表响应:', response);\n\n        if (response && response.code === 0 && response.data) {\n          // 将API返回的数据转换为页面需要的格式\n          this.profileList = response.data.map(item => ({\n            id: item.id,\n            name: item.userName || '',\n            phone: item.mobile || '',\n            idCard: item.idCard || '',\n            email: item.email || '',\n            school: item.school || '',\n            address: item.address || '',\n            isDefault: item.isDefault === 1\n          }));\n        } else {\n          console.error('获取报名资料列表失败:', response);\n          uni.showToast({\n            title: this.lang === 'zh' ? '获取数据失败' : 'Failed to load data',\n            icon: 'none'\n          });\n        }\n      } catch (error) {\n        console.error('获取报名资料列表异常:', error);\n        uni.showToast({\n          title: this.lang === 'zh' ? '网络错误' : 'Network error',\n          icon: 'none'\n        });\n      } finally {\n        uni.hideLoading();\n      }\n    },\n    formatIdCard(idCard) {\n      if (!idCard) return '';\n      // 保留前4位和后4位，中间用*代替\n      return idCard.substring(0, 4) + '************' + idCard.substring(idCard.length - 4);\n    },\n    addProfile() {\n      this.isEdit = false;\n      this.currentIndex = -1;\n      \n      // 检查是否已有默认资料\n      const hasDefault = this.profileList.some(item => item.isDefault);\n      \n      this.formData = {\n        name: '',\n        phone: '',\n        idCard: '',\n        email: '',\n        school: '',\n        address: '',\n        isDefault: !hasDefault // 如果没有默认资料，则自动设为默认\n      };\n      \n      // 如果没有默认资料，则不能取消默认选项\n      this.isDefaultDisabled = !hasDefault;\n      \n      this.showForm = true;\n    },\n    editProfile(index) {\n      this.isEdit = true;\n      this.currentIndex = index;\n      const profile = this.profileList[index];\n      this.formData = { ...profile };\n      \n      // 修改逻辑：如果当前编辑的是默认资料，则禁用复选框（不能取消默认）\n      // 如果不是默认资料，且已有其他默认资料，则不禁用（可以切换默认）\n      this.isDefaultDisabled = profile.isDefault;\n      \n      this.showForm = true;\n    },\n    closeForm() {\n      this.showForm = false;\n    },\n    async saveProfile() {\n      // 表单验证\n      if (!this.formData.name) {\n        return uni.showToast({\n          title: this.lang === 'zh' ? '请输入姓名' : 'Please enter name',\n          icon: 'none'\n        });\n      }\n      if (!this.formData.phone) {\n        return uni.showToast({\n          title: this.lang === 'zh' ? '请输入手机号' : 'Please enter phone',\n          icon: 'none'\n        });\n      }\n\n      // 显示加载中\n      uni.showLoading({\n        title: this.lang === 'zh' ? '保存中...' : 'Saving...'\n      });\n\n      try {\n        // 处理默认资料逻辑\n        if (this.formData.isDefault) {\n          // 如果当前资料设为默认，则其他资料取消默认\n          this.profileList.forEach(item => {\n            item.isDefault = false;\n          });\n        } else if (this.isEdit && this.profileList[this.currentIndex].isDefault) {\n          // 如果正在编辑的是默认资料，但取消了默认，则强制保持默认\n          this.formData.isDefault = true;\n        } else if (!this.profileList.some(item => item.isDefault) && (this.profileList.length === 0 || this.isEdit)) {\n          // 如果没有默认资料，则当前资料设为默认\n          this.formData.isDefault = true;\n        }\n\n        // 准备API数据\n        const apiData = {\n          userName: this.formData.name,\n          mobile: this.formData.phone,\n          idCard: this.formData.idCard || '',\n          email: this.formData.email || '',\n          school: this.formData.school || '',\n          address: this.formData.address || '',\n          isDefault: this.formData.isDefault ? 1 : 0\n        };\n\n        let response;\n        if (this.isEdit) {\n          // 修改资料\n          apiData.id = this.formData.id;\n          response = await updateUserInfoReg(apiData);\n        } else {\n          // 添加资料\n          response = await addUserInfoReg(apiData);\n        }\n\n        console.log('保存报名资料响应:', response);\n\n        if (response && response.code === 0) {\n          uni.showToast({\n            title: this.lang === 'zh' ? '保存成功' : 'Saved successfully',\n            icon: 'success'\n          });\n\n          // 关闭弹窗\n          this.closeForm();\n\n          // 重新获取列表\n          this.getProfileList();\n        } else {\n          uni.showToast({\n            title: response?.msg || (this.lang === 'zh' ? '保存失败' : 'Save failed'),\n            icon: 'none'\n          });\n        }\n      } catch (error) {\n        console.error('保存报名资料异常:', error);\n        uni.showToast({\n          title: this.lang === 'zh' ? '网络错误' : 'Network error',\n          icon: 'none'\n        });\n      } finally {\n        uni.hideLoading();\n      }\n    },\n    confirmDelete(index) {\n      uni.showModal({\n        title: this.lang === 'zh' ? '确认删除' : 'Confirm Delete',\n        content: this.lang === 'zh' ? '确定要删除该报名资料吗？' : 'Are you sure you want to delete this profile?',\n        success: (res) => {\n          if (res.confirm) {\n            this.deleteProfile(index);\n          }\n        }\n      });\n    },\n    async deleteProfile(index) {\n      const profile = this.profileList[index];\n\n      try {\n        uni.showLoading({\n          title: this.lang === 'zh' ? '删除中...' : 'Deleting...'\n        });\n\n        const response = await delUserInfoReg({\n          id: profile.id\n        });\n\n        console.log('删除报名资料响应:', response);\n\n        if (response && response.code === 0) {\n          uni.showToast({\n            title: this.lang === 'zh' ? '删除成功' : 'Deleted successfully',\n            icon: 'success'\n          });\n\n          // 重新获取列表\n          this.getProfileList();\n        } else {\n          uni.showToast({\n            title: response?.msg || (this.lang === 'zh' ? '删除失败' : 'Delete failed'),\n            icon: 'none'\n          });\n        }\n      } catch (error) {\n        console.error('删除报名资料异常:', error);\n        uni.showToast({\n          title: this.lang === 'zh' ? '网络错误' : 'Network error',\n          icon: 'none'\n        });\n      } finally {\n        uni.hideLoading();\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.signup-manage-page {\n  min-height: 100vh;\n  box-sizing: border-box;\n  padding-bottom: calc(120rpx + constant(safe-area-inset-bottom)); /* iOS 11.2 以下 */\n  padding-bottom: calc(120rpx + env(safe-area-inset-bottom)); /* iOS 11.2 及以上 */\n  background-color: #f5f7fa;\n}\n.tip-bar {\n  display: flex;\n  align-items: center;\n  background-color: #e6f2ff;\n  padding: 20rpx 30rpx;\n}\n.tip-text {\n  font-size: 26rpx;\n  color: #4285f4;\n  margin-left: 10rpx;\n}\n.profile-list {\n  padding: 20rpx;\n}\n.profile-item {\n  background-color: #fff;\n  border-radius: 12rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  position: relative;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);\n  border: 2rpx solid transparent; /* 添加透明边框，避免选中时的跳动 */\n}\n.profile-item.default-profile {\n  border: 2rpx solid #4285f4; /* 默认资料的边框 */\n}\n.profile-main {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20rpx;\n}\n.profile-info {\n  flex: 1;\n}\n.profile-name {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n.profile-name .default-tag {\n  background-color: #4285f4;\n  color: #fff;\n  font-size: 22rpx;\n  padding: 6rpx 16rpx;\n  border-radius: 0 12rpx 0 12rpx;\n  margin-left: 10rpx;\n}\n.profile-phone {\n  font-size: 28rpx;\n  color: #666;\n}\n.profile-actions {\n  display: flex;\n}\n.action-btn {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: 20rpx;\n}\n.profile-detail {\n  background-color: #f9f9f9;\n  border-radius: 8rpx;\n  padding: 20rpx;\n}\n.detail-item {\n  display: flex;\n  margin-bottom: 10rpx;\n}\n.detail-item:last-child {\n  margin-bottom: 0;\n}\n.detail-label {\n  width: 120rpx;\n  font-size: 26rpx;\n  color: #999;\n}\n.detail-value {\n  flex: 1;\n  font-size: 26rpx;\n  color: #666;\n}\n.add-btn {\n  position: fixed;\n  bottom: calc(40rpx + constant(safe-area-inset-bottom)); /* iOS 11.2 以下 */\n  bottom: calc(40rpx + env(safe-area-inset-bottom)); /* iOS 11.2 及以上 */\n  left: 50%;\n  transform: translateX(-50%);\n  background-color: #4285f4;\n  color: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 90%;\n  height: 80rpx;\n  border-radius: 40rpx;\n  box-shadow: 0 4rpx 12rpx rgba(66,133,244,0.3);\n  z-index: 9;\n}\n.add-btn text {\n  margin-left: 10rpx;\n  font-size: 28rpx;\n}\n.form-popup {\n  padding: 30rpx;\n  box-sizing: border-box;\n  height: 80vh;\n  display: flex;\n  flex-direction: column;\n}\n.form-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n.form-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n.form-scroll {\n  flex: 1;\n  height: 0;\n}\n.form-content {\n  padding-bottom: 30rpx;\n}\n.form-item {\n  margin-bottom: 30rpx;\n}\n.form-label {\n  font-size: 28rpx;\n  color: #666;\n  margin-bottom: 10rpx;\n  display: block;\n}\n.required::before {\n  content: '*';\n  color: #ff5252;\n  margin-right: 6rpx;\n}\n.checkbox-item {\n  display: flex;\n  align-items: center;\n}\n.checkbox-text {\n  font-size: 28rpx;\n  color: #666;\n  margin-left: 10rpx;\n}\n.form-footer {\n  padding-top: 20rpx;\n  border-top: 1px solid #eee;\n}\n</style> ", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./manage.vue?vue&type=style&index=0&id=28aa4093&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./manage.vue?vue&type=style&index=0&id=28aa4093&scoped=true&lang=css&\""], "sourceRoot": ""}