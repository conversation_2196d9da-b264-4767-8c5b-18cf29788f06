{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/signup/manage.vue?7f13", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/signup/manage.vue?1e41", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/signup/manage.vue?2d9e", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/signup/manage.vue?da06", "uni-app:///pages/signup/manage.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/signup/manage.vue?b2bc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uIcon", "uPopup", "uInput", "uCheckbox", "uButton", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "l0", "_self", "_c", "__map", "profileList", "item", "index", "$orig", "__get_orig", "m0", "idCard", "formatIdCard", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "LangSwitch", "lang", "name", "phone", "email", "school", "address", "isDefault", "showForm", "isEdit", "currentIndex", "formData", "isDefaultDisabled", "computed", "hasDefaultProfile", "onReady", "mounted", "uni", "<PERSON><PERSON><PERSON><PERSON>", "methods", "onLangChange", "setNavTitle", "title", "getProfileList", "addProfile", "editProfile", "closeForm", "saveProfile", "setTimeout", "icon", "confirmDelete", "content", "success", "deleteProfile"], "mappings": "8IAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,0BACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,MAAO,WACL,OAAO,kHAITC,OAAQ,WACN,OAAO,oHAITC,OAAQ,WACN,OAAO,oHAITC,UAAW,WACT,OAAO,0HAITC,QAAS,WACP,OAAO,uHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,MAAMP,EAAIQ,aAAa,SAAUC,EAAMC,GAClD,IAAIC,EAAQX,EAAIY,WAAWH,GACvBI,EAAKJ,EAAKK,OAASd,EAAIe,aAAaN,EAAKK,QAAU,KACvD,MAAO,CACLH,MAAOA,EACPE,GAAIA,OAGRb,EAAIgB,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLhB,GAAIA,MAKRiB,GAAmB,EACnBC,EAAkB,GACtBvB,EAAOwB,eAAgB,G,iCCrEvB,yHAAi4B,eAAG,G,w0BC6Hp4B,CACAnC,YACAoC,cAEAP,gBACA,OACAQ,UACAjB,aACA,CACAkB,UACAC,oBACAb,4BACAc,6BACAC,cACAC,yBACAC,cAEA,CACAL,UACAC,oBACAb,4BACAc,yBACAC,gBACAC,uBACAC,cAEA,CACAL,UACAC,oBACAb,4BACAc,2BACAC,cACAC,uBACAC,cAEA,CACAL,UACAC,oBACAb,4BACAc,4BACAC,cACAC,sBACAC,cAEA,CACAL,UACAC,oBACAb,4BACAc,2BACAC,cACAC,wBACAC,cAEA,CACAL,UACAC,oBACAb,4BACAc,0BACAC,cACAC,4BACAC,cAEA,CACAL,UACAC,oBACAb,4BACAc,4BACAC,cACAC,4BACAC,eAGAC,YACAC,UACAC,gBACAC,UACAT,QACAC,SACAb,UACAc,SACAC,UACAC,WACAC,cAEAK,uBAGAC,UACAC,6BACA,kEAGAC,qBAGAC,mBAEA,qCACA,YAGAC,uCAGA,mBAGA,uBAEAC,yBACAD,yCAEAE,SACAC,yBACA,YACA,oBAEAC,uBACA,oDACAJ,yBAAAK,WAEAC,0BAGAlD,iDAEAkB,yBACA,SAEA,wDAFA,IAIAiC,sBACA,eACA,qBAGA,+DAEA,eACAtB,QACAC,SACAb,UACAc,SACAC,UACAC,WACAC,cAIA,0BAEA,kBAEAkB,wBACA,eACA,oBACA,0BACA,sBAIA,mCAEA,kBAEAC,qBACA,kBAEAC,uBAAA,WAEA,0BAMA,qBAQAV,eACAK,8CAIA,wBAEA,sCACArC,kBAEA,2DAIA,2BACA,sGAEA,4BAIA,YACA,wDAEA,gDAIA2C,uBACAX,gBACAA,aACAK,gDACAO,iBAIA,gBACA,MA5CA,aACAP,qDACAO,cARA,aACAP,mDACAO,eAkDAC,0BAAA,WACAb,aACAK,+CACAS,wFACAC,oBACA,WACA,uBAKAC,0BAAA,WACA,gCAGA,6BAGA,+BACA,kCAIAL,uBACAX,aACAK,kDACAO,mBAEA,QAGA,c,6DC3XA,yHAAwxC,eAAG,G", "file": "pages/signup/manage.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/signup/manage.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./manage.vue?vue&type=template&id=28aa4093&scoped=true&\"\nvar renderjs\nimport script from \"./manage.vue?vue&type=script&lang=js&\"\nexport * from \"./manage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./manage.vue?vue&type=style&index=0&id=28aa4093&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"28aa4093\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/signup/manage.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./manage.vue?vue&type=template&id=28aa4093&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-input/u-input\" */ \"uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-checkbox/u-checkbox\" */ \"uview-ui/components/u-checkbox/u-checkbox.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-button/u-button\" */ \"uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.profileList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = item.idCard ? _vm.formatIdCard(item.idCard) : null\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./manage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./manage.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"signup-manage-page\">\n    <!-- 顶部提示 -->\n    <view class=\"tip-bar\">\n      <u-icon name=\"info-circle\" size=\"28\" color=\"#4285f4\"></u-icon>\n      <text class=\"tip-text\">{{ lang === 'zh' ? '报名资料用于活动报名，可添加多个资料' : 'Registration info for activities, you can add multiple profiles' }}</text>\n    </view>\n    \n    <!-- 资料列表 -->\n    <view class=\"profile-list\">\n      <view \n        class=\"profile-item\" \n        v-for=\"(item, index) in profileList\" \n        :key=\"index\" \n        @click=\"editProfile(index)\"\n        :class=\"{'default-profile': item.isDefault}\"\n      >\n        <view class=\"profile-main\">\n          <view class=\"profile-info\">\n            <view class=\"profile-name\">\n              {{ item.name }}\n              <text class=\"default-tag\" v-if=\"item.isDefault\">{{ lang === 'zh' ? '默认' : 'Default' }}</text>\n            </view>\n            <view class=\"profile-phone\">{{ item.phone }}</view>\n          </view>\n          <view class=\"profile-actions\">\n            <view class=\"action-btn edit-btn\" @click.stop=\"editProfile(index)\">\n              <u-icon name=\"edit-pen\" size=\"24\" color=\"#4285f4\"></u-icon>\n            </view>\n            <view class=\"action-btn delete-btn\" @click.stop=\"confirmDelete(index)\">\n              <u-icon name=\"trash\" size=\"24\" color=\"#ff5252\"></u-icon>\n            </view>\n          </view>\n        </view>\n        <view class=\"profile-detail\">\n          <view class=\"detail-item\" v-if=\"item.idCard\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '身份证' : 'ID Card' }}:</text>\n            <text class=\"detail-value\">{{ formatIdCard(item.idCard) }}</text>\n          </view>\n          <view class=\"detail-item\" v-if=\"item.email\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '邮箱' : 'Email' }}:</text>\n            <text class=\"detail-value\">{{ item.email }}</text>\n          </view>\n          <view class=\"detail-item\" v-if=\"item.school\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '学校' : 'School' }}:</text>\n            <text class=\"detail-value\">{{ item.school }}</text>\n          </view>\n          <view class=\"detail-item\" v-if=\"item.address\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '地址' : 'Address' }}:</text>\n            <text class=\"detail-value\">{{ item.address }}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 添加按钮 -->\n    <view class=\"add-btn\" @click=\"addProfile\">\n      <text>{{ lang === 'zh' ? '添加报名资料' : 'Add Profile' }}</text>\n    </view>\n    \n    <!-- 编辑/添加弹窗 -->\n    <u-popup :show=\"showForm\" mode=\"bottom\" @close=\"closeForm\" :safeAreaInsetBottom=\"true\" round=\"16\">\n      <view class=\"form-popup\">\n        <view class=\"form-header\">\n          <text class=\"form-title\">{{ isEdit ? (lang === 'zh' ? '编辑资料' : 'Edit Profile') : (lang === 'zh' ? '添加资料' : 'Add Profile') }}</text>\n          <u-icon name=\"close\" size=\"24\" color=\"#999\" @click=\"closeForm\"></u-icon>\n        </view>\n        \n        <scroll-view scroll-y class=\"form-scroll\">\n          <view class=\"form-content\">\n            <view class=\"form-item\">\n              <text class=\"form-label required\">{{ lang === 'zh' ? '姓名' : 'Name' }}</text>\n              <u-input v-model=\"formData.name\" :placeholder=\"lang === 'zh' ? '请输入姓名' : 'Enter name'\" border=\"bottom\"></u-input>\n            </view>\n            \n            <view class=\"form-item\">\n              <text class=\"form-label required\">{{ lang === 'zh' ? '手机号' : 'Phone' }}</text>\n              <u-input v-model=\"formData.phone\" :placeholder=\"lang === 'zh' ? '请输入手机号' : 'Enter phone'\" border=\"bottom\" type=\"number\"></u-input>\n            </view>\n            \n            <view class=\"form-item\">\n              <text class=\"form-label\">{{ lang === 'zh' ? '身份证号' : 'ID Card' }}</text>\n              <u-input v-model=\"formData.idCard\" :placeholder=\"lang === 'zh' ? '请输入身份证号' : 'Enter ID card'\" border=\"bottom\"></u-input>\n            </view>\n            \n            <view class=\"form-item\">\n              <text class=\"form-label\">{{ lang === 'zh' ? '邮箱' : 'Email' }}</text>\n              <u-input v-model=\"formData.email\" :placeholder=\"lang === 'zh' ? '请输入邮箱' : 'Enter email'\" border=\"bottom\"></u-input>\n            </view>\n            \n            <view class=\"form-item\">\n              <text class=\"form-label\">{{ lang === 'zh' ? '学校' : 'School' }}</text>\n              <u-input v-model=\"formData.school\" :placeholder=\"lang === 'zh' ? '请输入学校' : 'Enter school'\" border=\"bottom\"></u-input>\n            </view>\n            \n            <view class=\"form-item\">\n              <text class=\"form-label\">{{ lang === 'zh' ? '地址' : 'Address' }}</text>\n              <u-input v-model=\"formData.address\" :placeholder=\"lang === 'zh' ? '请输入地址' : 'Enter address'\" border=\"bottom\"></u-input>\n            </view>\n            \n            <view class=\"form-item\">\n              <view class=\"checkbox-item\">\n                <u-checkbox v-model=\"formData.isDefault\" :disabled=\"isDefaultDisabled\" activeColor=\"#4285f4\"></u-checkbox>\n                <text class=\"checkbox-text\">{{ lang === 'zh' ? '设为默认资料' : 'Set as default' }}</text>\n              </view>\n            </view>\n          </view>\n        </scroll-view>\n        \n        <view class=\"form-footer\">\n          <u-button type=\"primary\" @click=\"saveProfile\">\n            {{ lang === 'zh' ? '保存' : 'Save' }}\n          </u-button>\n        </view>\n      </view>\n    </u-popup>\n    \n    <!-- 语言切换 -->\n    <LangSwitch />\n  </view>\n</template>\n\n<script>\nimport LangSwitch from '@/components/LangSwitch.vue';\n\nexport default {\n  components: {\n    LangSwitch\n  },\n  data() {\n    return {\n      lang: 'zh',\n      profileList: [\n        {\n          name: '张三',\n          phone: '13812345678',\n          idCard: '******************',\n          email: '<EMAIL>',\n          school: '深圳大学',\n          address: '广东省深圳市南山区科技园南区',\n          isDefault: true\n        },\n        {\n          name: '李四',\n          phone: '13987654321',\n          idCard: '******************',\n          email: '<EMAIL>',\n          school: '华南理工大学',\n          address: '广东省广州市天河区五山路',\n          isDefault: false\n        },\n        {\n          name: '王五',\n          phone: '13500001111',\n          idCard: '******************',\n          email: '<EMAIL>',\n          school: '北京大学',\n          address: '北京市海淀区颐和园路5号',\n          isDefault: false\n        },\n        {\n          name: '赵六',\n          phone: '13600002222',\n          idCard: '******************',\n          email: '<EMAIL>',\n          school: '清华大学',\n          address: '北京市海淀区清华园1号',\n          isDefault: false\n        },\n        {\n          name: '钱七',\n          phone: '13700003333',\n          idCard: '******************',\n          email: '<EMAIL>',\n          school: '复旦大学',\n          address: '上海市杨浦区邯郸路220号',\n          isDefault: false\n        },\n        {\n          name: '孙八',\n          phone: '13800004444',\n          idCard: '******************',\n          email: '<EMAIL>',\n          school: '浙江大学',\n          address: '浙江省杭州市西湖区余杭塘路866号',\n          isDefault: false\n        },\n        {\n          name: '周九',\n          phone: '13900005555',\n          idCard: '******************',\n          email: '<EMAIL>',\n          school: '南京大学',\n          address: '江苏省南京市栖霞区仙林大道163号',\n          isDefault: false\n        }\n      ],\n      showForm: false,\n      isEdit: false,\n      currentIndex: -1,\n      formData: {\n        name: '',\n        phone: '',\n        idCard: '',\n        email: '',\n        school: '',\n        address: '',\n        isDefault: false\n      },\n      isDefaultDisabled: false\n    };\n  },\n  computed: {\n    hasDefaultProfile() {\n      return this.profileList.some(item => item.isDefault);\n    }\n  },\n  onReady() {\n    // 在页面渲染完成后初始化\n  },\n  mounted() {\n    // 获取语言设置\n    const lang = uni.getStorageSync('lang') || 'zh';\n    this.lang = lang;\n    \n    // 监听语言变化\n    uni.$on('lang-change', this.onLangChange);\n    \n    // 设置导航栏标题\n    this.setNavTitle();\n    \n    // 获取报名资料列表\n    this.getProfileList();\n  },\n  beforeDestroy() {\n    uni.$off('lang-change', this.onLangChange);\n  },\n  methods: {\n    onLangChange(newLang) {\n      this.lang = newLang;\n      this.setNavTitle();\n    },\n    setNavTitle() {\n      const title = this.lang === 'zh' ? '报名资料管理' : 'Registration Info';\n      uni.setNavigationBarTitle({ title });\n    },\n    getProfileList() {\n      // 在实际应用中，这里应该是从API获取数据\n      // 现在使用的是静态数据，已经在data中定义，所以这里不需要做任何事情\n      console.log('获取报名资料列表', this.profileList.length);\n    },\n    formatIdCard(idCard) {\n      if (!idCard) return '';\n      // 保留前4位和后4位，中间用*代替\n      return idCard.substring(0, 4) + '************' + idCard.substring(idCard.length - 4);\n    },\n    addProfile() {\n      this.isEdit = false;\n      this.currentIndex = -1;\n      \n      // 检查是否已有默认资料\n      const hasDefault = this.profileList.some(item => item.isDefault);\n      \n      this.formData = {\n        name: '',\n        phone: '',\n        idCard: '',\n        email: '',\n        school: '',\n        address: '',\n        isDefault: !hasDefault // 如果没有默认资料，则自动设为默认\n      };\n      \n      // 如果没有默认资料，则不能取消默认选项\n      this.isDefaultDisabled = !hasDefault;\n      \n      this.showForm = true;\n    },\n    editProfile(index) {\n      this.isEdit = true;\n      this.currentIndex = index;\n      const profile = this.profileList[index];\n      this.formData = { ...profile };\n      \n      // 修改逻辑：如果当前编辑的是默认资料，则禁用复选框（不能取消默认）\n      // 如果不是默认资料，且已有其他默认资料，则不禁用（可以切换默认）\n      this.isDefaultDisabled = profile.isDefault;\n      \n      this.showForm = true;\n    },\n    closeForm() {\n      this.showForm = false;\n    },\n    saveProfile() {\n      // 表单验证\n      if (!this.formData.name) {\n        return uni.showToast({\n          title: this.lang === 'zh' ? '请输入姓名' : 'Please enter name',\n          icon: 'none'\n        });\n      }\n      if (!this.formData.phone) {\n        return uni.showToast({\n          title: this.lang === 'zh' ? '请输入手机号' : 'Please enter phone',\n          icon: 'none'\n        });\n      }\n      \n      // 显示加载中\n      uni.showLoading({\n        title: this.lang === 'zh' ? '保存中...' : 'Saving...'\n      });\n      \n      // 处理默认资料\n      if (this.formData.isDefault) {\n        // 如果当前资料设为默认，则其他资料取消默认\n        this.profileList.forEach(item => {\n          item.isDefault = false;\n        });\n      } else if (this.isEdit && this.profileList[this.currentIndex].isDefault) {\n        // 如果正在编辑的是默认资料，但取消了默认，则第一个资料设为默认\n        // 注意：由于我们禁用了默认资料的复选框，这种情况实际上不应该发生\n        // 但为了代码健壮性，我们仍然处理这种情况\n        this.formData.isDefault = true; // 强制保持默认\n      } else if (!this.profileList.some(item => item.isDefault) && (this.profileList.length === 0 || this.isEdit)) {\n        // 如果没有默认资料，则当前资料设为默认\n        this.formData.isDefault = true;\n      }\n      \n      // 保存资料\n      if (this.isEdit) {\n        this.profileList[this.currentIndex] = { ...this.formData };\n      } else {\n        this.profileList.push({ ...this.formData });\n      }\n      \n      // 模拟API保存延迟\n      setTimeout(() => {\n        uni.hideLoading();\n        uni.showToast({\n          title: this.lang === 'zh' ? '保存成功' : 'Saved successfully',\n          icon: 'success'\n        });\n        \n        // 关闭弹窗\n        this.closeForm();\n      }, 500);\n    },\n    confirmDelete(index) {\n      uni.showModal({\n        title: this.lang === 'zh' ? '确认删除' : 'Confirm Delete',\n        content: this.lang === 'zh' ? '确定要删除该报名资料吗？' : 'Are you sure you want to delete this profile?',\n        success: (res) => {\n          if (res.confirm) {\n            this.deleteProfile(index);\n          }\n        }\n      });\n    },\n    deleteProfile(index) {\n      const isDefault = this.profileList[index].isDefault;\n      \n      // 删除资料\n      this.profileList.splice(index, 1);\n      \n      // 如果删除的是默认资料且还有其他资料，则将第一个资料设为默认\n      if (isDefault && this.profileList.length > 0) {\n        this.profileList[0].isDefault = true;\n      }\n      \n      // 模拟API删除延迟\n      setTimeout(() => {\n        uni.showToast({\n          title: this.lang === 'zh' ? '删除成功' : 'Deleted successfully',\n          icon: 'success'\n        });\n      }, 300);\n    }\n  }\n}\n</script>\n\n<style scoped>\n.signup-manage-page {\n  min-height: 100vh;\n  box-sizing: border-box;\n  padding-bottom: calc(120rpx + constant(safe-area-inset-bottom)); /* iOS 11.2 以下 */\n  padding-bottom: calc(120rpx + env(safe-area-inset-bottom)); /* iOS 11.2 及以上 */\n  background-color: #f5f7fa;\n}\n.tip-bar {\n  display: flex;\n  align-items: center;\n  background-color: #e6f2ff;\n  padding: 20rpx 30rpx;\n}\n.tip-text {\n  font-size: 26rpx;\n  color: #4285f4;\n  margin-left: 10rpx;\n}\n.profile-list {\n  padding: 20rpx;\n}\n.profile-item {\n  background-color: #fff;\n  border-radius: 12rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  position: relative;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);\n  border: 2rpx solid transparent; /* 添加透明边框，避免选中时的跳动 */\n}\n.profile-item.default-profile {\n  border: 2rpx solid #4285f4; /* 默认资料的边框 */\n}\n.profile-main {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20rpx;\n}\n.profile-info {\n  flex: 1;\n}\n.profile-name {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n.profile-name .default-tag {\n  background-color: #4285f4;\n  color: #fff;\n  font-size: 22rpx;\n  padding: 6rpx 16rpx;\n  border-radius: 0 12rpx 0 12rpx;\n  margin-left: 10rpx;\n}\n.profile-phone {\n  font-size: 28rpx;\n  color: #666;\n}\n.profile-actions {\n  display: flex;\n}\n.action-btn {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-left: 20rpx;\n}\n.profile-detail {\n  background-color: #f9f9f9;\n  border-radius: 8rpx;\n  padding: 20rpx;\n}\n.detail-item {\n  display: flex;\n  margin-bottom: 10rpx;\n}\n.detail-item:last-child {\n  margin-bottom: 0;\n}\n.detail-label {\n  width: 120rpx;\n  font-size: 26rpx;\n  color: #999;\n}\n.detail-value {\n  flex: 1;\n  font-size: 26rpx;\n  color: #666;\n}\n.add-btn {\n  position: fixed;\n  bottom: calc(40rpx + constant(safe-area-inset-bottom)); /* iOS 11.2 以下 */\n  bottom: calc(40rpx + env(safe-area-inset-bottom)); /* iOS 11.2 及以上 */\n  left: 50%;\n  transform: translateX(-50%);\n  background-color: #4285f4;\n  color: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 90%;\n  height: 80rpx;\n  border-radius: 40rpx;\n  box-shadow: 0 4rpx 12rpx rgba(66,133,244,0.3);\n  z-index: 9;\n}\n.add-btn text {\n  margin-left: 10rpx;\n  font-size: 28rpx;\n}\n.form-popup {\n  padding: 30rpx;\n  box-sizing: border-box;\n  height: 80vh;\n  display: flex;\n  flex-direction: column;\n}\n.form-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n.form-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n.form-scroll {\n  flex: 1;\n  height: 0;\n}\n.form-content {\n  padding-bottom: 30rpx;\n}\n.form-item {\n  margin-bottom: 30rpx;\n}\n.form-label {\n  font-size: 28rpx;\n  color: #666;\n  margin-bottom: 10rpx;\n  display: block;\n}\n.required::before {\n  content: '*';\n  color: #ff5252;\n  margin-right: 6rpx;\n}\n.checkbox-item {\n  display: flex;\n  align-items: center;\n}\n.checkbox-text {\n  font-size: 28rpx;\n  color: #666;\n  margin-left: 10rpx;\n}\n.form-footer {\n  padding-top: 20rpx;\n  border-top: 1px solid #eee;\n}\n</style> ", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./manage.vue?vue&type=style&index=0&id=28aa4093&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./manage.vue?vue&type=style&index=0&id=28aa4093&scoped=true&lang=css&\""], "sourceRoot": ""}