{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/index/index.vue?d42d", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/index/index.vue?8205", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/index/index.vue?02d3", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/index/index.vue?6628", "uni-app:///pages/index/index.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/index/index.vue?abf9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uSearch", "uSwiper", "uGrid", "uGridItem", "uImage", "uIcon", "uLoadmore", "uPopup", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "_isMounted", "e0", "$event", "showPopup", "recyclableRender", "staticRenderFns", "_withStripped", "CustomTabbar", "LangSwitch", "data", "lang", "searchValue", "popupImage", "popupLink", "bannerList", "categoryList", "name", "name_en", "image", "activityList", "id", "title", "time", "img", "desc", "pastList", "date", "views", "pastListMore", "page", "limit", "loadMoreStatus", "mounted", "uni", "<PERSON><PERSON><PERSON><PERSON>", "methods", "initLogin", "result", "icon", "onSearch", "url", "onCategoryClick", "onActivityClick", "onLangChange", "setNavTitle", "loadMorePast", "newItems", "fetchPastItems", "start", "end", "navigateToPastEventDetail", "navigateToEventDetail", "getHomePageData", "response", "navigateToCategory", "getWindowAdvert", "advertData", "onPopupClick"], "mappings": "4IAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,wBACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,QAAS,WACP,OAAO,sHAITC,QAAS,WACP,OAAO,sHAITC,MAAO,WACL,OAAO,kHAITC,UAAW,WACT,OAAO,4HAITC,OAAQ,WACN,OAAO,oHAITC,MAAO,WACL,OAAO,kHAITC,UAAW,WACT,OAAO,0HAITC,OAAQ,WACN,OAAO,qHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,GACdL,EAAIM,aACPN,EAAIO,GAAK,SAAUC,GACjBR,EAAIS,WAAY,KAIlBC,GAAmB,EACnBC,EAAkB,GACtBZ,EAAOa,eAAgB,G,iCCzEvB,yHAAg4B,eAAG,G,gKCuFn4B,SACA,SACA,uOACA,CACA3B,YAAA4B,eAAAC,cACAC,gBACA,OACAC,oCACAC,eACAR,aACAS,cACAC,aACAC,YACA,yCACA,yCACA,0CAEAC,cACA,CAAAC,UAAAC,iBAAAC,iDACA,CAAAF,UAAAC,cAAAC,iDACA,CAAAF,UAAAC,eAAAC,iDACA,CAAAF,UAAAC,kBAAAC,kDAEAC,cACA,CAAAC,KAAAC,cAAAC,kBAAAC,6CAAAC,uBACA,CAAAJ,KAAAC,cAAAC,kBAAAC,6CAAAC,yBAEAC,UACA,CACAL,KACAC,oBACAE,6KACAC,mCACAE,kBACAC,YAEA,CACAP,KACAC,oBACAE,6KACAC,gCACAE,kBACAC,YAIAC,cACA,CACAR,KACAC,qBACAE,6KACAC,+BACAE,kBACAC,WAEA,CACAP,KACAC,oBACAE,6KACAC,6BACAE,kBACAC,WAEA,CACAP,KACAC,oBACAE,6KACAC,+BACAE,kBACAC,WAEA,CACAP,KACAC,oBACAE,6KACAC,+BACAE,kBACAC,WAEA,CACAP,KACAC,oBACAE,6KACAC,8BACAE,kBACAC,WAEA,CACAP,KACAC,mBACAE,6KACAC,4BACAE,kBACAC,YAGAE,OACAC,QACAC,4BAGAC,mBACAC,uCACA,mBACA,iBACA,uBACA,wBAEAC,yBACAD,yCAEAE,SAEAC,qBAAA,2JACA,0BAAAC,SAEA,UACA,WASAJ,aACAZ,6CACAiB,cAEA,0CAjBA,IAoBAC,qBAEA,EAEAN,cACAO,uEAGAP,aACAZ,+DACAiB,eAIAG,4BAEAR,aAAAZ,wFAAAiB,eAEAI,4BAEAT,aAAAZ,sBAAAiB,eAEAK,yBACA,YACA,mBACA,wBAGAC,uBACA,8CACAX,yBACAZ,WAIAwB,wBAAA,6IACA,8EACA,oDAEA,0BAAAC,SACA,YACA,iEACA,SAEA,qCACA,4BAEA,2BAGA,0BACA,qDAEAvD,8BACA,4BACA0C,aACAZ,kEACAiB,cACA,yDAvBA,IA2BAS,0BAAA,4JAEA,4DAMA,OAHAC,yBACAC,iBAEA,kBACA,qEATA,IAWAC,sCACAjB,cACAO,iDAGAW,kCACAlB,cACAO,4CAIAY,2BAAA,0IAEA,OAFA,SAEA7D,2BAAA,UACA,6BAAA8D,SACA9D,2BAEA,uBACAA,gCAGA,wCACA,4CACA,2CAKA,0CACA,+CACA,OACAyB,aACAC,gBACAC,4CAMA,gDACA,kDACA,0BACA,2BACA,6CAEA,OACAE,QACAC,aACAC,oDACAC,MACAC,yBAMAjC,6BACA,mDAEAA,gCAAA,wDAhDA,IAoDA+D,+BAEArB,uCAGAA,aACAO,6BAIAe,2BAAA,sKAEA,iCAAAF,SACA9D,2BAEA,wBACAiE,SAEA,aAEA,8CAGA,uBAGA,eAEAjE,uCAEA,mDAEAA,gCAAA,wDAtBA,IA0BAkE,wBACA,gBAEAxB,cACAO,qBAGA,qBAGA,c,6DClZA,yHAAuxC,eAAG,G", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\ntry {\n  components = {\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-search/u-search\" */ \"uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    uSwiper: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-swiper/u-swiper\" */ \"uview-ui/components/u-swiper/u-swiper.vue\"\n      )\n    },\n    uGrid: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-grid/u-grid\" */ \"uview-ui/components/u-grid/u-grid.vue\"\n      )\n    },\n    uGridItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-grid-item/u-grid-item\" */ \"uview-ui/components/u-grid-item/u-grid-item.vue\"\n      )\n    },\n    uImage: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-image/u-image\" */ \"uview-ui/components/u-image/u-image.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uLoadmore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loadmore/u-loadmore\" */ \"uview-ui/components/u-loadmore/u-loadmore.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showPopup = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"index-page\">\n    <!-- 搜索栏 -->\n    <view class=\"search-bar\">\n      <u-search :placeholder=\"lang === 'zh' ? '搜索往期风采' : 'Search past events'\" v-model=\"searchValue\" @search=\"onSearch\" :show-action=\"false\" />\n    </view>\n\n    <!-- Banner轮播 -->\n    <view class=\"banner-swiper\">\n      <u-swiper :list=\"bannerList\" height=\"300rpx\" border-radius=\"16rpx\" indicator indicator-active-color=\"#2979ff\" />\n    </view>\n\n    <!-- 活动分类宫格 -->\n    <u-grid :col=\"4\" :border=\"false\" class=\"category-grid\">\n      <u-grid-item v-for=\"(item, idx) in categoryList\" :key=\"item.name\" @click=\"navigateToCategory(idx)\">\n        <u-image :src=\"item.image\" width=\"96rpx\" height=\"96rpx\" shape=\"circle\" />\n        <text class=\"cat-title\">{{ lang === 'zh' ? item.name : item.name_en }}</text>\n      </u-grid-item>\n    </u-grid>\n\n    <!-- 推荐活动 -->\n    <view class=\"section-title\">{{ lang === 'zh' ? '推荐活动' : 'Recommended' }}</view>\n    <view class=\"activity-list\">\n      <view v-for=\"item in activityList\" :key=\"item.id\" class=\"activity-card\" @click=\"navigateToEventDetail(item.id)\">\n        <u-image :src=\"item.img\" width=\"100%\" height=\"250rpx\" border-radius=\"16rpx 16rpx 0 0\" />\n        <view class=\"card-content\">\n          <view class=\"card-title\">{{ item.title }}</view>\n          <view class=\"card-subtitle\">{{ item.time }}</view>\n          <view class=\"card-desc\">{{ item.desc }}</view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 往期风采 -->\n    <view class=\"section-title\">{{ lang === 'zh' ? '往期风采' : 'Past Events' }}</view>\n    <view class=\"activity-list\">\n      <view v-for=\"item in pastList\" :key=\"item.id\" class=\"activity-card\" @click=\"navigateToPastEventDetail(item.id)\">\n        <u-image :src=\"item.img\" width=\"100%\" height=\"250rpx\" border-radius=\"16rpx 16rpx 0 0\" />\n        <view class=\"card-content\">\n          <view class=\"card-title\">{{ item.title }}</view>\n          <view class=\"card-desc\">{{ item.desc }}</view>\n          <view class=\"card-footer\">\n            <view class=\"card-date\">\n              <u-icon name=\"calendar\" size=\"24rpx\" color=\"#909399\"></u-icon>\n              <text>{{ item.date }}</text>\n            </view>\n            <view class=\"card-views\">\n              <u-icon name=\"eye\" size=\"24rpx\" color=\"#909399\"></u-icon>\n              <text>{{ item.views }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      <!-- 使用 u-loadmore 组件 -->\n      <u-loadmore \n        :status=\"loadMoreStatus\" \n        :loading-text=\"lang === 'zh' ? '正在加载...' : 'Loading...'\" \n        :loadmore-text=\"lang === 'zh' ? '点击加载更多' : 'Load more'\" \n        :nomore-text=\"lang === 'zh' ? '没有更多了' : 'No more data'\"\n        @loadmore=\"loadMorePast\"\n        margin-top=\"20\"\n        margin-bottom=\"20\"\n      ></u-loadmore>\n    </view>\n\n    <!-- 全屏弹窗 -->\n    <u-popup :safeAreaInsetBottom=\"false\" round=\"16\" :show=\"showPopup\" mode=\"center\" :mask-close-able=\"true\" @close=\"showPopup = false\">\n      <view class=\"poster-popup\">\n        <u-image \n          :src=\"popupImage || 'https://picsum.photos/100/100?random=10'\" \n          width=\"500rpx\" \n          height=\"750rpx\" \n          :fade=\"true\" \n          duration=\"450\" \n          @click=\"onPopupClick\"\n        ></u-image>\n      </view>\n    </u-popup>\n\n    <CustomTabbar :current=\"0\" />\n    <LangSwitch />\n  </view>\n</template>\n\n<script>\nimport CustomTabbar from '@/components/CustomTabbar.vue'\nimport LangSwitch from '@/components/LangSwitch.vue'\nimport { weChatLogin } from '@/api/weChat.js'\nimport { postHomePage, postWindowAdvert } from '@/api/apiApplet.js'\nimport { formatImageUrl } from '@/utils/http.js'\nexport default {\n  components: { CustomTabbar, LangSwitch },\n  data() {\n    return {\n      lang: uni.getStorageSync('lang') || 'zh',\n      searchValue: '',\n      showPopup: false, // 改为默认不显示\n      popupImage: '', // 弹窗图片URL\n      popupLink: '', // 弹窗点击跳转链接\n      bannerList: [\n        'https://picsum.photos/800/600?random=1',\n        'https://picsum.photos/800/600?random=2',\n        'https://picsum.photos/800/600?random=3'\n      ],\n      categoryList: [\n        { name: '体育', name_en: 'Sports', image: 'https://picsum.photos/100/100?random=10' },\n        { name: '艺术', name_en: 'Art', image: 'https://picsum.photos/100/100?random=11' },\n        { name: '科技', name_en: 'Tech', image: 'https://picsum.photos/100/100?random=12' },\n        { name: '户外', name_en: 'Outdoor', image: 'https://picsum.photos/100/100?random=13' },\n      ],\n      activityList: [\n        { id: 1, title: '篮球训练营', time: '2024-07-10', img: 'https://picsum.photos/800/600?random=4', desc: '专业教练指导，提升篮球技能。' },\n        { id: 2, title: '少儿美术班', time: '2024-07-15', img: 'https://picsum.photos/800/600?random=5', desc: '培养孩子艺术素养，激发创造力。' }\n      ],\n      pastList: [\n        { \n          id: 1, \n          title: '2023夏令营精彩回顾', \n          img: 'https://images.unsplash.com/photo-1530124566582-a618bc2615dc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', \n          desc: '2023年夏令营圆满结束，120名学员参与，收获满满。',\n          date: '2023-09-01',\n          views: 1286\n        },\n        { \n          id: 2, \n          title: '2022冬令营精彩瞬间', \n          img: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', \n          desc: '2022冬令营活动丰富，学员们在学习中收获快乐。',\n          date: '2022-12-28',\n          views: 962\n        }\n      ],\n      // 额外的往期风采数据，用于模拟加载更多\n      pastListMore: [\n        { \n          id: 3, \n          title: '2021青少年科技展回顾', \n          img: 'https://images.unsplash.com/photo-1581472723648-909f4851d4ae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', \n          desc: '青少年科技展展示了学生们的创新能力和科学素养。',\n          date: '2021-11-15',\n          views: 754\n        },\n        { \n          id: 4, \n          title: '2021春季音乐会回顾', \n          img: 'https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', \n          desc: '春季音乐会上，学生们展示了精彩的音乐才艺。',\n          date: '2021-04-20',\n          views: 683\n        },\n        { \n          id: 5, \n          title: '2020冬季艺术节回顾', \n          img: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', \n          desc: '冬季艺术节展示了学生们在艺术领域的才华和创意。',\n          date: '2020-12-15',\n          views: 592\n        },\n        { \n          id: 6, \n          title: '2020夏季运动会回顾', \n          img: 'https://images.unsplash.com/photo-1517649763962-0c623066013b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', \n          desc: '夏季运动会上，学生们展现了团队协作和体育精神。',\n          date: '2020-07-30',\n          views: 547\n        },\n        { \n          id: 7, \n          title: '2019科学实验日回顾', \n          img: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', \n          desc: '科学实验日让学生们亲身体验科学的奇妙与乐趣。',\n          date: '2019-10-25',\n          views: 486\n        },\n        { \n          id: 8, \n          title: '2019春游活动回顾', \n          img: 'https://images.unsplash.com/photo-1517164850305-99a3e65bb47e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80', \n          desc: '春游活动中，学生们亲近自然，增进了友谊。',\n          date: '2019-04-18',\n          views: 423\n        }\n      ],\n      page: 1, // 当前页码\n      limit: 2, // 每页加载数量\n      loadMoreStatus: 'loadmore' // u-loadmore 组件的状态\n    }\n  },\n  mounted() {\n    uni.$on('lang-change', this.onLangChange)\n    this.setNavTitle() // 新增：页面加载时设置标题\n    this.initLogin() // 初始化登录\n    this.getHomePageData() // 获取首页数据\n    this.getWindowAdvert() // 获取弹窗广告\n  },\n  beforeDestroy() {\n    uni.$off('lang-change', this.onLangChange)\n  },\n  methods: {\n    // 初始化登录\n    async initLogin() {\n      const result = await weChatLogin();\n      \n      if (result.success) {\n        if (!result.hasToken) {\n          // 新登录成功\n          // uni.showToast({\n          //   title: this.lang === 'zh' ? result.message : 'Login successful',\n          //   icon: 'success'\n          // });\n        }\n      } else {\n        // 登录失败\n        uni.showToast({\n          title: this.lang === 'zh' ? result.message : 'Login failed',\n          icon: 'none'\n        });\n      }\n    },\n    \n    onSearch(val) {\n      // 搜索逻辑\n      if (val) {\n        // 跳转到往期风采列表页面，并传递搜索关键词\n        uni.navigateTo({\n          url: `/pages/past-events/list?keyword=${encodeURIComponent(val)}`\n        });\n      } else {\n        uni.showToast({ \n          title: this.lang === 'zh' ? '请输入搜索内容' : 'Please enter search keyword', \n          icon: 'none' \n        });\n      }\n    },\n    onCategoryClick(idx) {\n      // 分类点击逻辑\n      uni.showToast({ title: '点击分类：' + (this.lang === 'zh' ? this.categoryList[idx].name : this.categoryList[idx].name_en), icon: 'none' })\n    },\n    onActivityClick(item) {\n      // 活动卡片点击逻辑\n      uni.showToast({ title: '点击活动：' + item.title, icon: 'none' })\n    },\n    onLangChange(newLang) {\n      this.lang = newLang\n      this.setNavTitle() // 新增：语言切换时设置标题\n      this.getHomePageData() // 语言切换时重新获取首页数据\n    },\n    // 新增：设置导航栏标题的方法\n    setNavTitle() {\n      const title = this.lang === 'zh' ? '培训班夏令营' : 'Summer Camp'\n      uni.setNavigationBarTitle({\n        title: title\n      })\n    },\n    // 新增：加载更多往期风采\n    async loadMorePast() {\n      if (this.loadMoreStatus === 'loading') return\n      this.loadMoreStatus = 'loading'\n      try {\n        const newItems = await this.fetchPastItems()\n        if (newItems.length > 0) {\n          this.pastList = [...this.pastList, ...newItems]\n          this.page++\n          // 检查是否还有更多数据\n          if (this.page * this.limit < this.pastListMore.length) {\n            this.loadMoreStatus = 'loadmore'\n          } else {\n            this.loadMoreStatus = 'nomore'\n          }\n        } else {\n          this.loadMoreStatus = 'nomore'\n        }\n      } catch (error) {\n        console.error('加载更多失败:', error)\n        this.loadMoreStatus = 'loadmore'\n        uni.showToast({\n          title: this.lang === 'zh' ? '加载失败，请重试' : 'Failed to load, please try again',\n          icon: 'none'\n        })\n      }\n    },\n    // 新增：模拟获取往期风采数据\n    async fetchPastItems() {\n      // 模拟网络延迟\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      // 计算当前页应该获取的数据范围\n      const start = (this.page * this.limit) - this.limit\n      const end = this.page * this.limit\n      \n      // 从额外数据中获取指定范围的数据\n      return this.pastListMore.slice(start, end)\n    },\n    navigateToPastEventDetail(id) {\n      uni.navigateTo({\n        url: `/pages/past-events/detail?id=${id}`\n      })\n    },\n    navigateToEventDetail(id) {\n      uni.navigateTo({\n        url: `/pages/events/detail?id=${id}`\n      })\n    },\n    // 获取首页数据\n    async getHomePageData() {\n      try {\n        console.log('开始获取首页数据...');\n        const response = await postHomePage({});\n        console.log('首页接口返回数据:', response);\n        \n        if (response && response.code === 0 && response.data) {\n          console.log('首页数据解析成功:', response.data);\n          \n          // 更新 banner 数据\n          if (response.data.banner && response.data.banner.length > 0) {\n            this.bannerList = response.data.banner.map(item => {\n              return formatImageUrl(item.contents);\n            });\n          }\n          \n          // 更新分类数据\n          if (response.data.picture && response.data.picture.length > 0) {\n            this.categoryList = response.data.picture.map(item => {\n              return {\n                name: item.title,\n                name_en: item.title,\n                image: formatImageUrl(item.contents)\n              };\n            });\n          }\n          \n          // 更新活动数据\n          if (response.data.productHot && response.data.productHot.length > 0) {\n            this.activityList = response.data.productHot.map(item => {\n              const imgUrl = item.img && item.img !== '|' ?\n                formatImageUrl(item.img) :\n                'https://picsum.photos/800/600?random=' + item.id;\n\n              return {\n                id: item.id,\n                title: item.name,\n                time: item.addDate ? item.addDate.split(' ')[0] : '2024-01-01',\n                img: imgUrl,\n                desc: item.des || '暂无描述'\n              };\n            });\n          }\n          \n        } else {\n          console.error('获取首页数据失败:', response);\n        }\n      } catch (error) {\n        console.error('获取首页数据异常:', error);\n      }\n    },\n    \n    navigateToCategory(idx) {\n      // 使用本地存储\n      uni.setStorageSync('selectedCategory', idx);\n      \n      // 使用switchTab跳转到tabbar页面\n      uni.switchTab({\n        url: '/pages/events/index'\n      });\n    },\n    // 获取全屏弹窗广告\n    async getWindowAdvert() {\n      try {\n        const response = await postWindowAdvert({});\n        console.log('弹窗广告接口返回:', response);\n\n        if (response && response.code === 0 && response.data) {\n          const advertData = response.data;\n\n          if (advertData.contents) {\n            // 使用全局图片URL处理函数\n            this.popupImage = formatImageUrl(advertData.contents);\n\n            // 设置跳转链接（如果有的话）\n            this.popupLink = advertData.link || '';\n\n            // 显示弹窗\n            this.showPopup = true;\n\n            console.log('弹窗图片URL:', this.popupImage);\n          }\n        }\n      } catch (error) {\n        console.error('获取弹窗广告失败:', error);\n      }\n    },\n    // 弹窗点击处理\n    onPopupClick() {\n      if (this.popupLink) {\n        // 如果有跳转链接，进行跳转\n        uni.navigateTo({\n          url: this.popupLink\n        });\n      }\n      this.showPopup = false;\n    }\n  }\n}\n</script>\n\n<style scoped>\n.poster-popup {\n  /* 消除 u-popup 内部的默认 padding */\n  line-height: 1; \n}\n.search-bar {\n  margin: 24rpx;\n  margin-top: 0rpx;\n  padding-top: 24rpx;\n  box-sizing: border-box;\n}\n.index-page {\n  min-height: 100vh;\n  padding-bottom: 0;\n}\n.banner-swiper {\n  margin: 24rpx;\n}\n.category-grid {\n  margin-top: 24rpx;\n}\n.cat-title {\n  font-size: 24rpx;\n  margin-top: 8rpx;\n  color: #333;\n}\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  margin: 32rpx 24rpx 16rpx 24rpx;\n  color: #222;\n}\n\n.activity-list {\n  padding: 0 24rpx;\n}\n\n.activity-card {\n  margin-bottom: 24rpx;\n  background: #fff;\n  border-radius: 16rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n}\n\n.card-content {\n  padding: 24rpx;\n}\n\n.card-title {\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #303133;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.card-subtitle {\n  font-size: 24rpx;\n  color: #909399;\n  margin-top: 8rpx;\n}\n\n.card-desc {\n  font-size: 26rpx;\n  color: #606266;\n  margin-top: 16rpx;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n}\n\n.card-footer {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 16rpx;\n  padding-top: 16rpx;\n  border-top: 1rpx solid #eee;\n}\n\n.card-date, .card-views {\n  display: flex;\n  align-items: center;\n  font-size: 24rpx;\n  color: #909399;\n}\n\n.card-date text, .card-views text {\n  margin-left: 6rpx;\n}\n\n.popup-content {\n  padding: 48rpx 32rpx;\n  text-align: center;\n}\n</style>\n", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\""], "sourceRoot": ""}