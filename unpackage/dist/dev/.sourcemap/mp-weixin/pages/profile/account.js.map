{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/profile/account.vue?ee3e", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/profile/account.vue?e09e", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/profile/account.vue?f212", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/profile/account.vue?edff", "uni-app:///pages/profile/account.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/profile/account.vue?d755"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uIcon", "uPopup", "uInput", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "_isMounted", "e0", "$event", "codeButtonDisabled", "sendVerificationCode", "recyclableRender", "staticRenderFns", "_withStripped", "LangSwitch", "data", "lang", "phoneNumber", "showPhonePopup", "formPhone", "formCode", "codeCountdown", "codeTimer", "computed", "codeButtonText", "onLoad", "uni", "onUnload", "clearInterval", "methods", "onLangChange", "setNavTitle", "title", "getAccountSecurityInfo", "setTimeout", "onPhoneBinding", "icon", "cancelPhoneBinding", "confirmPhoneBinding"], "mappings": "gJAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,4BACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,MAAO,WACL,OAAO,kHAITC,OAAQ,WACN,OAAO,oHAITC,OAAQ,WACN,OAAO,qHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,GACdL,EAAIM,aACPN,EAAIO,GAAK,SAAUC,IAChBR,EAAIS,oBAAsBT,EAAIU,0BAIjCC,GAAmB,EACnBC,EAAkB,GACtBb,EAAOc,eAAgB,G,iCChDvB,yHAAk4B,eAAG,G,mOCkEr4B,CACAvB,YACAwB,cAEAC,gBACA,OACAC,UACAC,eAGAC,kBACAC,aACAC,YAGAC,gBACAC,iBAGAC,UACAC,0BACA,4BACA,kCAEA,qCAEAf,8BACA,8BAGAgB,kBAEA,qCACA,YAGAC,uCAGA,mBAGA,+BAEAC,oBACAD,wCAGA,gBACAE,+BAGAC,SACAC,yBACA,YACA,oBAEAC,uBACA,iDACAL,yBAAAM,WAEAC,kCAAA,WAGAC,uBAEA,mBACA,MAIAC,0BACA,kBACA,iBACA,wBAEAzB,gCAAA,WAEA,+CACA,oBACAsB,uEACAI,cAKA,sBACA,uCACA,kBACA,oBACAR,6BAEA,KAGAF,aACAM,yDACAI,kBAGAC,8BACA,wBAEAC,+BAAA,WAEA,kDAMA,yCAQAZ,eACAM,uDAGAE,uBACAR,gBACA,oBAGA,yEAEAA,aACAM,gEACAI,mBAEA,OAtBA,aACAJ,2EACAI,cARA,aACAJ,uEACAI,iBA6BA,c,6DC3MA,yHAAyxC,eAAG,G", "file": "pages/profile/account.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/profile/account.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./account.vue?vue&type=template&id=0eb8f644&scoped=true&\"\nvar renderjs\nimport script from \"./account.vue?vue&type=script&lang=js&\"\nexport * from \"./account.vue?vue&type=script&lang=js&\"\nimport style0 from \"./account.vue?vue&type=style&index=0&id=0eb8f644&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0eb8f644\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/profile/account.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./account.vue?vue&type=template&id=0eb8f644&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-input/u-input\" */ \"uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      !_vm.codeButtonDisabled && _vm.sendVerificationCode()\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./account.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./account.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"account-page\">\n    <view class=\"account-container\">\n      <!-- 手机号绑定 -->\n      <view class=\"security-section\">\n        <view class=\"section-title\">{{ lang === 'zh' ? '手机号绑定' : 'Phone Number' }}</view>\n        <view class=\"security-item\" @click=\"onPhoneBinding\">\n          <view class=\"security-info\">\n            <u-icon name=\"phone\" size=\"28\" color=\"#2979ff\"></u-icon>\n            <view class=\"security-content\">\n              <view class=\"security-label\">{{ lang === 'zh' ? '已绑定手机号' : 'Bound Phone' }}</view>\n              <view class=\"security-value\">{{ phoneNumber || (lang === 'zh' ? '未绑定' : 'Not Bound') }}</view>\n            </view>\n          </view>\n          <view class=\"security-action\">\n            <text class=\"action-text\">{{ phoneNumber ? (lang === 'zh' ? '修改' : 'Change') : (lang === 'zh' ? '绑定' : 'Bind') }}</text>\n            <u-icon name=\"arrow-right\" size=\"20\" color=\"#c0c4cc\"></u-icon>\n          </view>\n        </view>\n      </view>\n\n      <!-- 弹窗：手机号绑定 -->\n      <u-popup :show=\"showPhonePopup\" mode=\"center\" :round=\"16\" width=\"80%\" :safeAreaInsetBottom=\"false\">\n        <view class=\"popup-container\">\n          <view class=\"popup-title\">{{ phoneNumber ? (lang === 'zh' ? '修改手机号' : 'Change Phone Number') : (lang === 'zh' ? '绑定手机号' : 'Bind Phone Number') }}</view>\n          <view class=\"popup-form\">\n            <view class=\"form-item\">\n              <u-input \n                v-model=\"formPhone\" \n                :placeholder=\"lang === 'zh' ? '请输入手机号' : 'Enter phone number'\"\n                type=\"number\"\n                border=\"bottom\"\n                clearable\n              ></u-input>\n            </view>\n            <view class=\"form-item verification-code\">\n              <u-input \n                v-model=\"formCode\" \n                :placeholder=\"lang === 'zh' ? '请输入验证码' : 'Enter verification code'\"\n                type=\"number\"\n                border=\"bottom\"\n                clearable\n                class=\"code-input\"\n              ></u-input>\n              <view class=\"code-button\" :class=\"{ 'disabled': codeButtonDisabled }\" @click=\"!codeButtonDisabled && sendVerificationCode()\">\n                {{ codeButtonText }}\n              </view>\n            </view>\n          </view>\n          <view class=\"popup-buttons\">\n            <view class=\"cancel-button\" @click=\"cancelPhoneBinding\" v-if=\"lang === 'zh'\">取消</view>\n            <view class=\"cancel-button\" @click=\"cancelPhoneBinding\" v-else>Cancel</view>\n            <view class=\"confirm-button\" @click=\"confirmPhoneBinding\" v-if=\"lang === 'zh'\">确认</view>\n            <view class=\"confirm-button\" @click=\"confirmPhoneBinding\" v-else>Confirm</view>\n          </view>\n        </view>\n      </u-popup>\n\n      <LangSwitch />\n    </view>\n  </view>\n</template>\n\n<script>\nimport LangSwitch from '@/components/LangSwitch.vue';\n\nexport default {\n  components: {\n    LangSwitch\n  },\n  data() {\n    return {\n      lang: 'zh',\n      phoneNumber: '', // 模拟数据\n      \n      // 弹窗相关\n      showPhonePopup: false,\n      formPhone: '',\n      formCode: '',\n      \n      // 验证码按钮\n      codeCountdown: 0,\n      codeTimer: null\n    };\n  },\n  computed: {\n    codeButtonText() {\n      if (this.codeCountdown > 0) {\n        return `${this.codeCountdown}s`;\n      }\n      return this.lang === 'zh' ? '获取验证码' : 'Get Code';\n    },\n    codeButtonDisabled() {\n      return this.codeCountdown > 0;\n    }\n  },\n  onLoad() {\n    // 获取语言设置\n    const lang = uni.getStorageSync('lang') || 'zh';\n    this.lang = lang;\n    \n    // 监听语言变化\n    uni.$on('lang-change', this.onLangChange);\n    \n    // 设置导航栏标题\n    this.setNavTitle();\n    \n    // 获取账户安全信息\n    this.getAccountSecurityInfo();\n  },\n  onUnload() {\n    uni.$off('lang-change', this.onLangChange);\n    \n    // 清除定时器\n    if (this.codeTimer) {\n      clearInterval(this.codeTimer);\n    }\n  },\n  methods: {\n    onLangChange(newLang) {\n      this.lang = newLang;\n      this.setNavTitle();\n    },\n    setNavTitle() {\n      const title = this.lang === 'zh' ? '账户安全' : 'Account Security';\n      uni.setNavigationBarTitle({ title });\n    },\n    getAccountSecurityInfo() {\n      // 模拟从API获取账户安全信息\n      // 实际项目中应该从服务器获取\n      setTimeout(() => {\n        // 模拟数据 - 默认未绑定手机号\n        this.phoneNumber = '';\n      }, 500);\n    },\n    \n    // 手机号绑定相关\n    onPhoneBinding() {\n      this.formPhone = '';\n      this.formCode = '';\n      this.showPhonePopup = true;\n    },\n    sendVerificationCode() {\n      // 验证手机号格式\n      if (!this.formPhone || this.formPhone.length !== 11) {\n        return uni.showToast({\n          title: this.lang === 'zh' ? '请输入正确的手机号' : 'Please enter a valid phone number',\n          icon: 'none'\n        });\n      }\n      \n      // 开始倒计时\n      this.codeCountdown = 60;\n      this.codeTimer = setInterval(() => {\n        this.codeCountdown--;\n        if (this.codeCountdown <= 0) {\n          clearInterval(this.codeTimer);\n        }\n      }, 1000);\n      \n      // 模拟发送验证码\n      uni.showToast({\n        title: this.lang === 'zh' ? '验证码已发送' : 'Verification code sent',\n        icon: 'success'\n      });\n    },\n    cancelPhoneBinding() {\n      this.showPhonePopup = false;\n    },\n    confirmPhoneBinding() {\n      // 验证手机号和验证码\n      if (!this.formPhone || this.formPhone.length !== 11) {\n        return uni.showToast({\n          title: this.lang === 'zh' ? '请输入正确的手机号' : 'Please enter a valid phone number',\n          icon: 'none'\n        });\n      }\n      if (!this.formCode || this.formCode.length !== 6) {\n        return uni.showToast({\n          title: this.lang === 'zh' ? '请输入6位验证码' : 'Please enter 6-digit verification code',\n          icon: 'none'\n        });\n      }\n      \n      // 模拟绑定成功\n      uni.showLoading({\n        title: this.lang === 'zh' ? '处理中...' : 'Processing...'\n      });\n      \n      setTimeout(() => {\n        uni.hideLoading();\n        this.showPhonePopup = false;\n        \n        // 更新手机号\n        this.phoneNumber = this.formPhone.substring(0, 3) + '****' + this.formPhone.substring(7);\n        \n        uni.showToast({\n          title: this.lang === 'zh' ? '手机号绑定成功' : 'Phone number bound successfully',\n          icon: 'success'\n        });\n      }, 1500);\n    }\n  }\n}\n</script>\n\n<style scoped>\n.account-page {\n  min-height: 100vh;\n  background-color: #f8f8f8;\n}\n.account-container {\n  padding: 24rpx;\n}\n.security-section {\n  background-color: #ffffff;\n  border-radius: 12rpx;\n  margin-bottom: 24rpx;\n  padding: 0 24rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);\n}\n.section-title {\n  font-size: 28rpx;\n  color: #909399;\n  padding: 24rpx 0 16rpx;\n}\n.security-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24rpx 0;\n  border-top: 1px solid #f5f5f5;\n}\n.security-item:first-child {\n  border-top: none;\n}\n.security-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n.security-content {\n  flex: 1;\n  margin-left: 24rpx; /* 增加图标与文本的距离 */\n}\n.security-label {\n  font-size: 28rpx;\n  color: #303133;\n  margin-bottom: 4rpx;\n}\n.security-value {\n  font-size: 24rpx;\n  color: #909399;\n}\n.security-action {\n  display: flex;\n  align-items: center;\n}\n.action-text {\n  font-size: 24rpx;\n  color: #2979ff;\n  margin-right: 8rpx;\n}\n\n/* 弹窗样式 */\n.popup-container {\n  background-color: #ffffff;\n  padding: 40rpx 30rpx;\n  border-radius: 14rpx;\n}\n.popup-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  text-align: center;\n  margin-bottom: 40rpx;\n  color: #303133;\n}\n.popup-form {\n  margin-bottom: 40rpx;\n}\n.form-item {\n  margin-bottom: 30rpx;\n}\n.verification-code {\n  display: flex;\n  align-items: center;\n}\n.code-input {\n  flex: 1;\n  margin-right: 20rpx;\n}\n.code-button {\n  width: 180rpx;\n  height: 70rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #2979ff;\n  color: #ffffff;\n  font-size: 26rpx;\n  border-radius: 8rpx;\n  margin-left: 20rpx;\n}\n.code-button.disabled {\n  background-color: #a0cfff;\n  color: #ffffff;\n}\n.popup-buttons {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 20rpx;\n}\n.cancel-button {\n  flex: 1;\n  height: 80rpx;\n  line-height: 80rpx;\n  text-align: center;\n  background-color: #f2f2f2;\n  color: #606266;\n  border-radius: 8rpx;\n  margin-right: 20rpx;\n  font-size: 28rpx;\n}\n.confirm-button {\n  flex: 1;\n  height: 80rpx;\n  line-height: 80rpx;\n  text-align: center;\n  background-color: #2979ff;\n  color: #ffffff;\n  border-radius: 8rpx;\n  margin-left: 20rpx;\n  font-size: 28rpx;\n}\n</style> ", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./account.vue?vue&type=style&index=0&id=0eb8f644&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./account.vue?vue&type=style&index=0&id=0eb8f644&scoped=true&lang=css&\""], "sourceRoot": ""}