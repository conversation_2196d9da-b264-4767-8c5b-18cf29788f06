{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/profile/index.vue?9433", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/profile/index.vue?e30e", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/profile/index.vue?9b5a", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/profile/index.vue?539c", "uni-app:///pages/profile/index.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/profile/index.vue?d43c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uAvatar", "uIcon", "uButton", "uTag", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "CustomTabbar", "LangSwitch", "data", "lang", "user", "avatar", "nickname", "signature", "desc", "points", "onShow", "mounted", "uni", "<PERSON><PERSON><PERSON><PERSON>", "methods", "getUserInfo", "response", "avatarUrl", "id", "level", "money", "cardNums", "isHaveEWM", "title", "icon", "loadLocalUserInfo", "onLangChange", "setNavTitle", "onEditProfile", "url", "onExchange", "onPointsRecord", "onSignupManage", "onPrivacy", "onAccount", "onService", "onContactSuccess", "onContactFail"], "mappings": "8IAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,0BACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,QAAS,WACP,OAAO,sHAITC,MAAO,WACL,OAAO,kHAITC,QAAS,WACP,OAAO,sHAITC,KAAM,WACJ,OAAO,iHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,iCChDvB,yHAAg4B,eAAG,G,qJCyGn4B,uOAEA,CACAnB,YACAoB,eACAC,cAEAC,gBACA,OACAC,UACAC,MACAC,mCACAC,mBACAC,mBACAC,kBAEAC,cAGAC,kBACA,0BAEAC,mBAEA,qCACA,YAGAC,uCAGA,mBAGA,oBAEAC,yBACAD,yCAEAE,SACAC,uBAAA,sKAGA,2BAAAC,SAEA,uBAEAC,2CACA,oBACAA,wCAIA,QACAC,iBACAb,SACAC,gCACAC,mBACAC,iBACAW,sBACAC,sBACAC,4BACAC,+BAIA,4BAGAV,oCACAA,wCAEAxB,kCAEAA,6BAEA,uBACA,mDAEAA,gCAEA,sBACAwB,aACAW,yDACAC,cACA,wDA7CA,IAiDAC,6BAEA,mCACA,IACA,cAGAC,yBACA,YACA,oBAEAC,uBACA,sCACAf,yBAAAW,WAEAK,yBACAhB,cACAiB,6BAGAC,sBACAlB,cACAiB,gCAGAE,0BACAnB,cACAiB,8BAGAG,0BACApB,cACAiB,8BAGAI,qBACArB,cACAiB,gCAGAK,qBACAtB,cACAiB,gCAGAM,uBAGAC,6BACAhD,yBAEAiD,0BACAjD,wBACAwB,aACAW,iDACAC,iBAIA,c,6DC7PA,yHAAuxC,eAAG,G", "file": "pages/profile/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/profile/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=14bc1b43&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=14bc1b43&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"14bc1b43\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/profile/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=14bc1b43&scoped=true&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-avatar/u-avatar\" */ \"uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-button/u-button\" */ \"uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uTag: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-tag/u-tag\" */ \"uview-ui/components/u-tag/u-tag.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"profile-page\">\n    <!-- 个人信息 -->\n    <view class=\"profile-header\">\n      <view class=\"profile-header-inner\">\n        <u-avatar :src=\"user.avatar\" size=\"80\" />\n        <view class=\"profile-info\">\n          <view class=\"profile-nick\">\n            {{ user.nickname }}\n            <text v-if=\"user.level > 0\" class=\"level-badge\">LV{{ user.level }}</text>\n          </view>\n          <view class=\"profile-sign\">{{ user.signature }}</view>\n          <view class=\"profile-desc\">{{ user.desc }}</view>\n          <view v-if=\"user.money > 0\" class=\"profile-money\">{{ lang === 'zh' ? '余额：' : 'Balance: ' }}¥{{ user.money }}</view>\n        </view>\n        <u-icon name=\"arrow-right\" size=\"24\" color=\"#c0c4cc\" class=\"profile-edit\" @click=\"onEditProfile\" />\n      </view>\n    </view>\n\n    <!-- 我的积分 -->\n    <view class=\"section-card\">\n      <view class=\"profile-points-bar\">\n        <view class=\"points-left\">\n          <text class=\"points-num\">{{ points }}</text>\n          <text class=\"points-unit\">{{ lang === 'zh' ? '积分' : 'Points' }}</text>\n        </view>\n        <view class=\"points-right\">\n          <u-button type=\"primary\" class=\"exchange-btn\" @click=\"onExchange\">{{ lang === 'zh' ? '积分兑换' : 'Exchange' }}</u-button>\n        </view>\n      </view>\n      \n      <view class=\"divider\"></view>\n      \n      <view class=\"points-record\" @click=\"onPointsRecord\">\n        <text class=\"record-text\">{{ lang === 'zh' ? '积分记录' : 'Points Record' }}</text>\n        <u-icon name=\"arrow-right\" size=\"20\" color=\"#c0c4cc\"></u-icon>\n      </view>\n    </view>\n\n    <!-- 报名资料管理入口 -->\n    <view class=\"section-card signup-card\" @click=\"onSignupManage\">\n      <view class=\"signup-header\">\n        <view class=\"signup-title\">\n          <u-icon name=\"file-text\" size=\"32\" color=\"#2979ff\" class=\"signup-icon\"></u-icon>\n          <text>{{ lang === 'zh' ? '报名资料管理' : 'Sign-up Info' }}</text>\n        </view>\n        <u-icon name=\"arrow-right\" size=\"20\" color=\"#c0c4cc\"></u-icon>\n      </view>\n      <view class=\"signup-content\">\n        <view class=\"signup-desc\">{{ lang === 'zh' ? '管理您的个人报名信息，确保参与活动时资料完整' : 'Manage your personal registration information' }}</view>\n        <u-tag text=\"重要\" type=\"warning\" size=\"mini\" v-if=\"lang === 'zh'\" class=\"signup-tag\"></u-tag>\n        <u-tag text=\"Important\" type=\"warning\" size=\"mini\" v-else class=\"signup-tag\"></u-tag>\n      </view>\n    </view>\n\n    <!-- 设置区 -->\n    <view class=\"section-card settings-card\">\n      <view class=\"settings-item\" @click=\"onPrivacy\">\n        <view class=\"settings-left\">\n          <view class=\"custom-icon privacy-icon\">\n            <u-icon name=\"info-circle\" size=\"28\" color=\"#2979ff\"></u-icon>\n          </view>\n          <text class=\"settings-title\">{{ lang === 'zh' ? '隐私协议' : 'Privacy Policy' }}</text>\n        </view>\n        <u-icon name=\"arrow-right\" size=\"20\" color=\"#c0c4cc\"></u-icon>\n      </view>\n      \n      <view class=\"settings-divider\"></view>\n      \n      <view class=\"settings-item\" @click=\"onAccount\">\n        <view class=\"settings-left\">\n          <view class=\"custom-icon security-icon\">\n            <u-icon name=\"lock\" size=\"28\" color=\"#ff9900\"></u-icon>\n          </view>\n          <text class=\"settings-title\">{{ lang === 'zh' ? '账户安全' : 'Account Security' }}</text>\n        </view>\n        <u-icon name=\"arrow-right\" size=\"20\" color=\"#c0c4cc\"></u-icon>\n      </view>\n      \n      <view class=\"settings-divider\"></view>\n      \n      <!-- 在线客服 - 整个区域都是按钮 -->\n      <button open-type=\"contact\" class=\"contact-btn-full\" @contact=\"onContactSuccess\" @error=\"onContactFail\">\n        <view class=\"settings-item service-item\">\n          <view class=\"settings-left\">\n            <view class=\"custom-icon service-icon\">\n              <u-icon name=\"kefu-ermai\" size=\"28\" color=\"#19be6b\"></u-icon>\n            </view>\n            <text class=\"settings-title\">{{ lang === 'zh' ? '在线客服' : 'Customer Service' }}</text>\n          </view>\n          <view class=\"arrow-right\">\n            <u-icon name=\"arrow-right\" size=\"20\" color=\"#c0c4cc\"></u-icon>\n          </view>\n        </view>\n      </button>\n    </view>\n\n    <CustomTabbar :current=\"3\" />\n    <LangSwitch />\n  </view>\n</template>\n\n<script>\nimport CustomTabbar from '@/components/CustomTabbar.vue';\nimport LangSwitch from '@/components/LangSwitch.vue';\nimport { postUserInfo } from '@/api/user.js';\n\nexport default {\n  components: {\n    CustomTabbar,\n    LangSwitch\n  },\n  data() {\n    return {\n      lang: 'zh',\n      user: {\n        avatar: 'https://picsum.photos/200',\n        nickname: 'User123',\n        signature: '这是个性签名',\n        desc: '这里是用户描述信息'\n      },\n      points: 1280\n    };\n  },\n  onShow() {\n    this.loadLocalUserInfo();\n  },\n  mounted() {\n    // 获取语言设置\n    const lang = uni.getStorageSync('lang') || 'zh';\n    this.lang = lang;\n    \n    // 监听语言变化\n    uni.$on('lang-change', this.onLangChange);\n    \n    // 设置导航栏标题\n    this.setNavTitle();\n    \n    // 获取用户信息\n    this.getUserInfo();\n  },\n  beforeDestroy() {\n    uni.$off('lang-change', this.onLangChange);\n  },\n  methods: {\n        async getUserInfo() {\n      try {\n        // 调用接口获取用户信息\n        const response = await postUserInfo();\n        \n        if (response && response.code === 0 && response.data) {\n          // 处理头像URL - 如果是相对路径，需要拼接完整URL\n          let avatarUrl = response.data.head || 'https://picsum.photos/200';\n          if (avatarUrl.startsWith('/')) {\n            avatarUrl = 'https://xialingying.guaguakj.com' + avatarUrl;\n          }\n          \n          // 更新用户信息 - 根据实际返回的字段映射\n          this.user = {\n            id: response.data.userID,\n            avatar: avatarUrl,\n            nickname: response.data.name || 'User123',\n            signature: '这是个性签名', // 接口暂无此字段，使用默认值\n            desc: '这里是用户描述信息', // 接口暂无此字段，使用默认值\n            level: response.data.level || 0,\n            money: response.data.money || 0,\n            cardNums: response.data.cardNums || 0,\n            isHaveEWM: response.data.isHaveEWM || 0\n          };\n          \n          // 更新积分 - 使用 integral 字段\n          this.points = response.data.integral || 0;\n          \n          // 保存到本地存储\n          uni.setStorageSync('userInfo', this.user);\n          uni.setStorageSync('userPoints', this.points);\n          \n          console.log('用户信息获取成功:', response.data);\n        } else {\n          console.error('获取用户信息失败:', response);\n          // 失败时从本地存储获取\n          this.loadLocalUserInfo();\n        }\n      } catch (error) {\n        console.error('获取用户信息异常:', error);\n        // 异常时从本地存储获取\n        this.loadLocalUserInfo();\n        uni.showToast({\n          title: this.lang === 'zh' ? '获取用户信息失败' : 'Failed to get user info',\n          icon: 'none'\n        });\n      }\n    },\n    \n    loadLocalUserInfo() {\n      // 从本地存储获取用户信息\n      const userInfo = uni.getStorageSync('userInfo');\n      if (userInfo) {\n        this.user = userInfo;\n      }\n    },\n    onLangChange(newLang) {\n      this.lang = newLang;\n      this.setNavTitle();\n    },\n    setNavTitle() {\n      const title = this.lang === 'zh' ? '我的' : 'Profile';\n      uni.setNavigationBarTitle({ title });\n    },\n    onEditProfile() {\n      uni.navigateTo({\n        url: '/pages/profile/edit'\n      });\n    },\n    onExchange() {\n      uni.navigateTo({\n        url: '/pages/points/exchange'\n      });\n    },\n    onPointsRecord() {\n      uni.navigateTo({\n        url: '/pages/points/record'\n      });\n    },\n    onSignupManage() {\n      uni.navigateTo({\n        url: '/pages/signup/manage'\n      });\n    },\n    onPrivacy() {\n      uni.navigateTo({\n        url: '/pages/profile/privacy'\n      });\n    },\n    onAccount() {\n      uni.navigateTo({\n        url: '/pages/profile/account'\n      });\n    },\n    onService() {\n      // 不执行任何操作，让button的open-type处理\n    },\n    onContactSuccess(e) {\n      console.log('客服联系成功', e);\n    },\n    onContactFail(e) {\n      console.log('客服联系失败', e);\n      uni.showToast({\n        title: this.lang === 'zh' ? '客服联系失败' : 'Contact failed',\n        icon: 'none'\n      });\n    }\n  }\n}\n</script>\n\n<style scoped>\n.profile-page {\n  min-height: 100vh;\n  box-sizing: border-box;\n  padding-bottom: 100rpx;\n  padding-top: 20rpx;\n}\n.profile-header {\n  background: #f5f7fa;\n  border-radius: 12rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);\n  padding: 0 24rpx;\n  margin: 0 24rpx;\n  margin-bottom: 24rpx;\n  box-sizing: border-box;\n}\n.profile-header-inner {\n  display: flex;\n  align-items: center;\n  padding: 32rpx 0 24rpx 0;\n  position: relative;\n}\n.profile-info {\n  flex: 1;\n  margin-left: 24rpx;\n}\n.profile-nick {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #222;\n  display: flex;\n  align-items: center;\n}\n.level-badge {\n  background: linear-gradient(45deg, #ff9900, #ffcc00);\n  color: #fff;\n  font-size: 20rpx;\n  padding: 4rpx 8rpx;\n  border-radius: 8rpx;\n  margin-left: 12rpx;\n  font-weight: normal;\n}\n.profile-sign {\n  font-size: 26rpx;\n  color: #888;\n  margin-top: 8rpx;\n}\n.profile-desc {\n  font-size: 24rpx;\n  color: #aaa;\n  margin-top: 8rpx;\n}\n.profile-money {\n  font-size: 24rpx;\n  color: #19be6b;\n  margin-top: 8rpx;\n  font-weight: 500;\n}\n.loading-text {\n  font-size: 22rpx;\n  color: #2979ff;\n  margin-top: 8rpx;\n}\n.profile-edit {\n  position: absolute;\n  right: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #c0c4cc;\n}\n.section-card {\n  background: #f5f7fa;\n  border-radius: 12rpx;\n  margin: 0 24rpx 24rpx 24rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);\n  border: 1rpx solid #f0f0f0;\n  padding-bottom: 8rpx;\n}\n.profile-points-bar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 24rpx;\n}\n.points-left {\n  display: flex;\n  align-items: baseline;\n}\n.points-right {\n  flex: 1;\n  margin-left: 24rpx;\n  max-width: 60%;\n}\n.points-num {\n  color: #ff9900;\n  font-weight: bold;\n  font-size: 40rpx;\n}\n.points-unit {\n  font-size: 26rpx;\n  color: #888;\n  margin-left: 8rpx;\n}\n.exchange-btn {\n  width: 100%;\n  height: 80rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.divider {\n  height: 1px;\n  background-color: #eee;\n  margin: 0;\n}\n.points-record {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24rpx;\n}\n.record-text {\n  font-size: 28rpx;\n  color: #333;\n}\n.section-title {\n  font-size: 28rpx;\n  font-weight: bold;\n  padding: 24rpx 0 8rpx 24rpx;\n  color: #2979ff;\n}\n.signup-card {\n  margin-top: 0;\n  padding: 24rpx;\n  background: #f5f7fa;\n  border-radius: 12rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);\n  border: 1rpx solid #f0f0f0;\n}\n.signup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16rpx;\n}\n.signup-title {\n  display: flex;\n  align-items: center;\n}\n.signup-title text {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #2979ff;\n}\n.signup-icon {\n  margin-right: 12rpx;\n}\n.signup-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-top: 12rpx;\n}\n.signup-desc {\n  font-size: 26rpx;\n  color: #666;\n  flex: 1;\n  margin-right: 16rpx;\n}\n.signup-tag {\n  margin-left: 16rpx;\n}\n.settings-card {\n  margin-top: 0;\n  padding: 24rpx;\n  background: #f5f7fa;\n  border-radius: 12rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);\n  border: 1rpx solid #f0f0f0;\n}\n.settings-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16rpx 0;\n}\n.settings-left {\n  display: flex;\n  align-items: center;\n}\n.settings-title {\n  font-size: 28rpx;\n  color: #333;\n  margin-left: 16rpx;\n}\n.settings-divider {\n  height: 1px;\n  background-color: #eee;\n  margin: 8rpx 0;\n}\n.custom-icon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 30rpx;\n  margin-right: 12rpx;\n}\n.privacy-icon {\n  background-color: #e6f2ff; /* 浅蓝色背景 */\n}\n.security-icon {\n  background-color: #fff5e6; /* 浅橙色背景 */\n}\n.service-icon {\n  background-color: #e6f9f0; /* 浅绿色背景 */\n}\n.contact-btn {\n  padding: 0;\n  margin: 0;\n  line-height: 1;\n  height: auto;\n  background-color: transparent;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 0;\n  width: auto;\n  overflow: visible;\n}\n.contact-btn::after {\n  border: none;\n}\n.contact-btn-full {\n  width: 100%;\n  display: block;\n  padding: 0;\n  margin: 0;\n  background-color: transparent;\n  border: none;\n  line-height: normal;\n  text-align: left;\n}\n.contact-btn-full::after {\n  border: none;\n}\n.service-item {\n  width: 100%;\n  box-sizing: border-box;\n}\n.arrow-right {\n  display: flex;\n  align-items: center;\n}\n</style> ", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=14bc1b43&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=14bc1b43&scoped=true&lang=css&\""], "sourceRoot": ""}