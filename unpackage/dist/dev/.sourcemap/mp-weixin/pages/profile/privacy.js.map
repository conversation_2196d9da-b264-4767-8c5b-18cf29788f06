{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/profile/privacy.vue?9449", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/profile/privacy.vue?841f", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/profile/privacy.vue?fd7e", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/profile/privacy.vue?b3a9", "uni-app:///pages/profile/privacy.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/profile/privacy.vue?e025"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "LangSwitch", "data", "lang", "loading", "privacyContent", "title", "content", "mounted", "uni", "<PERSON><PERSON><PERSON><PERSON>", "methods", "getPrivacyContent", "id", "response", "console", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onLangChange", "setNavTitle"], "mappings": "gJAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,4BACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,iCCRvB,yHAAk4B,eAAG,G,qJCwBr4B,wHAEA,CACAV,YACAW,cAEAC,gBACA,OACAC,UACAC,WACAC,gBACAC,SACAC,cAIAC,mBAEA,qCACA,YAGAC,uCAGA,mBAGA,0BAEAC,yBACAD,yCAEAE,SAEAC,6BAAA,0IAIA,OAJA,SAEA,aAEA,UACA,qBAAAC,OAAA,OAAAC,SAEA,sBACA,kBACAR,SACAC,iBAGAQ,6BAEA,uBACA,mDAEAA,gCAEA,8BAEA,OAFA,UAEA,wFAtBA,IA2BAC,6BACA,qBACAV,SACAC,yBACA,oDACA,6LAIAU,yBACA,YACA,mBAEA,0BAEAC,uBACA,+CACAT,yBAAAH,aAGA,c,6DC1GA,yHAAyxC,eAAG,G", "file": "pages/profile/privacy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/profile/privacy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./privacy.vue?vue&type=template&id=1e9ef8b9&scoped=true&\"\nvar renderjs\nimport script from \"./privacy.vue?vue&type=script&lang=js&\"\nexport * from \"./privacy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./privacy.vue?vue&type=style&index=0&id=1e9ef8b9&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1e9ef8b9\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/profile/privacy.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./privacy.vue?vue&type=template&id=1e9ef8b9&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./privacy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./privacy.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"privacy-page\">\n    <view class=\"privacy-container\">\n      <!-- 加载状态 -->\n      <view v-if=\"loading\" class=\"loading-container\">\n        <text class=\"loading-text\">{{ lang === 'zh' ? '加载中...' : 'Loading...' }}</text>\n      </view>\n      \n      <!-- 内容显示 -->\n      <view v-else class=\"content-container\">\n        <!-- 内容 -->\n        <view class=\"content-body\">\n          <rich-text :nodes=\"privacyContent.content || '暂无内容'\"></rich-text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 添加语言切换组件 -->\n    <LangSwitch />\n  </view>\n</template>\n\n<script>\nimport LangSwitch from '@/components/LangSwitch.vue';\nimport { postSingleInfo } from '@/api/apiApplet.js';\n\nexport default {\n  components: {\n    LangSwitch\n  },\n  data() {\n    return {\n      lang: 'zh',\n      loading: true,\n      privacyContent: {\n        title: '',\n        content: ''\n      }\n    };\n  },\n  mounted() {\n    // 获取语言设置\n    const lang = uni.getStorageSync('lang') || 'zh';\n    this.lang = lang;\n    \n    // 监听语言变化\n    uni.$on('lang-change', this.onLangChange);\n    \n    // 设置导航栏标题\n    this.setNavTitle();\n    \n    // 获取隐私政策内容\n    this.getPrivacyContent();\n  },\n  beforeDestroy() {\n    uni.$off('lang-change', this.onLangChange);\n  },\n  methods: {\n    // 获取隐私政策内容\n    async getPrivacyContent() {\n      try {\n        this.loading = true;\n        \n        // 调用接口，传递id为2\n        const response = await postSingleInfo({ id: 2 });\n        \n        if (response && response.code === 0 && response.data) {\n          this.privacyContent = {\n            title: '',\n            content: response.data\n          };\n        } else {\n          console.error('获取隐私政策失败:', response);\n          // 失败时使用默认内容\n          this.setDefaultContent();\n        }\n      } catch (error) {\n        console.error('获取隐私政策异常:', error);\n        // 异常时使用默认内容\n        this.setDefaultContent();\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    // 设置默认内容（作为备用方案）\n    setDefaultContent() {\n      this.privacyContent = {\n        title: '',\n        content: this.lang === 'zh' \n          ? '我们致力于保护您的隐私和个人信息安全。请仔细阅读本隐私政策以了解我们如何收集、使用和保护您的信息。'\n          : 'We are committed to protecting your privacy and personal information security. Please read this privacy policy carefully to understand how we collect, use and protect your information.'\n      };\n    },\n    \n    onLangChange(newLang) {\n      this.lang = newLang;\n      this.setNavTitle();\n      // 语言切换时重新获取内容\n      this.getPrivacyContent();\n    },\n    setNavTitle() {\n      const title = this.lang === 'zh' ? '隐私协议' : 'Privacy Policy';\n      uni.setNavigationBarTitle({ title });\n    }\n  }\n}\n</script>\n\n<style scoped>\n.privacy-page {\n  min-height: 100vh;\n  padding: 30rpx;\n  box-sizing: border-box;\n  background-color: #f5f7fa;\n}\n.privacy-container {\n  background-color: #fff;\n  border-radius: 12rpx;\n  padding: 30rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);\n  min-height: 500rpx;\n}\n\n/* 加载状态样式 */\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 400rpx;\n}\n.loading-text {\n  font-size: 28rpx;\n  color: #2979ff;\n}\n\n/* 内容样式 */\n.content-container {\n  min-height: 400rpx;\n}\n.content-body {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.8;\n}\n\n/* 富文本内容样式优化 */\n.content-body rich-text {\n  word-break: break-all;\n}\n</style> ", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./privacy.vue?vue&type=style&index=0&id=1e9ef8b9&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./privacy.vue?vue&type=style&index=0&id=1e9ef8b9&scoped=true&lang=css&\""], "sourceRoot": ""}