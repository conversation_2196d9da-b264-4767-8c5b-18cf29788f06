{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/survey/result.vue?eae1", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/survey/result.vue?9682", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/survey/result.vue?d7ae", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/survey/result.vue?7245", "uni-app:///pages/survey/result.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/survey/result.vue?1e0f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uIcon", "uButton", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "LangSwitch", "data", "lang", "activityId", "result", "language", "logic", "creativity", "social", "physical", "recommendedCourses", "icon", "name", "name_en", "desc", "desc_en", "suggestions", "zh", "en", "onLoad", "uni", "onUnload", "methods", "viewActivityDetail", "url", "downloadReport", "title", "setTimeout", "goBack", "onLangChange", "setNavigationBarTitle"], "mappings": "8IAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,0BACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,MAAO,WACL,OAAO,kHAITC,QAAS,WACP,OAAO,uHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,iCCtCvB,yHAAi4B,eAAG,G,mOCsGp4B,CACAjB,YACAkB,cAEAC,gBACA,OACAC,oCACAC,gBACAC,QACAC,YACAC,SACAC,cACAC,UACAC,aAEAC,oBACA,CACAC,gBACAC,cACAC,kCACAC,uCACAC,4HAEA,CACAJ,YACAC,eACAC,2CACAC,2BACAC,iGAEA,CACAJ,YACAC,eACAC,kCACAC,6BACAC,iGAGAC,aACAC,yGACAC,sZAIAC,mBAEA,OACA,sBAIA,yCAGA,gDACA,oCAIAC,uCAGA,8BAEAC,oBAEAD,yCAEAE,SACAC,8BACAH,cACAI,0DAGAC,0BAAA,WACAL,eACAM,2DAIAC,uBACAP,gBAEAA,aACAM,6DACAf,mBAEA,MAEAiB,kBACAR,kBAEAS,yBACA,YAEA,8BAEAC,iCACAV,yBACAM,qDAIA,c,6DC7MA,yHAAgwC,eAAG,G", "file": "pages/survey/result.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/survey/result.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./result.vue?vue&type=template&id=4d29fbc9&\"\nvar renderjs\nimport script from \"./result.vue?vue&type=script&lang=js&\"\nexport * from \"./result.vue?vue&type=script&lang=js&\"\nimport style0 from \"./result.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/survey/result.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=template&id=4d29fbc9&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-button/u-button\" */ \"uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"result-page\">\n    <!-- 结果卡片 -->\n    <view class=\"result-card\">\n      <view class=\"card-header\">\n        <u-icon name=\"checkmark-circle\" color=\"#19be6b\" size=\"60\"></u-icon>\n        <view class=\"header-text\">{{ lang === 'zh' ? '问卷已完成' : 'Survey Completed' }}</view>\n      </view>\n      \n      <view class=\"divider\"></view>\n      \n      <!-- 认知水平评估结果 -->\n      <view class=\"result-section\">\n        <view class=\"section-title\">{{ lang === 'zh' ? '认知水平评估' : 'Cognitive Level Assessment' }}</view>\n        \n        <view class=\"level-chart\">\n          <view class=\"chart-item\">\n            <view class=\"chart-label\">{{ lang === 'zh' ? '语言能力' : 'Language' }}</view>\n            <view class=\"chart-bar\">\n              <view class=\"chart-fill\" :style=\"{width: result.language + '%'}\"></view>\n            </view>\n            <view class=\"chart-value\">{{ result.language }}%</view>\n          </view>\n          \n          <view class=\"chart-item\">\n            <view class=\"chart-label\">{{ lang === 'zh' ? '逻辑思维' : 'Logic' }}</view>\n            <view class=\"chart-bar\">\n              <view class=\"chart-fill\" :style=\"{width: result.logic + '%'}\"></view>\n            </view>\n            <view class=\"chart-value\">{{ result.logic }}%</view>\n          </view>\n          \n          <view class=\"chart-item\">\n            <view class=\"chart-label\">{{ lang === 'zh' ? '创造力' : 'Creativity' }}</view>\n            <view class=\"chart-bar\">\n              <view class=\"chart-fill\" :style=\"{width: result.creativity + '%'}\"></view>\n            </view>\n            <view class=\"chart-value\">{{ result.creativity }}%</view>\n          </view>\n          \n          <view class=\"chart-item\">\n            <view class=\"chart-label\">{{ lang === 'zh' ? '社交能力' : 'Social' }}</view>\n            <view class=\"chart-bar\">\n              <view class=\"chart-fill\" :style=\"{width: result.social + '%'}\"></view>\n            </view>\n            <view class=\"chart-value\">{{ result.social }}%</view>\n          </view>\n          \n          <view class=\"chart-item\">\n            <view class=\"chart-label\">{{ lang === 'zh' ? '运动能力' : 'Physical' }}</view>\n            <view class=\"chart-bar\">\n              <view class=\"chart-fill\" :style=\"{width: result.physical + '%'}\"></view>\n            </view>\n            <view class=\"chart-value\">{{ result.physical }}%</view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 课程推荐 -->\n      <!-- <view class=\"divider\"></view>\n      <view class=\"result-section\">\n        <view class=\"section-title\">{{ lang === 'zh' ? '课程推荐' : 'Course Recommendations' }}</view>\n        \n        <view class=\"course-list\">\n          <view class=\"course-item\" v-for=\"(course, index) in recommendedCourses\" :key=\"index\">\n            <view class=\"course-icon\">\n              <u-icon :name=\"course.icon\" size=\"40\" color=\"#409eff\"></u-icon>\n            </view>\n            <view class=\"course-info\">\n              <view class=\"course-name\">{{ lang === 'zh' ? course.name : course.name_en }}</view>\n              <view class=\"course-desc\">{{ lang === 'zh' ? course.desc : course.desc_en }}</view>\n            </view>\n          </view>\n        </view>\n      </view> -->\n      \n      <view class=\"divider\"></view>\n      \n      <!-- 个性化建议 -->\n      <view class=\"result-section\">\n        <view class=\"section-title\">{{ lang === 'zh' ? '个性化建议' : 'Personalized Suggestions' }}</view>\n        \n        <view class=\"suggestion-content\">\n          {{ lang === 'zh' ? suggestions.zh : suggestions.en }}\n        </view>\n      </view>\n    </view>\n    \n    <!-- 按钮区域 -->\n    <view class=\"button-group safe-area-inset-bottom\">\n      <u-button type=\"primary\" @click=\"viewActivityDetail\">{{ lang === 'zh' ? '查看活动详情' : 'View Activity Details' }}</u-button>\n      <!-- <u-button type=\"info\" @click=\"downloadReport\">{{ lang === 'zh' ? '下载完整报告' : 'Download Full Report' }}</u-button> -->\n    </view>\n    \n    <!-- 语言切换按钮 -->\n    <LangSwitch />\n  </view>\n</template>\n\n<script>\nimport LangSwitch from '@/components/LangSwitch.vue';\n\nexport default {\n  components: {\n    LangSwitch\n  },\n  data() {\n    return {\n      lang: uni.getStorageSync('lang') || 'zh',\n      activityId: null,\n      result: {\n        language: 85,\n        logic: 70,\n        creativity: 90,\n        social: 65,\n        physical: 75\n      },\n      recommendedCourses: [\n        {\n          icon: 'edit-pen',\n          name: '创意绘画课程',\n          name_en: 'Creative Drawing Course',\n          desc: '根据孩子的创造力水平，推荐参加创意绘画课程，培养艺术感知能力。',\n          desc_en: 'Based on your child\\'s creativity level, we recommend the Creative Drawing Course to develop artistic perception.'\n        },\n        {\n          icon: 'chat',\n          name: '英语口语强化班',\n          name_en: 'English Speaking Intensive Class',\n          desc: '提高孩子的语言表达能力，增强沟通自信。',\n          desc_en: 'Improve your child\\'s language expression skills and enhance communication confidence.'\n        },\n        {\n          icon: 'grid',\n          name: '团队协作训练营',\n          name_en: 'Team Collaboration Camp',\n          desc: '通过团队活动提升孩子的社交能力和协作精神。',\n          desc_en: 'Enhance your child\\'s social skills and collaborative spirit through team activities.'\n        }\n      ],\n      suggestions: {\n        zh: '根据问卷结果，您的孩子在创造力方面表现出色，建议多参加艺术类和创新思维类的活动。语言能力也较强，可以通过阅读和口语练习进一步提升。社交能力相对较弱，建议参加更多团队活动，培养合作精神和人际交往能力。',\n        en: 'Based on the survey results, your child shows excellent performance in creativity. We recommend more participation in artistic and innovative thinking activities. Language ability is also strong and can be further improved through reading and speaking practice. Social skills are relatively weak, so we suggest participating in more team activities to develop cooperation and interpersonal skills.'\n      }\n    };\n  },\n  onLoad(options) {\n    // 获取活动ID\n    if (options.id) {\n      this.activityId = options.id;\n    }\n    \n    // 从本地存储获取语言设置\n    this.lang = uni.getStorageSync('lang') || 'zh';\n    \n    // 从全局获取语言设置\n    if (getApp().globalData && getApp().globalData.lang) {\n      this.lang = getApp().globalData.lang;\n    }\n    \n    // 监听语言变化\n    uni.$on('lang-change', this.onLangChange);\n    \n    // 设置导航栏标题\n    this.setNavigationBarTitle();\n  },\n  onUnload() {\n    // 取消监听语言变化\n    uni.$off('lang-change', this.onLangChange);\n  },\n  methods: {\n    viewActivityDetail() {\n      uni.navigateTo({\n        url: `/pages/events/detail?id=${this.activityId}`\n      });\n    },\n    downloadReport() {\n      uni.showLoading({\n        title: this.lang === 'zh' ? '准备下载...' : 'Preparing download...'\n      });\n      \n      // 模拟下载\n      setTimeout(() => {\n        uni.hideLoading();\n        \n        uni.showToast({\n          title: this.lang === 'zh' ? '报告已保存到手机' : 'Report saved to your device',\n          icon: 'success'\n        });\n      }, 2000);\n    },\n    goBack() {\n      uni.navigateBack();\n    },\n    onLangChange(newLang) {\n      this.lang = newLang;\n      // 更新导航栏标题\n      this.setNavigationBarTitle();\n    },\n    setNavigationBarTitle() {\n      uni.setNavigationBarTitle({\n        title: this.lang === 'zh' ? '调查结果反馈' : 'Survey Result'\n      });\n    }\n  }\n};\n</script>\n\n<style>\n.result-page {\n  min-height: 100vh;\n  padding-bottom: 40rpx;\n  box-sizing: border-box;\n  background-color: #f5f5f5;\n  padding-top: 20rpx;\n}\n\n.navbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24rpx 30rpx;\n  background-color: #ffffff;\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.navbar-left, .navbar-right {\n  padding: 10rpx;\n  width: 80rpx;\n}\n\n.navbar-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #303133;\n  flex-grow: 1;\n  text-align: center;\n}\n\n.result-card {\n  margin: 30rpx;\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n}\n\n.card-header {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 40rpx 30rpx;\n}\n\n.header-text {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #303133;\n  margin-top: 20rpx;\n}\n\n.divider {\n  height: 1rpx;\n  background-color: #ebeef5;\n  margin: 0 30rpx;\n}\n\n.result-section {\n  padding: 30rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 24rpx;\n}\n\n.level-chart {\n}\n\n.chart-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20rpx;\n}\n\n.chart-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #606266;\n}\n\n.chart-bar {\n  flex: 1;\n  height: 30rpx;\n  background-color: #ebeef5;\n  border-radius: 15rpx;\n  overflow: hidden;\n  margin: 0 20rpx;\n}\n\n.chart-fill {\n  height: 100%;\n  background-color: #409eff;\n  border-radius: 15rpx;\n}\n\n.chart-value {\n  width: 80rpx;\n  font-size: 24rpx;\n  color: #909399;\n  text-align: right;\n}\n\n.course-list {\n  margin-top: 20rpx;\n}\n\n.course-item {\n  display: flex;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #ebeef5;\n}\n\n.course-item:last-child {\n  border-bottom: none;\n}\n\n.course-icon {\n  margin-right: 20rpx;\n  display: flex;\n  align-items: center;\n}\n\n.course-info {\n  flex: 1;\n}\n\n.course-name {\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 8rpx;\n}\n\n.course-desc {\n  font-size: 26rpx;\n  color: #606266;\n  line-height: 1.5;\n}\n\n.suggestion-content {\n  font-size: 28rpx;\n  color: #606266;\n  line-height: 1.6;\n}\n\n.button-group {\n  margin: 40rpx 30rpx;\n  display: flex;\n  flex-direction: column;\n  gap: 20rpx;\n}\n\n.safe-area-inset-bottom {\n  padding-bottom: calc(40rpx + constant(safe-area-inset-bottom));\n  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));\n}\n</style> ", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=style&index=0&lang=css&\""], "sourceRoot": ""}