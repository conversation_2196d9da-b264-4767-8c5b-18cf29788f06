{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/survey/index.vue?f541", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/survey/index.vue?0cf7", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/survey/index.vue?03cb", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/survey/index.vue?2364", "uni-app:///pages/survey/index.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/survey/index.vue?b7eb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uRadioGroup", "uRadio", "uCheckboxGroup", "uCheckbox", "uInput", "uButton", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "t0", "_self", "_c", "lang", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "LangSwitch", "activityId", "answers", "questions", "type", "title", "title_en", "options_en", "onLoad", "uni", "onUnload", "methods", "initAnswers", "onLangChange", "setNavigationBarTitle", "submit<PERSON><PERSON>vey", "setTimeout", "icon", "url", "unansweredIndex"], "mappings": "6IAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,yBACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,YAAa,WACX,OAAO,gIAITC,OAAQ,WACN,OAAO,oHAITC,eAAgB,WACd,OAAO,sIAITC,UAAW,WACT,OAAO,0HAITC,OAAQ,WACN,OAAO,oHAITC,QAAS,WACP,OAAO,uHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GAEJ,OAAbN,EAAIO,KACA,0DACA,sMACNP,EAAIQ,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLR,GAAIA,MAKRS,GAAmB,EACnBC,EAAkB,GACtBf,EAAOgB,eAAgB,G,iCCtEvB,yHAAg4B,eAAG,G,mOCuEn4B,CACA5B,YACA6B,cAEAP,gBACA,OACAF,oCACAU,gBACAC,WACAC,WACA,CACAC,aACAC,iBACAC,qCACArC,iDACAsC,6EAEA,CACAH,aACAC,2BACAC,2EACArC,uCACAsC,iEAEA,CACAH,gBACAC,+BACAC,6FACArC,4DACAsC,8IAEA,CACAH,aACAC,4BACAC,2EACArC,0CACAsC,0EAEA,CACAH,aACAC,4BACAC,sEACArC,8CACAsC,6FAEA,CACAH,YACAC,+BACAC,sHAKAE,mBAEA,OACA,sBAIA,mBAGA,yCAGA,gDACA,oCAIAC,uCAGA,8BAEAC,oBAEAD,yCAEAE,SACAC,uBAEA,6CACA,0BACA,GAEA,OAGAC,yBACA,YAEA,8BAEAC,iCACAL,yBACAJ,8DAGAU,wBAAA,WAEA,wCACA,wBACA,aAEA,WAGA,OAWAN,eACAJ,kDAIAW,uBACAP,gBAGAA,aACAJ,oDACAY,iBAIAD,uBACAP,cACAS,wDAEA,QACA,MA9BAT,aACAJ,qCACAc,4CACAA,KACAF,iBA6BA,c,6DCtNA,yHAA+vC,eAAG,G", "file": "pages/survey/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/survey/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=25f90856&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/survey/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=25f90856&\"", "var components\ntry {\n  components = {\n    uRadioGroup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-radio-group/u-radio-group\" */ \"uview-ui/components/u-radio-group/u-radio-group.vue\"\n      )\n    },\n    uRadio: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-radio/u-radio\" */ \"uview-ui/components/u-radio/u-radio.vue\"\n      )\n    },\n    uCheckboxGroup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-checkbox-group/u-checkbox-group\" */ \"uview-ui/components/u-checkbox-group/u-checkbox-group.vue\"\n      )\n    },\n    uCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-checkbox/u-checkbox\" */ \"uview-ui/components/u-checkbox/u-checkbox.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-input/u-input\" */ \"uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-button/u-button\" */ \"uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var t0 =\n    _vm.lang === \"zh\"\n      ? \"为了更好地了解您孩子的认知水平，请您花几分钟时间填写以下问卷。这将帮助我们为您的孩子提供更加个性化的培训课程。\"\n      : \"To better understand your child's cognitive level, please take a few minutes to complete the following questionnaire. This will help us provide more personalized training courses for your child.\"\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        t0: t0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"survey-page\">\n    <!-- 问卷说明 -->\n    <view class=\"survey-intro\">\n      <view class=\"intro-title\">{{ lang === 'zh' ? '亲爱的家长' : 'Dear Parent' }}</view>\n      <view class=\"intro-text\">\n        {{ lang === 'zh' ? \n          '为了更好地了解您孩子的认知水平，请您花几分钟时间填写以下问卷。这将帮助我们为您的孩子提供更加个性化的培训课程。' : \n          'To better understand your child\\'s cognitive level, please take a few minutes to complete the following questionnaire. This will help us provide more personalized training courses for your child.' \n        }}\n      </view>\n    </view>\n    \n    <!-- 问卷内容 -->\n    <view class=\"survey-content\">\n      <view class=\"question-item\" v-for=\"(question, index) in questions\" :key=\"index\">\n        <view class=\"question-title\">{{ index + 1 }}. {{ lang === 'zh' ? question.title : question.title_en }}</view>\n        \n        <!-- 单选题 -->\n        <view class=\"options-list\" v-if=\"question.type === 'radio'\">\n          <u-radio-group v-model=\"answers[index]\" placement=\"column\">\n            <u-radio\n              v-for=\"(option, optIndex) in lang === 'zh' ? question.options : question.options_en\" \n              :key=\"optIndex\"\n              :label=\"option\"\n              :name=\"optIndex\"\n              :customStyle=\"{marginBottom: '20rpx'}\"\n            >\n            </u-radio>\n          </u-radio-group>\n        </view>\n        \n        <!-- 多选题 -->\n        <view class=\"options-list\" v-if=\"question.type === 'checkbox'\">\n          <u-checkbox-group v-model=\"answers[index]\" placement=\"column\">\n            <u-checkbox\n              v-for=\"(option, optIndex) in lang === 'zh' ? question.options : question.options_en\" \n              :key=\"optIndex\"\n              :label=\"option\"\n              :name=\"optIndex\"\n              :customStyle=\"{marginBottom: '20rpx'}\"\n            >\n            </u-checkbox>\n          </u-checkbox-group>\n        </view>\n        \n        <!-- 文本输入 -->\n        <view class=\"text-input\" v-if=\"question.type === 'text'\">\n          <u-input \n            v-model=\"answers[index]\" \n            type=\"text\" \n            border=\"bottom\"\n            :placeholder=\"lang === 'zh' ? '请输入您的回答' : 'Please enter your answer'\"\n          />\n        </view>\n      </view>\n    </view>\n    \n    <!-- 提交按钮 -->\n    <view class=\"submit-section safe-area-inset-bottom\">\n      <u-button type=\"primary\" @click=\"submitSurvey\">{{ lang === 'zh' ? '提交问卷' : 'Submit Survey' }}</u-button>\n    </view>\n    \n    <!-- 语言切换按钮 -->\n    <LangSwitch />\n  </view>\n</template>\n\n<script>\nimport LangSwitch from '@/components/LangSwitch.vue';\n\nexport default {\n  components: {\n    LangSwitch\n  },\n  data() {\n    return {\n      lang: uni.getStorageSync('lang') || 'zh',\n      activityId: null,\n      answers: [],\n      questions: [\n        {\n          type: 'radio',\n          title: '您的孩子年龄是？',\n          title_en: 'What is your child\\'s age?',\n          options: ['3-5岁', '6-8岁', '9-12岁', '13-15岁', '16岁以上'],\n          options_en: ['3-5 years', '6-8 years', '9-12 years', '13-15 years', '16+ years']\n        },\n        {\n          type: 'radio',\n          title: '您的孩子是否有参加过类似的培训课程？',\n          title_en: 'Has your child participated in similar training courses before?',\n          options: ['是，多次参加', '是，参加过1-2次', '没有参加过'],\n          options_en: ['Yes, multiple times', 'Yes, 1-2 times', 'No, never']\n        },\n        {\n          type: 'checkbox',\n          title: '您的孩子对以下哪些领域表现出兴趣？（可多选）',\n          title_en: 'In which of the following areas does your child show interest? (Multiple choices)',\n          options: ['艺术与创意', '体育运动', '科学探索', '语言学习', '数学逻辑', '音乐舞蹈', '社交活动'],\n          options_en: ['Arts & Creativity', 'Sports', 'Science Exploration', 'Language Learning', 'Mathematical Logic', 'Music & Dance', 'Social Activities']\n        },\n        {\n          type: 'radio',\n          title: '您的孩子在学习新知识时的接受能力如何？',\n          title_en: 'How would you rate your child\\'s ability to learn new knowledge?',\n          options: ['非常快', '较快', '一般', '需要反复学习', '学习困难'],\n          options_en: ['Very fast', 'Fast', 'Average', 'Needs repetition', 'Difficult']\n        },\n        {\n          type: 'radio',\n          title: '您的孩子在团队活动中通常扮演什么角色？',\n          title_en: 'What role does your child usually play in team activities?',\n          options: ['领导者', '积极参与者', '跟随者', '观察者', '不喜欢团队活动'],\n          options_en: ['Leader', 'Active participant', 'Follower', 'Observer', 'Dislikes team activities']\n        },\n        {\n          type: 'text',\n          title: '您对孩子参加本次培训有什么特别的期望或需求？',\n          title_en: 'Do you have any specific expectations or requirements for your child\\'s participation in this training?'\n        }\n      ]\n    };\n  },\n  onLoad(options) {\n    // 获取活动ID\n    if (options.id) {\n      this.activityId = options.id;\n    }\n    \n    // 初始化答案数组\n    this.initAnswers();\n    \n    // 从本地存储获取语言设置\n    this.lang = uni.getStorageSync('lang') || 'zh';\n    \n    // 从全局获取语言设置\n    if (getApp().globalData && getApp().globalData.lang) {\n      this.lang = getApp().globalData.lang;\n    }\n    \n    // 监听语言变化\n    uni.$on('lang-change', this.onLangChange);\n    \n    // 设置导航栏标题\n    this.setNavigationBarTitle();\n  },\n  onUnload() {\n    // 取消监听语言变化\n    uni.$off('lang-change', this.onLangChange);\n  },\n  methods: {\n    initAnswers() {\n      // 根据问题数量初始化答案数组\n      this.answers = this.questions.map(q => {\n        if (q.type === 'checkbox') {\n          return [];\n        }\n        return '';\n      });\n    },\n    onLangChange(newLang) {\n      this.lang = newLang;\n      // 更新导航栏标题\n      this.setNavigationBarTitle();\n    },\n    setNavigationBarTitle() {\n      uni.setNavigationBarTitle({\n        title: this.lang === 'zh' ? '认知水平调查问卷' : 'Cognitive Level Survey'\n      });\n    },\n    submitSurvey() {\n      // 验证是否所有问题都已回答\n      const unansweredIndex = this.answers.findIndex((answer, index) => {\n        if (Array.isArray(answer)) {\n          return answer.length === 0;\n        }\n        return answer === '';\n      });\n      \n      if (unansweredIndex !== -1) {\n        uni.showToast({\n          title: this.lang === 'zh' ? \n            `请回答第${unansweredIndex + 1}个问题` : \n            `Please answer question ${unansweredIndex + 1}`,\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 提交问卷\n      uni.showLoading({\n        title: this.lang === 'zh' ? '提交中...' : 'Submitting...'\n      });\n      \n      // 模拟提交\n      setTimeout(() => {\n        uni.hideLoading();\n        \n        // 显示提交成功\n        uni.showToast({\n          title: this.lang === 'zh' ? '提交成功' : 'Submitted successfully',\n          icon: 'success'\n        });\n        \n        // 跳转到结果页\n        setTimeout(() => {\n          uni.navigateTo({\n            url: `/pages/survey/result?id=${this.activityId}`\n          });\n        }, 1500);\n      }, 2000);\n    }\n  }\n};\n</script>\n\n<style>\n.survey-page {\n  min-height: 100vh;\n  padding-bottom: 180rpx;\n  box-sizing: border-box;\n}\n\n.survey-intro {\n  background-color: #f8f8f8;\n  padding: 30rpx;\n  margin: 30rpx;\n  border-radius: 16rpx;\n}\n\n.intro-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  margin-bottom: 16rpx;\n  color: #303133;\n}\n\n.intro-text {\n  font-size: 28rpx;\n  color: #606266;\n  line-height: 1.6;\n}\n\n.survey-content {\n  padding: 0 30rpx;\n  margin-bottom: 30rpx;\n}\n\n.question-item {\n  margin-bottom: 40rpx;\n  background-color: #ffffff;\n  padding: 30rpx;\n  border-radius: 16rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n}\n\n.question-title {\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 24rpx;\n}\n\n.options-list {\n  padding-left: 20rpx;\n}\n\n.text-input {\n  margin-top: 20rpx;\n}\n\n.submit-section {\n  padding: 40rpx 30rpx;\n  position: fixed;\n  bottom: 0;\n  box-sizing: border-box;\n  left: 0;\n  z-index: 9999999;\n  width: 100%;\n  background-color: #ffffff;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  box-sizing: border-box;\n}\n\n.safe-area-inset-bottom {\n  padding-bottom: calc(10rpx + constant(safe-area-inset-bottom));\n  padding-bottom: calc(10rpx + env(safe-area-inset-bottom));\n}\n</style> ", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\""], "sourceRoot": ""}