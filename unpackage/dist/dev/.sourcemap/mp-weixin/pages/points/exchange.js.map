{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/points/exchange.vue?5eab", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/points/exchange.vue?e4bd", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/points/exchange.vue?9927", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/points/exchange.vue?b9f1", "uni-app:///pages/points/exchange.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/points/exchange.vue?b287"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uTabs", "uImage", "uEmpty", "uPopup", "uIcon", "uButton", "uInput", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "g0", "_self", "_c", "getCurrentList", "length", "_isMounted", "e0", "$event", "showProfileSelect", "e1", "e2", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "LangSwitch", "lang", "points", "currentTab", "tabList", "activityList", "giftList", "showActivityDetail", "showGiftDetail", "showActivityJoin", "showGiftExchange", "profileList", "name", "phone", "idCard", "email", "school", "address", "isDefault", "selectedProfile", "currentActivity", "currentGift", "activityForm", "remarks", "giftForm", "computed", "onReady", "mounted", "uni", "<PERSON><PERSON><PERSON><PERSON>", "methods", "onLangChange", "setNavTitle", "title", "initTabs", "onTabChange", "getUserPoints", "getExchangeList", "id", "desc", "image", "time", "location", "stock", "viewActivityDetail", "closeActivityDetail", "viewGiftDetail", "closeGiftDetail", "joinActivity", "icon", "closeActivityJoin", "exchangeGift", "closeGiftExchange", "submitActivityJoin", "setTimeout", "userInfo", "submitGiftExchange", "selectProfile", "goToManageProfiles", "url", "initDefaultProfile", "confirmProfileSelection"], "mappings": "gJAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,4BACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,MAAO,WACL,OAAO,kHAITC,OAAQ,WACN,OAAO,oHAITC,OAAQ,WACN,OAAO,oHAITC,OAAQ,WACN,OAAO,oHAITC,MAAO,WACL,OAAO,kHAITC,QAAS,WACP,OAAO,sHAITC,OAAQ,WACN,OAAO,qHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,eAAeC,QACvBR,EAAIS,aACPT,EAAIU,GAAK,SAAUC,GACjBX,EAAIY,mBAAoB,GAE1BZ,EAAIa,GAAK,SAAUF,GACjBX,EAAIY,mBAAoB,GAE1BZ,EAAIc,GAAK,SAAUH,GACjBX,EAAIY,mBAAoB,IAG5BZ,EAAIe,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLf,GAAIA,MAKRgB,GAAmB,EACnBC,EAAkB,GACtBtB,EAAOuB,eAAgB,G,iCCnFvB,yHAAm4B,eAAG,G,mOCkTt4B,CACApC,YACAqC,cAEAP,gBACA,OACAQ,UACAC,YACAC,aACAC,WACAC,gBACAC,YACAC,sBACAC,kBACAC,oBACAC,oBACArB,qBACAsB,aACA,CACAC,UACAC,oBACAC,4BACAC,6BACAC,cACAC,yBACAC,cAEA,CACAN,UACAC,oBACAC,4BACAC,yBACAC,gBACAC,uBACAC,cAEA,CACAN,UACAC,oBACAC,4BACAC,2BACAC,cACAC,uBACAC,cAEA,CACAN,UACAC,oBACAC,4BACAC,4BACAC,cACAC,sBACAC,cAEA,CACAN,UACAC,oBACAC,4BACAC,2BACAC,cACAC,wBACAC,cAEA,CACAN,UACAC,oBACAC,4BACAC,0BACAC,cACAC,4BACAC,cAEA,CACAN,UACAC,oBACAC,4BACAC,4BACAC,cACAC,4BACAC,eAGAC,qBACAC,mBACAC,eACAC,cACAV,QACAC,SACAU,YAEAC,UACAZ,QACAC,SACAI,WACAM,cAIAE,UACAzC,0BACA,6DAGA0C,mBAEA,iBAEAC,mBAEA,qCACA,YAGAC,uCAGA,mBAGA,qBAGA,uBAGA,2BAEAC,yBACAD,yCAEAE,SACAC,yBACA,YACA,mBACA,gBACA,wBAEAC,uBACA,gDACAJ,yBAAAK,WAEAC,oBACA,cACA,CAAAtB,yCACA,CAAAA,sCAGAuB,wBACA7D,sBACA,yBAEA8D,yBAEA,qCACA,yBAMA,mCACA,uBACA,2BAPA,eAUAC,2BAKA,mBACA,CACAC,YACAL,0DACAM,6FACArC,WACAsC,+CACAC,8BACAC,oEAEA,CACAJ,YACAL,oDACAM,uGACArC,WACAsC,+CACAC,8BACAC,gDAEA,CACAJ,YACAL,4DACAM,oGACArC,WACAsC,+CACAC,8BACAC,8EAKA,eACA,CACAJ,aACAL,+CACAM,+FACArC,WACAsC,+CACAG,UAEA,CACAL,aACAL,qDACAM,kGACArC,WACAsC,+CACAG,UAEA,CACAL,aACAL,8CACAM,kHACArC,UACAsC,+CACAG,WAEA,CACAL,aACAL,2CACAM,qFACArC,WACAsC,+CACAG,YAIAC,+BACA,uBACA,4BAEAC,+BACA,4BAEAC,2BACA,mBACA,wBAEAC,2BACA,wBAEAC,yBAEA,wBACA,oBACAf,oDACAgB,cAIA,uBACA,2BACA,0BAEAC,6BACA,yBACA,mBACAtC,QACAC,SACAU,YAEA,2BAEA4B,yBAEA,wBACA,oBACAlB,oDACAgB,cAIA,mBACA,uBACA,0BAEAG,6BACA,yBACA,eACAxC,QACAC,SACAI,WACAM,aAGA8B,8BAAA,WAEA,yBACA,oBACApB,2DACAgB,cAKArB,eACAK,kDAKAqB,uBAEA,mCAGA,uCACAC,kBACA3B,+BAEAA,gBACAA,aACAK,qDACAgB,iBAIA,wBACA,MAEAO,8BAAA,WAEA,0BAMA,oBAMA,uBAQA5B,eACAK,uDAKAqB,uBAEA,+BAGA,uCACAC,kBACA3B,+BAEAA,gBACAA,aACAK,iDACAgB,iBAIA,wBACA,MA9BA,aACAhB,iEACAgB,cARA,aACAhB,8DACAgB,cARA,aACAhB,gEACAgB,eAiIAQ,0BAEA,8FACA,wBAGAC,8BAEA,0BAEA9B,cACA+B,8BAIAC,8BAEA,+DACA,IACA,yBAIAC,oCACA,8BASA,0BARAjC,aACAK,2DACAgB,iBASA,c,6DCryBA,yHAA8pD,eAAG,G", "file": "pages/points/exchange.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/points/exchange.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./exchange.vue?vue&type=template&id=45560ae6&scoped=true&\"\nvar renderjs\nimport script from \"./exchange.vue?vue&type=script&lang=js&\"\nexport * from \"./exchange.vue?vue&type=script&lang=js&\"\nimport style0 from \"./exchange.vue?vue&type=style&index=0&id=45560ae6&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"45560ae6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/points/exchange.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exchange.vue?vue&type=template&id=45560ae6&scoped=true&\"", "var components\ntry {\n  components = {\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-tabs/u-tabs\" */ \"uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uImage: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-image/u-image\" */ \"uview-ui/components/u-image/u-image.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-empty/u-empty\" */ \"uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-button/u-button\" */ \"uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-input/u-input\" */ \"uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.getCurrentList.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showProfileSelect = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.showProfileSelect = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.showProfileSelect = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exchange.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exchange.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"exchange-page\">\n    <!-- 顶部积分信息 -->\n    <view class=\"points-info\">\n      <view class=\"points-title\">{{ lang === 'zh' ? '我的积分' : 'My Points' }}</view>\n      <view class=\"points-value\">{{ points }}</view>\n    </view>\n    \n    <!-- 兑换类型选择 -->\n    <view class=\"exchange-tabs\">\n      <u-tabs\n        :list=\"tabList\"\n        :current=\"currentTab\"\n        @click=\"onTabChange\"\n        :activeStyle=\"{\n          color: '#4285f4',\n          fontWeight: 'bold',\n          transform: 'scale(1.05)'\n        }\"\n        :inactiveStyle=\"{\n          color: '#666',\n          fontWeight: 'normal',\n          transform: 'scale(1)'\n        }\"\n        itemStyle=\"padding-left: 60rpx; padding-right: 60rpx; height: 80rpx;\"\n        :scrollable=\"false\"\n        lineColor=\"#4285f4\"\n        lineWidth=\"40\"\n      ></u-tabs>\n    </view>\n    \n    <!-- 兑换列表 -->\n    <view class=\"exchange-list\">\n      <block v-if=\"currentTab === 0\">\n        <!-- 活动列表 -->\n        <view class=\"exchange-item\" v-for=\"(item, index) in activityList\" :key=\"index\" @click=\"viewActivityDetail(item)\">\n          <view class=\"item-main\">\n            <u-image :src=\"item.image\" width=\"200rpx\" height=\"160rpx\" radius=\"8\" class=\"item-image\"></u-image>\n            <view class=\"item-content\">\n              <view class=\"item-title\">{{ item.title }}</view>\n              <view class=\"item-desc\">{{ item.desc }}</view>\n            </view>\n          </view>\n          <view class=\"item-footer\">\n            <view class=\"item-points\">{{ item.points }} {{ lang === 'zh' ? '积分' : 'points' }}</view>\n            <view class=\"exchange-btn\" @click.stop=\"joinActivity(item)\">\n              {{ lang === 'zh' ? '立即报名' : 'Join Now' }}\n            </view>\n          </view>\n        </view>\n      </block>\n      \n      <block v-if=\"currentTab === 1\">\n        <!-- 礼品列表 -->\n        <view class=\"exchange-item\" v-for=\"(item, index) in giftList\" :key=\"index\" @click=\"viewGiftDetail(item)\">\n          <view class=\"item-main\">\n            <u-image :src=\"item.image\" width=\"200rpx\" height=\"160rpx\" radius=\"8\" class=\"item-image\"></u-image>\n            <view class=\"item-content\">\n              <view class=\"item-title\">{{ item.title }}</view>\n              <view class=\"item-desc\">{{ item.desc }}</view>\n            </view>\n          </view>\n          <view class=\"item-footer\">\n            <view class=\"item-points\">{{ item.points }} {{ lang === 'zh' ? '积分' : 'points' }}</view>\n            <view class=\"exchange-btn\" @click.stop=\"exchangeGift(item)\">\n              {{ lang === 'zh' ? '立即兑换' : 'Exchange' }}\n            </view>\n          </view>\n        </view>\n      </block>\n      \n      <!-- 无记录提示 -->\n      <view class=\"empty-tip\" v-if=\"getCurrentList.length === 0\">\n        <u-empty mode=\"data\" :text=\"lang === 'zh' ? '暂无可兑换项目' : 'No items available'\"></u-empty>\n      </view>\n    </view>\n    \n    <!-- 活动详情弹窗 -->\n    <u-popup :show=\"showActivityDetail\" mode=\"center\" @close=\"closeActivityDetail\" round=\"16\" :safeAreaInsetBottom=\"false\">\n      <view class=\"detail-popup\">\n        <view class=\"detail-header\">\n          <text class=\"detail-title\">{{ lang === 'zh' ? '活动详情' : 'Activity Detail' }}</text>\n          <u-icon name=\"close\" size=\"24\" color=\"#999\" @click=\"closeActivityDetail\"></u-icon>\n        </view>\n        \n        <view class=\"detail-content\">\n          <u-image :src=\"currentActivity.image\" width=\"100%\" height=\"300rpx\" radius=\"8\"></u-image>\n          \n          <view class=\"detail-item\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '活动名称' : 'Name' }}</text>\n            <text class=\"detail-value\">{{ currentActivity.title }}</text>\n          </view>\n          \n          <view class=\"detail-item\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '所需积分' : 'Points' }}</text>\n            <text class=\"detail-value\">{{ currentActivity.points }}</text>\n          </view>\n          \n          <view class=\"detail-item\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '活动时间' : 'Time' }}</text>\n            <text class=\"detail-value\">{{ currentActivity.time }}</text>\n          </view>\n          \n          <view class=\"detail-item\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '活动地点' : 'Location' }}</text>\n            <text class=\"detail-value\">{{ currentActivity.location }}</text>\n          </view>\n          \n          <view class=\"detail-item\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '活动详情' : 'Description' }}</text>\n            <text class=\"detail-value\">{{ currentActivity.desc }}</text>\n          </view>\n        </view>\n        \n        <view class=\"detail-footer\">\n          <u-button type=\"primary\" @click=\"joinActivity(currentActivity)\">\n            {{ lang === 'zh' ? '立即报名' : 'Join Now' }}\n          </u-button>\n        </view>\n      </view>\n    </u-popup>\n    \n    <!-- 礼品详情弹窗 -->\n    <u-popup :show=\"showGiftDetail\" mode=\"center\" @close=\"closeGiftDetail\" round=\"16\" :safeAreaInsetBottom=\"fakse\">\n      <view class=\"detail-popup\">\n        <view class=\"detail-header\">\n          <text class=\"detail-title\">{{ lang === 'zh' ? '礼品详情' : 'Gift Detail' }}</text>\n          <u-icon name=\"close\" size=\"24\" color=\"#999\" @click=\"closeGiftDetail\"></u-icon>\n        </view>\n        \n        <view class=\"detail-content\">\n          <u-image :src=\"currentGift.image\" width=\"100%\" height=\"300rpx\" radius=\"8\"></u-image>\n          \n          <view class=\"detail-item\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '礼品名称' : 'Name' }}</text>\n            <text class=\"detail-value\">{{ currentGift.title }}</text>\n          </view>\n          \n          <view class=\"detail-item\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '所需积分' : 'Points' }}</text>\n            <text class=\"detail-value\">{{ currentGift.points }}</text>\n          </view>\n          \n          <view class=\"detail-item\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '礼品详情' : 'Description' }}</text>\n            <text class=\"detail-value\">{{ currentGift.desc }}</text>\n          </view>\n        </view>\n        \n        <view class=\"detail-footer\">\n          <u-button type=\"primary\" @click=\"exchangeGift(currentGift)\">\n            {{ lang === 'zh' ? '立即兑换' : 'Exchange' }}\n          </u-button>\n        </view>\n      </view>\n    </u-popup>\n    \n    <!-- 活动报名弹窗 -->\n    <u-popup :show=\"showActivityJoin\" mode=\"center\" @close=\"closeActivityJoin\" round=\"16\" :safeAreaInsetBottom=\"false\">\n      <view class=\"form-popup\">\n        <view class=\"form-header\">\n          <text class=\"form-title\">{{ lang === 'zh' ? '活动报名' : 'Activity Registration' }}</text>\n          <u-icon name=\"close\" size=\"24\" color=\"#999\" @click=\"closeActivityJoin\"></u-icon>\n        </view>\n        \n        <view class=\"form-content\">\n          <!-- 选择报名资料 -->\n          <view class=\"form-section\">\n            <view class=\"form-section-title\">{{ lang === 'zh' ? '选择报名信息：' : 'Select Profile:' }}</view>\n            <view class=\"profile-selector-new\" @click=\"showProfileSelect = true\">\n              <text>{{ selectedProfile ? selectedProfile.name : (lang === 'zh' ? '请选择报名资料' : 'Please select a profile') }}</text>\n              <u-icon name=\"arrow-right\" size=\"36rpx\" color=\"#909399\"></u-icon>\n            </view>\n          </view>\n          \n          <!-- 显示选中的报名信息 -->\n          <view class=\"selected-profile-info-new\" v-if=\"selectedProfile\">\n            <view class=\"profile-item-row\">\n              <text class=\"profile-item-label\">{{ lang === 'zh' ? '姓名：' : 'Name:' }}</text>\n              <text class=\"profile-item-value\">{{ selectedProfile.name }}</text>\n            </view>\n            <view class=\"profile-item-row\">\n              <text class=\"profile-item-label\">{{ lang === 'zh' ? '手机：' : 'Phone:' }}</text>\n              <text class=\"profile-item-value\">{{ selectedProfile.phone }}</text>\n            </view>\n            <view class=\"profile-item-row\" v-if=\"selectedProfile.email\">\n              <text class=\"profile-item-label\">{{ lang === 'zh' ? '邮箱：' : 'Email:' }}</text>\n              <text class=\"profile-item-value\">{{ selectedProfile.email }}</text>\n            </view>\n            <view class=\"profile-item-row\" v-if=\"selectedProfile.idCard\">\n              <text class=\"profile-item-label\">{{ lang === 'zh' ? '身份证：' : 'ID Card:' }}</text>\n              <text class=\"profile-item-value\">{{ selectedProfile.idCard }}</text>\n            </view>\n            <view class=\"profile-item-row\" v-if=\"selectedProfile.address\">\n              <text class=\"profile-item-label\">{{ lang === 'zh' ? '地址：' : 'Address:' }}</text>\n              <text class=\"profile-item-value\">{{ selectedProfile.address }}</text>\n            </view>\n            <view class=\"profile-item-row\" v-if=\"selectedProfile.school\">\n              <text class=\"profile-item-label\">{{ lang === 'zh' ? '学校：' : 'School:' }}</text>\n              <text class=\"profile-item-value\">{{ selectedProfile.school }}</text>\n            </view>\n          </view>\n          \n          <view class=\"form-tips\">\n            <u-icon name=\"info-circle\" size=\"32rpx\" color=\"#ff9900\" style=\"margin-right: 10rpx;\"></u-icon>\n            <text>{{ lang === 'zh' ? '报名成功后将消耗' : 'Registration will consume' }} {{ currentActivity.points }} {{ lang === 'zh' ? '积分' : 'points' }}</text>\n          </view>\n          \n          <view class=\"form-link\" @click=\"goToManageProfiles\">\n            {{ lang === 'zh' ? '管理报名资料 >' : 'Manage profiles >' }}\n          </view>\n        </view>\n        \n        <view class=\"form-footer\">\n          <u-button type=\"primary\" @click=\"submitActivityJoin\" :disabled=\"!selectedProfile\">\n            {{ lang === 'zh' ? '确认报名' : 'Confirm' }}\n          </u-button>\n        </view>\n      </view>\n    </u-popup>\n    \n    <!-- 报名信息选择弹窗 -->\n    <u-popup :show=\"showProfileSelect\" mode=\"bottom\" @close=\"showProfileSelect = false\" :safeAreaInsetBottom=\"true\">\n      <view class=\"popup-content\">\n        <view class=\"popup-header\">\n          <text>{{ lang === 'zh' ? '选择报名资料' : 'Select Profile' }}</text>\n        </view>\n        <scroll-view scroll-y class=\"profile-list\">\n          <view \n            v-for=\"(profile, index) in profileList\" \n            :key=\"index\" \n            class=\"profile-item\"\n            :class=\"{ 'selected': selectedProfileIndex === index, 'default': profile.isDefault }\"\n            @click=\"selectProfile(profile)\"\n          >\n            <view class=\"profile-info\">\n              <text class=\"profile-name\">{{ profile.name }}</text>\n              <text class=\"profile-phone\">{{ profile.phone }}</text>\n            </view>\n            <u-icon v-if=\"selectedProfileIndex === index\" name=\"checkmark\" color=\"#2979ff\" size=\"40rpx\"></u-icon>\n          </view>\n        </scroll-view>\n        <view class=\"popup-buttons\">\n          <view class=\"cancel-button\" @click=\"showProfileSelect = false\">\n            {{ lang === 'zh' ? '取消' : 'Cancel' }}\n          </view>\n          <view class=\"confirm-button\" @click=\"confirmProfileSelection\">\n            {{ lang === 'zh' ? '确认' : 'Confirm' }}\n          </view>\n        </view>\n        <view class=\"manage-profiles-link\" @click=\"goToManageProfiles\">\n          {{ lang === 'zh' ? '管理报名资料 >' : 'Manage Profiles >' }}\n        </view>\n      </view>\n    </u-popup>\n    \n    <!-- 礼品兑换弹窗 -->\n    <u-popup :show=\"showGiftExchange\" mode=\"center\" @close=\"closeGiftExchange\" round=\"16\" :safeAreaInsetBottom=\"fakse\">\n      <view class=\"form-popup\">\n        <view class=\"form-header\">\n          <text class=\"form-title\">{{ lang === 'zh' ? '礼品兑换' : 'Gift Exchange' }}</text>\n          <u-icon name=\"close\" size=\"24\" color=\"#999\" @click=\"closeGiftExchange\"></u-icon>\n        </view>\n        \n        <view class=\"form-content\">\n          <view class=\"form-item\">\n            <text class=\"form-label\">{{ lang === 'zh' ? '收件人' : 'Recipient' }}</text>\n            <u-input v-model=\"giftForm.name\" :placeholder=\"lang === 'zh' ? '请输入收件人姓名' : 'Enter recipient name'\" border=\"bottom\"></u-input>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">{{ lang === 'zh' ? '手机号' : 'Phone' }}</text>\n            <u-input v-model=\"giftForm.phone\" :placeholder=\"lang === 'zh' ? '请输入联系电话' : 'Enter contact phone'\" border=\"bottom\" type=\"number\"></u-input>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">{{ lang === 'zh' ? '收货地址' : 'Address' }}</text>\n            <u-input v-model=\"giftForm.address\" :placeholder=\"lang === 'zh' ? '请输入详细地址' : 'Enter detailed address'\" border=\"bottom\"></u-input>\n          </view>\n          \n          <view class=\"form-item\">\n            <text class=\"form-label\">{{ lang === 'zh' ? '备注' : 'Remarks' }}</text>\n            <u-input v-model=\"giftForm.remarks\" :placeholder=\"lang === 'zh' ? '请输入备注信息（选填）' : 'Enter remarks (optional)'\" border=\"bottom\"></u-input>\n          </view>\n          \n          <view class=\"form-tips\">\n            {{ lang === 'zh' ? '兑换成功后将消耗' : 'This will cost' }} {{ currentGift.points }} {{ lang === 'zh' ? '积分' : 'points' }}\n          </view>\n        </view>\n        \n        <view class=\"form-footer\">\n          <u-button type=\"primary\" @click=\"submitGiftExchange\">\n            {{ lang === 'zh' ? '确认兑换' : 'Confirm' }}\n          </u-button>\n        </view>\n      </view>\n    </u-popup>\n    \n    <!-- 语言切换 -->\n    <LangSwitch />\n  </view>\n</template>\n\n<script>\nimport LangSwitch from '@/components/LangSwitch.vue';\n\nexport default {\n  components: {\n    LangSwitch\n  },\n  data() {\n    return {\n      lang: 'zh',\n      points: 1280,\n      currentTab: 0,\n      tabList: [],\n      activityList: [],\n      giftList: [],\n      showActivityDetail: false,\n      showGiftDetail: false,\n      showActivityJoin: false,\n      showGiftExchange: false,\n      showProfileSelect: false, // 控制选择报名资料弹窗\n      profileList: [\n        {\n          name: '张三',\n          phone: '13812345678',\n          idCard: '******************',\n          email: '<EMAIL>',\n          school: '深圳大学',\n          address: '广东省深圳市南山区科技园南区',\n          isDefault: true\n        },\n        {\n          name: '李四',\n          phone: '13987654321',\n          idCard: '******************',\n          email: '<EMAIL>',\n          school: '华南理工大学',\n          address: '广东省广州市天河区五山路',\n          isDefault: false\n        },\n        {\n          name: '王五',\n          phone: '13500001111',\n          idCard: '******************',\n          email: '<EMAIL>',\n          school: '北京大学',\n          address: '北京市海淀区颐和园路5号',\n          isDefault: false\n        },\n        {\n          name: '赵六',\n          phone: '13600002222',\n          idCard: '******************',\n          email: '<EMAIL>',\n          school: '清华大学',\n          address: '北京市海淀区清华园1号',\n          isDefault: false\n        },\n        {\n          name: '钱七',\n          phone: '13700003333',\n          idCard: '******************',\n          email: '<EMAIL>',\n          school: '复旦大学',\n          address: '上海市杨浦区邯郸路220号',\n          isDefault: false\n        },\n        {\n          name: '孙八',\n          phone: '13800004444',\n          idCard: '******************',\n          email: '<EMAIL>',\n          school: '浙江大学',\n          address: '浙江省杭州市西湖区余杭塘路866号',\n          isDefault: false\n        },\n        {\n          name: '周九',\n          phone: '13900005555',\n          idCard: '******************',\n          email: '<EMAIL>',\n          school: '南京大学',\n          address: '江苏省南京市栖霞区仙林大道163号',\n          isDefault: false\n        }\n      ], // 报名资料列表\n      selectedProfile: null, // 当前选中的报名资料\n      currentActivity: {},\n      currentGift: {},\n      activityForm: {\n        name: '',\n        phone: '',\n        remarks: ''\n      },\n      giftForm: {\n        name: '',\n        phone: '',\n        address: '',\n        remarks: ''\n      }\n    };\n  },\n  computed: {\n    getCurrentList() {\n      return this.currentTab === 0 ? this.activityList : this.giftList;\n    }\n  },\n  onReady() {\n    // 在页面渲染完成后初始化标签页\n    this.initTabs();\n  },\n  mounted() {\n    // 获取语言设置\n    const lang = uni.getStorageSync('lang') || 'zh';\n    this.lang = lang;\n    \n    // 监听语言变化\n    uni.$on('lang-change', this.onLangChange);\n    \n    // 设置导航栏标题\n    this.setNavTitle();\n    \n    // 获取用户积分\n    this.getUserPoints();\n    \n    // 获取兑换列表\n    this.getExchangeList();\n    \n    // 初始化默认资料\n    this.initDefaultProfile();\n  },\n  beforeDestroy() {\n    uni.$off('lang-change', this.onLangChange);\n  },\n  methods: {\n    onLangChange(newLang) {\n      this.lang = newLang;\n      this.setNavTitle();\n      this.initTabs();\n      this.getExchangeList(); // 重新获取列表以更新语言\n    },\n    setNavTitle() {\n      const title = this.lang === 'zh' ? '积分兑换' : 'Points Exchange';\n      uni.setNavigationBarTitle({ title });\n    },\n    initTabs() {\n      this.tabList = [\n        { name: this.lang === 'zh' ? '活动' : 'Activities' },\n        { name: this.lang === 'zh' ? '礼品' : 'Gifts' }\n      ];\n    },\n    onTabChange(res) {\n      console.log(res, \"切换标签\");\n      this.currentTab = res.index;\n    },\n    getUserPoints() {\n      // 优先从本地存储的积分数据获取\n      const userPoints = uni.getStorageSync('userPoints');\n      if (userPoints !== undefined && userPoints !== null) {\n        this.points = userPoints;\n        return;\n      }\n      \n      // 如果没有单独存储的积分，从用户信息中获取\n      const userInfo = uni.getStorageSync('userInfo');\n      if (userInfo && userInfo.points !== undefined) {\n        this.points = userInfo.points;\n      }\n    },\n    getExchangeList() {\n      // 这里应该是从服务器获取兑换列表\n      // 这里使用模拟数据\n      \n      // 活动列表\n      this.activityList = [\n        {\n          id: 'act001',\n          title: this.lang === 'zh' ? '编程技能工作坊' : 'Coding Skills Workshop',\n          desc: this.lang === 'zh' ? '学习最新的编程技术和实践' : 'Learn the latest programming techniques and practices',\n          points: 100,\n          image: 'https://picsum.photos/400/300?random=1',\n          time: '2023-09-15 14:00-17:00',\n          location: this.lang === 'zh' ? '深圳市南山区科技园' : 'Nanshan District, Shenzhen'\n        },\n        {\n          id: 'act002',\n          title: this.lang === 'zh' ? '人工智能讲座' : 'AI Lecture Series',\n          desc: this.lang === 'zh' ? '了解AI最新发展和应用场景' : 'Learn about the latest developments in AI and its applications',\n          points: 150,\n          image: 'https://picsum.photos/400/300?random=2',\n          time: '2023-09-20 19:00-21:00',\n          location: this.lang === 'zh' ? '线上直播' : 'Online Live'\n        },\n        {\n          id: 'act003',\n          title: this.lang === 'zh' ? '创业经验分享会' : 'Entrepreneurship Sharing',\n          desc: this.lang === 'zh' ? '成功创业者分享经验和教训' : 'Successful entrepreneurs share their experiences and lessons',\n          points: 200,\n          image: 'https://picsum.photos/400/300?random=3',\n          time: '2023-09-25 15:00-17:30',\n          location: this.lang === 'zh' ? '深圳市福田区会展中心' : 'Futian Convention Center, Shenzhen'\n        }\n      ];\n      \n      // 礼品列表\n      this.giftList = [\n        {\n          id: 'gift001',\n          title: this.lang === 'zh' ? '定制T恤' : 'Custom T-shirt',\n          desc: this.lang === 'zh' ? '舒适透气的纯棉T恤，印有夏令营Logo' : 'Comfortable cotton T-shirt with Summer Camp Logo',\n          points: 100,\n          image: 'https://picsum.photos/400/300?random=4',\n          stock: 50\n        },\n        {\n          id: 'gift002',\n          title: this.lang === 'zh' ? '蓝牙耳机' : 'Bluetooth Headphones',\n          desc: this.lang === 'zh' ? '高音质无线蓝牙耳机，续航时间长' : 'High-quality wireless headphones with long battery life',\n          points: 300,\n          image: 'https://picsum.photos/400/300?random=5',\n          stock: 20\n        },\n        {\n          id: 'gift003',\n          title: this.lang === 'zh' ? '电影票券' : 'Movie Tickets',\n          desc: this.lang === 'zh' ? '全国通用电影票兑换券，可在线选座' : 'Movie ticket voucher valid nationwide, online seat selection available',\n          points: 80,\n          image: 'https://picsum.photos/400/300?random=6',\n          stock: 100\n        },\n        {\n          id: 'gift004',\n          title: this.lang === 'zh' ? '移动电源' : 'Power Bank',\n          desc: this.lang === 'zh' ? '10000mAh大容量，支持快充' : '10000mAh capacity, supports fast charging',\n          points: 200,\n          image: 'https://picsum.photos/400/300?random=7',\n          stock: 30\n        }\n      ];\n    },\n    viewActivityDetail(item) {\n      this.currentActivity = item;\n      this.showActivityDetail = true;\n    },\n    closeActivityDetail() {\n      this.showActivityDetail = false;\n    },\n    viewGiftDetail(item) {\n      this.currentGift = item;\n      this.showGiftDetail = true;\n    },\n    closeGiftDetail() {\n      this.showGiftDetail = false;\n    },\n    joinActivity(item) {\n      // 检查积分是否足够\n      if (this.points < item.points) {\n        return uni.showToast({\n          title: this.lang === 'zh' ? '积分不足' : 'Insufficient points',\n          icon: 'none'\n        });\n      }\n      \n      this.currentActivity = item;\n      this.showActivityDetail = false;\n      this.showActivityJoin = true;\n    },\n    closeActivityJoin() {\n      this.showActivityJoin = false;\n      this.activityForm = {\n        name: '',\n        phone: '',\n        remarks: ''\n      };\n      this.selectedProfile = null; // 关闭弹窗时清空选中资料\n    },\n    exchangeGift(item) {\n      // 检查积分是否足够\n      if (this.points < item.points) {\n        return uni.showToast({\n          title: this.lang === 'zh' ? '积分不足' : 'Insufficient points',\n          icon: 'none'\n        });\n      }\n      \n      this.currentGift = item;\n      this.showGiftDetail = false;\n      this.showGiftExchange = true;\n    },\n    closeGiftExchange() {\n      this.showGiftExchange = false;\n      this.giftForm = {\n        name: '',\n        phone: '',\n        address: '',\n        remarks: ''\n      };\n    },\n    submitActivityJoin() {\n      // 表单验证\n      if (!this.selectedProfile) {\n        return uni.showToast({\n          title: this.lang === 'zh' ? '请选择报名资料' : 'Please select a profile',\n          icon: 'none'\n        });\n      }\n      \n      // 显示加载中\n      uni.showLoading({\n        title: this.lang === 'zh' ? '提交中...' : 'Submitting...'\n      });\n      \n      // 这里应该是提交到服务器\n      // 这里使用模拟提交\n      setTimeout(() => {\n        // 扣除积分\n        this.points -= this.currentActivity.points;\n        \n        // 更新本地存储\n        const userInfo = uni.getStorageSync('userInfo') || {};\n        userInfo.points = this.points;\n        uni.setStorageSync('userInfo', userInfo);\n        \n        uni.hideLoading();\n        uni.showToast({\n          title: this.lang === 'zh' ? '报名成功' : 'Registration successful',\n          icon: 'success'\n        });\n        \n        // 关闭弹窗\n        this.closeActivityJoin();\n      }, 1000);\n    },\n    submitGiftExchange() {\n      // 表单验证\n      if (!this.giftForm.name) {\n        return uni.showToast({\n          title: this.lang === 'zh' ? '请输入收件人姓名' : 'Please enter recipient name',\n          icon: 'none'\n        });\n      }\n      if (!this.giftForm.phone) {\n        return uni.showToast({\n          title: this.lang === 'zh' ? '请输入联系电话' : 'Please enter contact phone',\n          icon: 'none'\n        });\n      }\n      if (!this.giftForm.address) {\n        return uni.showToast({\n          title: this.lang === 'zh' ? '请输入收货地址' : 'Please enter delivery address',\n          icon: 'none'\n        });\n      }\n      \n      // 显示加载中\n      uni.showLoading({\n        title: this.lang === 'zh' ? '提交中...' : 'Submitting...'\n      });\n      \n      // 这里应该是提交到服务器\n      // 这里使用模拟提交\n      setTimeout(() => {\n        // 扣除积分\n        this.points -= this.currentGift.points;\n        \n        // 更新本地存储\n        const userInfo = uni.getStorageSync('userInfo') || {};\n        userInfo.points = this.points;\n        uni.setStorageSync('userInfo', userInfo);\n        \n        uni.hideLoading();\n        uni.showToast({\n          title: this.lang === 'zh' ? '兑换成功' : 'Exchange successful',\n          icon: 'success'\n        });\n        \n        // 关闭弹窗\n        this.closeGiftExchange();\n      }, 1000);\n    },\n    // 新增：获取所有报名资料\n    // getProfileList() {\n    //   const profiles = uni.getStorageSync('signupProfiles') || [];\n      \n    //   // 如果没有数据，先添加一些测试数据\n    //   if (profiles.length === 0) {\n    //     const testProfiles = [\n    //       {\n    //         name: '张三',\n    //         phone: '13812345678',\n    //         idCard: '******************',\n    //         email: '<EMAIL>',\n    //         school: '深圳大学',\n    //         address: '广东省深圳市南山区科技园南区',\n    //         isDefault: true\n    //       },\n    //       {\n    //         name: '李四',\n    //         phone: '13987654321',\n    //         idCard: '******************',\n    //         email: '<EMAIL>',\n    //         school: '华南理工大学',\n    //         address: '广东省广州市天河区五山路',\n    //         isDefault: false\n    //       },\n    //       {\n    //         name: '王五',\n    //         phone: '13500001111',\n    //         idCard: '******************',\n    //         email: '<EMAIL>',\n    //         school: '北京大学',\n    //         address: '北京市海淀区颐和园路5号',\n    //         isDefault: false\n    //       },\n    //       {\n    //         name: '赵六',\n    //         phone: '13600002222',\n    //         idCard: '******************',\n    //         email: '<EMAIL>',\n    //         school: '清华大学',\n    //         address: '北京市海淀区清华园1号',\n    //         isDefault: false\n    //       },\n    //       {\n    //         name: '钱七',\n    //         phone: '13700003333',\n    //         idCard: '******************',\n    //         email: '<EMAIL>',\n    //         school: '复旦大学',\n    //         address: '上海市杨浦区邯郸路220号',\n    //         isDefault: false\n    //       },\n    //       {\n    //         name: '孙八',\n    //         phone: '13800004444',\n    //         idCard: '******************',\n    //         email: '<EMAIL>',\n    //         school: '浙江大学',\n    //         address: '浙江省杭州市西湖区余杭塘路866号',\n    //         isDefault: false\n    //       },\n    //       {\n    //         name: '周九',\n    //         phone: '13900005555',\n    //         idCard: '******************',\n    //         email: '<EMAIL>',\n    //         school: '南京大学',\n    //         address: '江苏省南京市栖霞区仙林大道163号',\n    //         isDefault: false\n    //       }\n    //     ];\n        \n    //     uni.setStorageSync('signupProfiles', testProfiles);\n    //     this.profileList = testProfiles;\n    //   } else {\n    //     this.profileList = profiles;\n    //   }\n      \n    //   // 如果有默认资料，自动选中\n    //   const defaultProfile = this.profileList.find(profile => profile.isDefault);\n    //   if (defaultProfile) {\n    //     this.selectedProfile = defaultProfile;\n    //     this.activityForm.name = defaultProfile.name;\n    //     this.activityForm.phone = defaultProfile.phone;\n    //   }\n    // },\n    // 新增：选择报名资料\n    selectProfile(profile) {\n      // 查找对应的索引\n      this.selectedProfileIndex = this.profileList.findIndex(item => item.phone === profile.phone);\n      this.selectedProfile = profile;\n    },\n    // 新增：跳转到管理报名资料页面\n    goToManageProfiles() {\n      // 关闭当前弹窗\n      this.showProfileSelect = false;\n      \n      uni.navigateTo({\n        url: '/pages/signup/manage'\n      });\n    },\n    // 新增：初始化默认资料\n    initDefaultProfile() {\n      // 找到默认资料\n      const defaultProfile = this.profileList.find(profile => profile.isDefault);\n      if (defaultProfile) {\n        this.selectedProfile = defaultProfile;\n      }\n    },\n    // 新增：确认选择报名资料\n    confirmProfileSelection() {\n      if (this.selectedProfileIndex === -1) {\n        uni.showToast({\n          title: this.lang === 'zh' ? '请选择报名资料' : 'Please select a profile',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 确认选择并关闭弹窗\n      this.showProfileSelect = false;\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.popup-content {\n  padding: 30rpx;\n  .popup-header {\n    text-align: center;\n    font-size: 32rpx;\n    font-weight: bold;\n    color: #303133;\n    margin-bottom: 30rpx;\n  }\n  .profile-list {\n    max-height: 600rpx;\n  }\n  .profile-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 20rpx;\n    border-bottom: 1rpx solid #f2f2f2;\n    \n    &.selected {\n      background-color: #f5f7fa;\n    }\n    \n    &.default {\n      border: 2rpx solid #2979ff;\n      border-radius: 8rpx;\n      margin-bottom: 10rpx;\n    }\n  }\n  .profile-info {\n    display: flex;\n    flex-direction: column;\n  }\n  \n  .profile-name {\n    font-size: 28rpx;\n    color: #303133;\n    margin-bottom: 8rpx;\n  }\n  \n  .profile-phone {\n    font-size: 24rpx;\n    color: #909399;\n  }\n  \n  .popup-buttons {\n    display: flex;\n    justify-content: space-between;\n    margin-top: 30rpx;\n  }\n  \n  .cancel-button {\n    flex: 1;\n    height: 80rpx;\n    line-height: 80rpx;\n    text-align: center;\n    background-color: #f2f2f2;\n    color: #606266;\n    border-radius: 8rpx;\n    margin-right: 20rpx;\n    font-size: 28rpx;\n  }.confirm-button {\n    flex: 1;\n    height: 80rpx;\n    line-height: 80rpx;\n    text-align: center;\n    background-color: #2979ff;\n    color: #ffffff;\n    border-radius: 8rpx;\n    margin-left: 20rpx;\n    font-size: 28rpx;\n  }\n}\n.exchange-page {\n  min-height: 100vh;\n  box-sizing: border-box;\n  padding-bottom: 100rpx;\n  background-color: #f5f7fa;\n}\n.points-info {\n  background-color: #4285f4;\n  color: #fff;\n  padding: 40rpx 30rpx;\n  text-align: center;\n}\n.points-title {\n  font-size: 30rpx;\n  margin-bottom: 16rpx;\n}\n.points-value {\n  font-size: 72rpx;\n  font-weight: bold;\n}\n.exchange-tabs {\n  background-color: #fff;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n}\n.exchange-list {\n  padding: 0;\n  background-color: #f5f7fa;\n}\n.exchange-item {\n  display: flex;\n  flex-direction: column;\n  background-color: #fff;\n  padding: 30rpx;\n  margin-top: 20rpx;\n  border-radius: 0;\n}\n.item-main {\n  display: flex;\n  margin-bottom: 16rpx;\n  position: relative;\n}\n.item-image {\n  margin-right: 0;\n  border-radius: 8rpx;\n  overflow: hidden;\n  width: 200rpx;\n  height: 160rpx;\n  flex-shrink: 0;\n}\n.item-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding-left: 30rpx;\n  position: relative;\n}\n.item-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10rpx;\n}\n.item-desc {\n  font-size: 26rpx;\n  color: #666;\n}\n.item-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 30rpx;\n  border-top: 1px solid #f5f5f5;\n  height: 50rpx;\n}\n.item-points {\n  font-size: 32rpx;\n  color: #ff9900;\n  font-weight: bold;\n  line-height: 1;\n}\n.exchange-btn {\n  background-color: #4285f4;\n  color: #fff;\n  padding: 15rpx 24rpx;\n  border-radius: 6rpx;\n  font-size: 26rpx;\n  text-align: center;\n  width: 160rpx;\n  line-height: 1.4;\n}\n.empty-tip {\n  padding: 100rpx 0;\n}\n.detail-popup {\n  width: 600rpx;\n  padding: 30rpx;\n  box-sizing: border-box;\n}\n.detail-header, .form-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n.detail-title, .form-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n.detail-content, .form-content {\n  margin-bottom: 30rpx;\n}\n.detail-item {\n  display: flex;\n  margin-top: 20rpx;\n}\n.detail-item:first-child {\n  margin-top: 0;\n}\n.detail-label, .form-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #666;\n}\n.detail-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n.detail-footer, .form-footer {\n  margin-top: 40rpx;\n}\n.form-popup {\n  width: 650rpx;\n  padding: 30rpx;\n  box-sizing: border-box;\n}\n.form-item {\n  margin-bottom: 30rpx;\n}\n.form-tips {\n  font-size: 24rpx;\n  color: #ff9900;\n  text-align: center;\n  margin-top: 20rpx;\n}\n.profile-selector {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20rpx 0;\n  border-bottom: 1px solid #eee;\n}\n.profile-selector .placeholder {\n  color: #999;\n  font-size: 28rpx;\n}\n.profile-selector-new {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20rpx 30rpx;\n  background-color: #f5f5f5;\n  border-radius: 8rpx;\n  margin-bottom: 20rpx;\n}\n.profile-selector-left {\n  display: none;\n}\n\n.profile-info-header {\n  display: none;\n}\n\n.profile-info-name {\n  display: none;\n}\n\n.profile-info-tag {\n  display: none;\n}\n\n.profile-card-item {\n  display: none;\n}\n\n.profile-card-text {\n  display: none;\n}\n\n.profile-popup {\n  width: 100%;\n  background-color: #fff;\n  display: flex;\n  flex-direction: column;\n  border-top-left-radius: 16rpx;\n  border-top-right-radius: 16rpx;\n  box-sizing: border-box;\n  padding: 30rpx;\n  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));\n}\n\n.profile-popup-header {\n  text-align: center;\n  padding-bottom: 20rpx;\n}\n\n.popup-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.profile-list {\n  max-height: 60vh;\n  overflow-y: auto;\n}\n\n.profile-select-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 30rpx 0;\n  border-bottom: 1px solid #f2f2f2;\n}\n\n.profile-select-item.profile-selected {\n  background-color: #f0f7ff;\n  border: 2rpx solid #4285f4;\n  border-radius: 12rpx;\n  padding: 30rpx 20rpx;\n  margin: 10rpx 0;\n}\n\n.profile-select-info {\n  flex: 1;\n}\n\n.profile-select-name {\n  font-size: 32rpx;\n  color: #333;\n  font-weight: 500;\n  margin-bottom: 8rpx;\n}\n\n.profile-select-phone {\n  font-size: 28rpx;\n  color: #999;\n}\n\n.profile-popup-buttons {\n  display: flex;\n  justify-content: space-between;\n  margin: 30rpx 0;\n}\n\n.profile-cancel-button, .profile-confirm-button {\n  width: 320rpx;\n  height: 88rpx;\n  line-height: 88rpx;\n  text-align: center;\n  border-radius: 8rpx;\n  font-size: 32rpx;\n}\n\n.profile-cancel-button {\n  background-color: #f5f5f5;\n  color: #333;\n}\n\n.profile-confirm-button {\n  background-color: #4285f4;\n  color: #fff;\n}\n\n.manage-profiles-link {\n  text-align: center;\n  font-size: 28rpx;\n  color: #2979ff;\n  margin-top: 20rpx;\n  padding-bottom: 20rpx;\n}\n.selected-profile-info-new {\n  background-color: #fff;\n  border-radius: 8rpx;\n  padding: 20rpx 30rpx;\n  margin-top: 10rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.profile-info-header {\n  display: none;\n}\n\n.profile-info-name {\n  display: none;\n}\n\n.profile-info-tag {\n  display: none;\n}\n\n.profile-card-item {\n  display: none;\n}\n\n.profile-card-text {\n  display: none;\n}\n\n.form-link {\n  font-size: 26rpx;\n  color: #4285f4;\n  text-align: center;\n  margin-top: 20rpx;\n}\n.form-section {\n  margin-bottom: 20rpx;\n}\n\n.form-section-title {\n  font-size: 30rpx;\n  color: #333;\n  margin-bottom: 10rpx;\n}\n\n.profile-item-new.selected {\n  background-color: #ecf5ff;\n}\n\n.profile-item-new.default {\n  border-left: 8rpx solid #4285f4;\n}\n\n.selected-profile-info-new {\n  border-left: 8rpx solid #4285f4;\n}\n\n.form-tips {\n  display: flex;\n  align-items: center;\n  padding: 20rpx;\n  background-color: #fffbe6;\n  border-radius: 8rpx;\n  margin-top: 20rpx;\n}\n\n.form-tips text {\n  font-size: 28rpx;\n  color: #ff9900;\n  flex: 1;\n}\n\n.profile-item-row {\n  display: flex;\n  padding: 10rpx 0;\n}\n\n.profile-item-label {\n  width: 120rpx;\n  font-size: 28rpx;\n  color: #666;\n}\n\n.profile-item-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n</style> ", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exchange.vue?vue&type=style&index=0&id=45560ae6&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exchange.vue?vue&type=style&index=0&id=45560ae6&scoped=true&lang=scss&\""], "sourceRoot": ""}