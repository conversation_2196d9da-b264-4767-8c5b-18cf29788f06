{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/points/record.vue?08f6", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/points/record.vue?1702", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/points/record.vue?6d7e", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/points/record.vue?d560", "uni-app:///pages/points/record.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/points/record.vue?f4ee"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uTabs", "uIcon", "uEmpty", "uPopup", "uButton", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "g0", "_self", "_c", "getCurrentRecords", "length", "m0", "getRecordTypeName", "currentDetail", "category", "m1", "getDeliveryStatus", "deliveryStatus", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "LangSwitch", "lang", "currentTab", "tabList", "allRecords", "activityRecords", "giftRecords", "showDetail", "computed", "onReady", "mounted", "uni", "<PERSON><PERSON><PERSON><PERSON>", "methods", "onLangChange", "setNavTitle", "title", "initTabs", "name", "onTabChange", "getPointsRecords", "id", "type", "time", "points", "location", "duration", "exchangeTime", "trackingNo", "address", "viewDetail", "closeDetail", "checkDelivery", "icon", "setTimeout", "content", "showCancel"], "mappings": "8IAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,0BACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,MAAO,WACL,OAAO,kHAITC,MAAO,WACL,OAAO,kHAITC,OAAQ,WACN,OAAO,oHAITC,OAAQ,WACN,OAAO,oHAITC,QAAS,WACP,OAAO,uHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,kBAAkBC,QAC3BC,EAAKT,EAAIU,kBAAkBV,EAAIW,cAAcC,UAC7CC,EAC6B,SAA/Bb,EAAIW,cAAcC,SACdZ,EAAIc,kBAAkBd,EAAIW,cAAcI,gBACxC,KACNf,EAAIgB,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLhB,GAAIA,EACJK,GAAIA,EACJI,GAAIA,MAKRQ,GAAmB,EACnBC,EAAkB,GACtBvB,EAAOwB,eAAgB,G,iCCrEvB,yHAAi4B,eAAG,G,yPC2Jp4B,CACAnC,YACAoC,cAEAP,gBACA,OACAQ,UACAC,aACAC,WACAC,cACAC,mBACAC,eACAC,cACApB,mBAGAqB,UACAzB,6BACA,wBACA,OACA,uBACA,OACA,4BACA,OACA,wBACA,QACA,YAIA0B,mBAEA,iBAEAC,mBAEA,qCACA,YAGAC,uCAGA,mBAGA,yBAEAC,yBACAD,yCAEAE,SACAC,yBACA,YACA,mBACA,iBAEAC,uBACA,8CACAJ,yBAAAK,WAEAC,oBACA,cACA,CAAAC,kCACA,CAAAA,yCACA,CAAAA,sCAGAC,wBACA9C,qBACA,yBAEA+C,4BAKA,sBACA,CACAC,YACAjC,oBACAkC,WACAN,wDACAO,wBACAC,WACAC,mEACAC,2CAEA,CACAL,YACAjC,oBACAkC,WACAN,oDACAO,wBACAC,WACAC,wCACAC,2CAEA,CACAL,YACAjC,oBACAkC,WACAN,8DACAO,wBACAC,UACAC,mEACAC,2CAKA,kBACA,CACAL,aACAjC,gBACAkC,aACAN,iDACAO,wBACAC,WACAG,gCACApC,2BACAqC,0BACAC,mJAEA,CACAR,aACAjC,gBACAkC,aACAN,uDACAO,wBACAC,WACAG,gCACApC,0BACAqC,0BACAC,mJAEA,CACAR,aACAjC,gBACAkC,aACAN,gDACAO,wBACAC,UACAG,gCACApC,yBACAqC,cACAC,aAKA,mHACA,6CAGAC,uBACA,qBACA,oBAEAC,uBACA,oBAEA7C,8BACA,qBACA,iCACA,WACA,6BAEA,IAEAI,8BACA,UACA,cACA,uCACA,eACA,wCACA,gBACA,yCACA,cACA,6CACA,QACA,WAGA0C,yBAAA,WAGArB,aACAK,+DACAiB,cAGAC,uBACAvB,aACAK,kDACAmB,sCACA,8IACA,gKACAC,kBAEA,QAGA,c,6DCtWA,yHAAwxC,eAAG,G", "file": "pages/points/record.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/points/record.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./record.vue?vue&type=template&id=18f9de58&scoped=true&\"\nvar renderjs\nimport script from \"./record.vue?vue&type=script&lang=js&\"\nexport * from \"./record.vue?vue&type=script&lang=js&\"\nimport style0 from \"./record.vue?vue&type=style&index=0&id=18f9de58&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"18f9de58\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/points/record.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record.vue?vue&type=template&id=18f9de58&scoped=true&\"", "var components\ntry {\n  components = {\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-tabs/u-tabs\" */ \"uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-empty/u-empty\" */ \"uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-button/u-button\" */ \"uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.getCurrentRecords.length\n  var m0 = _vm.getRecordTypeName(_vm.currentDetail.category)\n  var m1 =\n    _vm.currentDetail.category === \"gift\"\n      ? _vm.getDeliveryStatus(_vm.currentDetail.deliveryStatus)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"points-record-page\">\n    <!-- 积分记录类型选择 -->\n    <view class=\"record-tabs\">\n      <u-tabs\n        :list=\"tabList\"\n        :current=\"currentTab\"\n        @click=\"onTabChange\"\n        :activeStyle=\"{\n          color: '#2979ff',\n          fontWeight: 'bold'\n        }\"\n        :inactiveStyle=\"{\n          color: '#666',\n          fontWeight: 'normal'\n        }\"\n        itemStyle=\"padding-left: 30rpx; padding-right: 30rpx; height: 80rpx;\"\n        :scrollable=\"false\"\n      ></u-tabs>\n    </view>\n    \n    <!-- 记录列表 -->\n    <view class=\"record-list\">\n      <block v-if=\"currentTab === 0\">\n        <!-- 全部记录 -->\n        <view class=\"record-item\" v-for=\"(item, index) in allRecords\" :key=\"index\" @click=\"viewDetail(item)\">\n          <view class=\"record-main\">\n            <view class=\"record-title\">{{ item.title }}</view>\n            <view class=\"record-time\">{{ item.time }}</view>\n          </view>\n          <view class=\"record-points\" :class=\"{ 'points-add': item.type === 'add', 'points-minus': item.type === 'minus' }\">\n            {{ item.type === 'add' ? '+' : '-' }}{{ item.points }}\n          </view>\n          <u-icon name=\"arrow-right\" size=\"24\" color=\"#c0c4cc\"></u-icon>\n        </view>\n      </block>\n      \n      <block v-if=\"currentTab === 1\">\n        <!-- 活动记录 -->\n        <view class=\"record-item\" v-for=\"(item, index) in activityRecords\" :key=\"index\" @click=\"viewDetail(item)\">\n          <view class=\"record-main\">\n            <view class=\"record-title\">{{ item.title }}</view>\n            <view class=\"record-time\">{{ item.time }}</view>\n          </view>\n          <view class=\"record-points\" :class=\"{ 'points-add': item.type === 'add', 'points-minus': item.type === 'minus' }\">\n            {{ item.type === 'add' ? '+' : '-' }}{{ item.points }}\n          </view>\n          <u-icon name=\"arrow-right\" size=\"24\" color=\"#c0c4cc\"></u-icon>\n        </view>\n      </block>\n      \n      <block v-if=\"currentTab === 2\">\n        <!-- 礼品记录 -->\n        <view class=\"record-item\" v-for=\"(item, index) in giftRecords\" :key=\"index\" @click=\"viewDetail(item)\">\n          <view class=\"record-main\">\n            <view class=\"record-title\">{{ item.title }}</view>\n            <view class=\"record-time\">{{ item.time }}</view>\n          </view>\n          <view class=\"record-points points-minus\">\n            -{{ item.points }}\n          </view>\n          <u-icon name=\"arrow-right\" size=\"24\" color=\"#c0c4cc\"></u-icon>\n        </view>\n      </block>\n      \n      <!-- 无记录提示 -->\n      <view class=\"empty-tip\" v-if=\"getCurrentRecords.length === 0\">\n        <u-empty mode=\"data\" :text=\"lang === 'zh' ? '暂无积分记录' : 'No records yet'\"></u-empty>\n      </view>\n    </view>\n    \n    <!-- 记录详情弹窗 -->\n    <u-popup :show=\"showDetail\" :safeAreaInsetBottom=\"false\" mode=\"center\" @close=\"closeDetail\" round=\"16\">\n      <view class=\"detail-popup\">\n        <view class=\"detail-header\">\n          <text class=\"detail-title\">{{ lang === 'zh' ? '记录详情' : 'Record Detail' }}</text>\n          <u-icon name=\"close\" size=\"24\" color=\"#999\" @click=\"closeDetail\"></u-icon>\n        </view>\n        \n        <view class=\"detail-content\">\n          <!-- 通用信息 -->\n          <view class=\"detail-item\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '类型' : 'Type' }}</text>\n            <text class=\"detail-value\">{{ getRecordTypeName(currentDetail.category) }}</text>\n          </view>\n          \n          <view class=\"detail-item\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '名称' : 'Name' }}</text>\n            <text class=\"detail-value\">{{ currentDetail.title }}</text>\n          </view>\n          \n          <view class=\"detail-item\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '时间' : 'Time' }}</text>\n            <text class=\"detail-value\">{{ currentDetail.time }}</text>\n          </view>\n          \n          <view class=\"detail-item\">\n            <text class=\"detail-label\">{{ lang === 'zh' ? '积分变动' : 'Points' }}</text>\n            <text class=\"detail-value\" :class=\"{ 'points-add': currentDetail.type === 'add', 'points-minus': currentDetail.type === 'minus' }\">\n              {{ currentDetail.type === 'add' ? '+' : '-' }}{{ currentDetail.points }}\n            </text>\n          </view>\n          \n          <!-- 活动类特有信息 -->\n          <block v-if=\"currentDetail.category === 'activity'\">\n            <view class=\"detail-item\">\n              <text class=\"detail-label\">{{ lang === 'zh' ? '活动地点' : 'Location' }}</text>\n              <text class=\"detail-value\">{{ currentDetail.location }}</text>\n            </view>\n            \n            <view class=\"detail-item\">\n              <text class=\"detail-label\">{{ lang === 'zh' ? '活动时长' : 'Duration' }}</text>\n              <text class=\"detail-value\">{{ currentDetail.duration }}</text>\n            </view>\n          </block>\n          \n          <!-- 礼品类特有信息 -->\n          <block v-if=\"currentDetail.category === 'gift'\">\n            <view class=\"detail-item\">\n              <text class=\"detail-label\">{{ lang === 'zh' ? '兑换时间' : 'Exchange Time' }}</text>\n              <text class=\"detail-value\">{{ currentDetail.exchangeTime }}</text>\n            </view>\n            \n            <view class=\"detail-item\">\n              <text class=\"detail-label\">{{ lang === 'zh' ? '物流状态' : 'Delivery Status' }}</text>\n              <text class=\"detail-value\">{{ getDeliveryStatus(currentDetail.deliveryStatus) }}</text>\n            </view>\n            \n            <view class=\"detail-item\">\n              <text class=\"detail-label\">{{ lang === 'zh' ? '快递单号' : 'Tracking No.' }}</text>\n              <text class=\"detail-value\">{{ currentDetail.trackingNo || '-' }}</text>\n            </view>\n            \n            <view class=\"detail-item\" v-if=\"currentDetail.trackingNo\">\n              <text class=\"detail-label\">{{ lang === 'zh' ? '收货地址' : 'Address' }}</text>\n              <text class=\"detail-value address-value\">{{ currentDetail.address }}</text>\n            </view>\n          </block>\n        </view>\n        \n        <!-- 查看物流按钮 -->\n        <view class=\"detail-footer\" v-if=\"currentDetail.category === 'gift' && currentDetail.trackingNo\">\n          <u-button type=\"primary\" @click=\"checkDelivery\">{{ lang === 'zh' ? '查看物流' : 'Check Delivery' }}</u-button>\n        </view>\n      </view>\n    </u-popup>\n    \n    <!-- 语言切换 -->\n    <LangSwitch />\n  </view>\n</template>\n\n<script>\nimport LangSwitch from '@/components/LangSwitch.vue';\n\nexport default {\n  components: {\n    LangSwitch\n  },\n  data() {\n    return {\n      lang: 'zh',\n      currentTab: 0,\n      tabList: [],\n      allRecords: [],\n      activityRecords: [],\n      giftRecords: [],\n      showDetail: false,\n      currentDetail: {}\n    };\n  },\n  computed: {\n    getCurrentRecords() {\n      switch (this.currentTab) {\n        case 0:\n          return this.allRecords;\n        case 1:\n          return this.activityRecords;\n        case 2:\n          return this.giftRecords;\n        default:\n          return [];\n      }\n    }\n  },\n  onReady() {\n    // 在页面渲染完成后初始化标签页\n    this.initTabs();\n  },\n  mounted() {\n    // 获取语言设置\n    const lang = uni.getStorageSync('lang') || 'zh';\n    this.lang = lang;\n    \n    // 监听语言变化\n    uni.$on('lang-change', this.onLangChange);\n    \n    // 设置导航栏标题\n    this.setNavTitle();\n    \n    // 获取积分记录\n    this.getPointsRecords();\n  },\n  beforeDestroy() {\n    uni.$off('lang-change', this.onLangChange);\n  },\n  methods: {\n    onLangChange(newLang) {\n      this.lang = newLang;\n      this.setNavTitle();\n      this.initTabs();\n    },\n    setNavTitle() {\n      const title = this.lang === 'zh' ? '积分记录' : 'Points Record';\n      uni.setNavigationBarTitle({ title });\n    },\n    initTabs() {\n      this.tabList = [\n        { name: this.lang === 'zh' ? '全部' : 'All' },\n        { name: this.lang === 'zh' ? '活动' : 'Activities' },\n        { name: this.lang === 'zh' ? '礼品' : 'Gifts' }\n      ];\n    },\n    onTabChange(res) {\n      console.log(res,\"大家访\")\n      this.currentTab = res.index;\n    },\n    getPointsRecords() {\n      // 这里应该是从服务器获取积分记录\n      // 这里使用模拟数据\n      \n      // 活动类记录\n      this.activityRecords = [\n        {\n          id: 'act001',\n          category: 'activity',\n          type: 'add',\n          title: this.lang === 'zh' ? '参与夏令营活动' : 'Summer Camp Activity',\n          time: '2023-07-15 14:30',\n          points: 200,\n          location: this.lang === 'zh' ? '深圳市南山区科技园' : 'Nanshan District, Shenzhen',\n          duration: this.lang === 'zh' ? '3小时' : '3 hours'\n        },\n        {\n          id: 'act002',\n          category: 'activity',\n          type: 'add',\n          title: this.lang === 'zh' ? '完成编程挑战赛' : 'Coding Challenge',\n          time: '2023-07-20 10:15',\n          points: 150,\n          location: this.lang === 'zh' ? '线上' : 'Online',\n          duration: this.lang === 'zh' ? '2小时' : '2 hours'\n        },\n        {\n          id: 'act003',\n          category: 'activity',\n          type: 'add',\n          title: this.lang === 'zh' ? '分享学习心得' : 'Learning Experience Sharing',\n          time: '2023-08-05 16:00',\n          points: 50,\n          location: this.lang === 'zh' ? '深圳市福田区会展中心' : 'Futian District, Shenzhen',\n          duration: this.lang === 'zh' ? '1小时' : '1 hour'\n        }\n      ];\n      \n      // 礼品类记录\n      this.giftRecords = [\n        {\n          id: 'gift001',\n          category: 'gift',\n          type: 'minus',\n          title: this.lang === 'zh' ? '兑换定制T恤' : 'Custom T-shirt',\n          time: '2023-08-10 09:45',\n          points: 100,\n          exchangeTime: '2023-08-10 09:45',\n          deliveryStatus: 'delivered',\n          trackingNo: 'SF1234567890',\n          address: this.lang === 'zh' ? '广东省深圳市南山区科技园南区T3栋5楼' : 'Floor 5, Building T3, South Area of Science Park, Nanshan District, Shenzhen, Guangdong Province'\n        },\n        {\n          id: 'gift002',\n          category: 'gift',\n          type: 'minus',\n          title: this.lang === 'zh' ? '兑换蓝牙耳机' : 'Bluetooth Headphones',\n          time: '2023-08-15 14:20',\n          points: 300,\n          exchangeTime: '2023-08-15 14:20',\n          deliveryStatus: 'shipping',\n          trackingNo: 'YT9876543210',\n          address: this.lang === 'zh' ? '广东省深圳市南山区科技园南区T3栋5楼' : 'Floor 5, Building T3, South Area of Science Park, Nanshan District, Shenzhen, Guangdong Province'\n        },\n        {\n          id: 'gift003',\n          category: 'gift',\n          type: 'minus',\n          title: this.lang === 'zh' ? '兑换电影票券' : 'Movie Tickets',\n          time: '2023-08-20 11:30',\n          points: 80,\n          exchangeTime: '2023-08-20 11:30',\n          deliveryStatus: 'virtual',\n          trackingNo: '',\n          address: ''\n        }\n      ];\n      \n      // 合并所有记录\n      this.allRecords = [...this.activityRecords, ...this.giftRecords].sort((a, b) => {\n        return new Date(b.time) - new Date(a.time);\n      });\n    },\n    viewDetail(item) {\n      this.currentDetail = item;\n      this.showDetail = true;\n    },\n    closeDetail() {\n      this.showDetail = false;\n    },\n    getRecordTypeName(category) {\n      if (category === 'activity') {\n        return this.lang === 'zh' ? '活动' : 'Activity';\n      } else if (category === 'gift') {\n        return this.lang === 'zh' ? '礼品' : 'Gift';\n      }\n      return '';\n    },\n    getDeliveryStatus(status) {\n      switch (status) {\n        case 'pending':\n          return this.lang === 'zh' ? '待发货' : 'Pending';\n        case 'shipping':\n          return this.lang === 'zh' ? '运输中' : 'Shipping';\n        case 'delivered':\n          return this.lang === 'zh' ? '已送达' : 'Delivered';\n        case 'virtual':\n          return this.lang === 'zh' ? '虚拟物品' : 'Virtual Item';\n        default:\n          return '';\n      }\n    },\n    checkDelivery() {\n      // 这里应该跳转到物流查询页面或调用物流查询API\n      // 这里简单模拟\n      uni.showToast({\n        title: this.lang === 'zh' ? '查询物流信息...' : 'Checking delivery info...',\n        icon: 'none'\n      });\n      \n      setTimeout(() => {\n        uni.showModal({\n          title: this.lang === 'zh' ? '物流信息' : 'Delivery Information',\n          content: this.lang === 'zh' \n            ? `快递单号: ${this.currentDetail.trackingNo}\\n状态: ${this.getDeliveryStatus(this.currentDetail.deliveryStatus)}\\n最新位置: 深圳市南山区配送中心` \n            : `Tracking No.: ${this.currentDetail.trackingNo}\\nStatus: ${this.getDeliveryStatus(this.currentDetail.deliveryStatus)}\\nLatest Location: Nanshan Distribution Center, Shenzhen`,\n          showCancel: false\n        });\n      }, 1000);\n    }\n  }\n}\n</script>\n\n<style scoped>\n.points-record-page {\n  min-height: 100vh;\n  box-sizing: border-box;\n  padding-bottom: 100rpx;\n  background-color: #f5f7fa;\n}\n.record-tabs {\n  background-color: #fff;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);\n}\n.record-list {\n  padding: 20rpx;\n}\n.record-item {\n  display: flex;\n  align-items: center;\n  background-color: #fff;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  border-radius: 12rpx;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);\n}\n.record-main {\n  flex: 1;\n}\n.record-title {\n  font-size: 28rpx;\n  color: #333;\n  margin-bottom: 8rpx;\n}\n.record-time {\n  font-size: 24rpx;\n  color: #999;\n}\n.record-points {\n  font-size: 32rpx;\n  font-weight: bold;\n  margin: 0 20rpx;\n}\n.points-add {\n  color: #19be6b;\n}\n.points-minus {\n  color: #ff9900;\n}\n.empty-tip {\n  padding: 100rpx 0;\n}\n.detail-popup {\n  width: 600rpx;\n  padding: 30rpx;\n  box-sizing: border-box;\n}\n.detail-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30rpx;\n}\n.detail-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n.detail-content {\n}\n.detail-item {\n  display: flex;\n  margin-top: 20rpx;\n}\n.detail-item:first-child {\n  margin-top: 0;\n}\n.detail-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #666;\n}\n.detail-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n.address-value {\n  word-break: break-all;\n}\n.detail-footer {\n  margin-top: 40rpx;\n}\n</style> ", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record.vue?vue&type=style&index=0&id=18f9de58&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record.vue?vue&type=style&index=0&id=18f9de58&scoped=true&lang=css&\""], "sourceRoot": ""}