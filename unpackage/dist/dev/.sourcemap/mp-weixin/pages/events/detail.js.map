{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/events/detail.vue?0f4e", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/events/detail.vue?b0b4", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/events/detail.vue?f028", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/events/detail.vue?25a8", "uni-app:///pages/events/detail.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/events/detail.vue?bd50"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uIcon", "uPopup", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "_isMounted", "e0", "$event", "showProfileSelector", "e1", "e2", "e3", "showPayment", "e4", "paymentMethod", "e5", "recyclableRender", "staticRenderFns", "_withStripped", "data", "lang", "eventId", "eventData", "id", "title", "title_en", "images", "startTime", "endTime", "location", "location_en", "currentParticipants", "maxParticipants", "fee", "points", "status", "description", "description_en", "showPaymentSuccess", "selectedProfileIndex", "selectedProfile", "statusTextZh", "statusTextEn", "signupProfiles", "name", "phone", "email", "idCard", "address", "isDefault", "onLoad", "uni", "onUnload", "methods", "setNavigationBarTitle", "setDefaultProfile", "selectProfile", "confirmProfileSelection", "handleSignup", "icon", "processPayment", "setTimeout", "handlePaymentSuccessDone", "url", "fetchEventDetail", "switchLanguage", "getApp"], "mappings": "8IAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,0BACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,MAAO,WACL,OAAO,kHAITC,OAAQ,WACN,OAAO,qHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,GACdL,EAAIM,aACPN,EAAIO,GAAK,SAAUC,GACjBR,EAAIS,qBAAsB,GAE5BT,EAAIU,GAAK,SAAUF,GACjBR,EAAIS,qBAAsB,GAE5BT,EAAIW,GAAK,SAAUH,GACjBR,EAAIS,qBAAsB,GAE5BT,EAAIY,GAAK,SAAUJ,GACjBR,EAAIa,aAAc,GAEpBb,EAAIc,GAAK,SAAUN,GACjBR,EAAIe,cAAgB,UAEtBf,EAAIgB,GAAK,SAAUR,GACjBR,EAAIa,aAAc,KAIpBI,GAAmB,EACnBC,EAAkB,GACtBnB,EAAOoB,eAAgB,G,iCC1DvB,yHAAi4B,eAAG,G,sHCsNp4B,CACAC,gBACA,OACAC,UACAC,aACAC,WACAC,KACAC,oBACAC,mCACAC,QACA,yKACA,yKACA,0KAEAC,6BACAC,2BACAC,qBACAC,yCACAC,uBACAC,mBACAC,SACAC,WACAC,iBACAC,uHACAC,yaAEA7B,uBACAI,eACA0B,sBACAC,wBACAC,qBACA1B,uBACA2B,cACA,cACA,cACA,WACA,aAEAC,cACA,kBACA,qBACA,YACA,eAEAC,gBACA,CACApB,KACAqB,UACAC,oBACAC,6BACAC,uBACAC,uBACAC,cAEA,CACA1B,KACAqB,UACAC,oBACAC,yBACAC,uBACAC,uBACAC,cAEA,CACA1B,KACAqB,gBACAC,oBACAC,6BACAC,uBACAC,qCACAC,iBAKAC,mBAAA,WAEA,2BACA,oCAIAC,qCACA,cACA,6BAIA,OACA,mBAMA,yBAGA,8BAEAC,oBAEAD,2BAEAE,SACAC,iCACAH,yBACA3B,iDAGA+B,6BAEA,wEACA,QACA,4BACA,8CAGAC,0BACA,6BAEAC,oCACA,gCACA,qEAEA,6BAEAC,wBAEA,qBASA,+BASA,sBACA,2BAGA,oBAZAP,aACA3B,+CACAmC,cAXAR,aACA3B,2DACAmC,eAsBAC,0BAAA,WAEAT,eACA3B,kDAIAqC,uBACAV,gBACA,iBACA,0BACA,MAEAW,oCACA,2BAEAX,cACAY,8BAGAC,+BAIAC,0BACA,iCACA,YAGA,sBACAC,4BAIAf,2BAAA/B,SAGA,gCAGA,c,6DCxZA,yHAA4pD,eAAG,G", "file": "pages/events/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/events/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=5398ca44&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=5398ca44&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5398ca44\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/events/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=5398ca44&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showProfileSelector = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.showProfileSelector = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.showProfileSelector = false\n    }\n    _vm.e3 = function ($event) {\n      _vm.showPayment = false\n    }\n    _vm.e4 = function ($event) {\n      _vm.paymentMethod = \"wechat\"\n    }\n    _vm.e5 = function ($event) {\n      _vm.showPayment = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"event-detail\">\n    <!-- 顶部活动轮播图 -->\n    <swiper class=\"event-swiper\" circular indicator-dots autoplay :interval=\"3000\" :duration=\"500\">\n      <swiper-item v-for=\"(item, index) in eventData.images\" :key=\"index\">\n        <image class=\"event-image\" :src=\"item\" mode=\"aspectFill\"></image>\n      </swiper-item>\n    </swiper>\n    \n    <!-- 活动标题和状态 -->\n    <view class=\"event-header\">\n      <view class=\"event-title\">{{ lang === 'zh' ? eventData.title : eventData.title_en }}</view>\n      <view class=\"event-status\" :class=\"eventData.status\">\n        {{ lang === 'zh' ? statusTextZh[eventData.status] : statusTextEn[eventData.status] }}\n      </view>\n    </view>\n    \n    <!-- 活动信息卡片 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n        <view class=\"card-title\">{{ lang === 'zh' ? '活动信息' : 'Event Information' }}</view>\n      </view>\n      <view class=\"info-item\">\n        <u-icon name=\"calendar\" size=\"36rpx\" color=\"#2979ff\"></u-icon>\n        <text class=\"info-label\">{{ lang === 'zh' ? '开始：' : 'Start: ' }}</text>\n        <text class=\"info-content\">{{ eventData.startTime }}</text>\n      </view>\n      <view class=\"info-item\">\n        <u-icon name=\"calendar\" size=\"36rpx\" color=\"#2979ff\"></u-icon>\n        <text class=\"info-label\">{{ lang === 'zh' ? '结束：' : 'End: ' }}</text>\n        <text class=\"info-content\">{{ eventData.endTime }}</text>\n      </view>\n      <view class=\"info-item\">\n        <u-icon name=\"map\" size=\"36rpx\" color=\"#2979ff\"></u-icon>\n        <text class=\"info-label\">{{ lang === 'zh' ? '地点：' : 'Location: ' }}</text>\n        <text class=\"info-content\">{{ lang === 'zh' ? eventData.location : eventData.location_en }}</text>\n      </view>\n      <view class=\"info-item\">\n        <u-icon name=\"account\" size=\"36rpx\" color=\"#2979ff\"></u-icon>\n        <text class=\"info-label\">{{ lang === 'zh' ? '人数：' : 'Capacity: ' }}</text>\n        <text class=\"info-content\">{{ eventData.currentParticipants }}/{{ eventData.maxParticipants }}</text>\n      </view>\n      <view class=\"info-item\">\n        <u-icon name=\"rmb-circle\" size=\"36rpx\" color=\"#2979ff\"></u-icon>\n        <text class=\"info-label\">{{ lang === 'zh' ? '费用：' : 'Fee: ' }}</text>\n        <text class=\"info-content\">{{ eventData.fee > 0 ? '¥' + eventData.fee : (lang === 'zh' ? '免费' : 'Free') }}</text>\n      </view>\n      <view class=\"info-item\">\n        <u-icon name=\"star\" size=\"36rpx\" color=\"#2979ff\"></u-icon>\n        <text class=\"info-label\">{{ lang === 'zh' ? '积分：' : 'Points: ' }}</text>\n        <text class=\"info-content\">+{{ eventData.points }}</text>\n      </view>\n    </view>\n    \n    <!-- 活动详情 -->\n    <view class=\"info-card\">\n      <view class=\"card-header\">\n        <view class=\"card-title\">{{ lang === 'zh' ? '活动详情' : 'Event Details' }}</view>\n      </view>\n      <view class=\"event-description\">{{ lang === 'zh' ? eventData.description : eventData.description_en }}</view>\n    </view>\n    \n    <!-- 报名信息 -->\n    <view class=\"info-card\" v-if=\"eventData.status !== 'ended'\">\n      <view class=\"card-header\">\n        <view class=\"card-title\">{{ lang === 'zh' ? '报名信息' : 'Registration' }}</view>\n      </view>\n      \n      <!-- 选择报名信息 -->\n      <view class=\"select-profile\">\n        <text class=\"select-label\">{{ lang === 'zh' ? '选择报名信息：' : 'Select Profile:' }}</text>\n        <view class=\"profile-selector\" @click=\"showProfileSelector = true\">\n          <text>{{ selectedProfile ? selectedProfile.name : (lang === 'zh' ? '请选择' : 'Please select') }}</text>\n          <u-icon name=\"arrow-right\" size=\"28rpx\" color=\"#909399\"></u-icon>\n        </view>\n      </view>\n      \n      <!-- 显示选中的报名信息 -->\n      <view class=\"selected-profile-info\" v-if=\"selectedProfile\">\n        <view class=\"profile-detail-item\">\n          <text class=\"profile-detail-label\">{{ lang === 'zh' ? '姓名：' : 'Name:' }}</text>\n          <text class=\"profile-detail-value\">{{ selectedProfile.name }}</text>\n        </view>\n        <view class=\"profile-detail-item\">\n          <text class=\"profile-detail-label\">{{ lang === 'zh' ? '手机：' : 'Phone:' }}</text>\n          <text class=\"profile-detail-value\">{{ selectedProfile.phone }}</text>\n        </view>\n        <view class=\"profile-detail-item\" v-if=\"selectedProfile.email\">\n          <text class=\"profile-detail-label\">{{ lang === 'zh' ? '邮箱：' : 'Email:' }}</text>\n          <text class=\"profile-detail-value\">{{ selectedProfile.email }}</text>\n        </view>\n        <view class=\"profile-detail-item\" v-if=\"selectedProfile.idCard\">\n          <text class=\"profile-detail-label\">{{ lang === 'zh' ? '身份证：' : 'ID Card:' }}</text>\n          <text class=\"profile-detail-value\">{{ selectedProfile.idCard }}</text>\n        </view>\n        <view class=\"profile-detail-item\" v-if=\"selectedProfile.address\">\n          <text class=\"profile-detail-label\">{{ lang === 'zh' ? '地址：' : 'Address:' }}</text>\n          <text class=\"profile-detail-value\">{{ selectedProfile.address }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作栏 -->\n    <view class=\"bottom-bar\" v-if=\"eventData.status !== 'ended'\">\n      <view class=\"bottom-price\">\n        <text class=\"price-label\">{{ lang === 'zh' ? '费用：' : 'Fee: ' }}</text>\n        <text class=\"price-value\" v-if=\"eventData.fee > 0\">¥{{ eventData.fee }}</text>\n        <text class=\"price-value\" v-else>{{ lang === 'zh' ? '免费' : 'Free' }}</text>\n      </view>\n      <view class=\"bottom-button\" :class=\"{ disabled: !selectedProfile || eventData.status === 'full' }\" @click=\"handleSignup\">\n        {{ lang === 'zh' ? '立即报名' : 'Sign Up' }}\n      </view>\n    </view>\n    <!-- <view class=\"safe-area-inset-bottom\"></view> -->\n    \n    <!-- 报名信息选择弹窗 -->\n    <u-popup :show=\"showProfileSelector\" mode=\"bottom\" @close=\"showProfileSelector = false\">\n      <view class=\"popup-content\">\n        <view class=\"popup-header\">\n          <text>{{ lang === 'zh' ? '选择报名信息' : 'Select Profile' }}</text>\n        </view>\n        <scroll-view scroll-y class=\"profile-list\">\n          <view \n            v-for=\"(profile, index) in signupProfiles\" \n            :key=\"index\" \n            class=\"profile-item\"\n            :class=\"{ 'selected': selectedProfileIndex === index, 'default': profile.isDefault }\"\n            @click=\"selectProfile(index)\"\n          >\n            <view class=\"profile-info\">\n              <text class=\"profile-name\">{{ profile.name }}</text>\n              <text class=\"profile-phone\">{{ profile.phone }}</text>\n            </view>\n            <u-icon v-if=\"selectedProfileIndex === index\" name=\"checkmark\" color=\"#2979ff\" size=\"40rpx\"></u-icon>\n          </view>\n        </scroll-view>\n        <view class=\"popup-buttons\">\n          <view class=\"cancel-button\" @click=\"showProfileSelector = false\">\n            {{ lang === 'zh' ? '取消' : 'Cancel' }}\n          </view>\n          <view class=\"confirm-button\" @click=\"confirmProfileSelection\">\n            {{ lang === 'zh' ? '确认' : 'Confirm' }}\n          </view>\n        </view>\n      </view>\n    </u-popup>\n    \n    <!-- 支付弹窗 -->\n    <u-popup :show=\"showPayment\" mode=\"bottom\" @close=\"showPayment = false\">\n      <view class=\"popup-content\">\n        <view class=\"popup-header\">\n          <text>{{ lang === 'zh' ? '支付费用' : 'Payment' }}</text>\n        </view>\n        <view class=\"payment-info\">\n          <view class=\"payment-item\">\n            <text class=\"payment-label\">{{ lang === 'zh' ? '活动名称：' : 'Event: ' }}</text>\n            <text class=\"payment-value\">{{ lang === 'zh' ? eventData.title : eventData.title_en }}</text>\n          </view>\n          <view class=\"payment-item\">\n            <text class=\"payment-label\">{{ lang === 'zh' ? '报名人：' : 'Participant: ' }}</text>\n            <text class=\"payment-value\">{{ selectedProfile ? selectedProfile.name : '' }}</text>\n          </view>\n          <view class=\"payment-item\">\n            <text class=\"payment-label\">{{ lang === 'zh' ? '应付金额：' : 'Amount: ' }}</text>\n            <text class=\"payment-value payment-amount\">¥{{ eventData.fee }}</text>\n          </view>\n        </view>\n        <view class=\"payment-methods\">\n          <view class=\"payment-title\">{{ lang === 'zh' ? '支付方式' : 'Payment Method' }}</view>\n          <view \n            class=\"payment-method-item\"\n            :class=\"{ 'selected': paymentMethod === 'wechat' }\"\n            @click=\"paymentMethod = 'wechat'\"\n          >\n            <view class=\"payment-method-icon wechat\">\n              <u-icon name=\"weixin-fill\" color=\"#09BB07\" size=\"40rpx\"></u-icon>\n            </view>\n            <view class=\"payment-method-name\">\n              {{ lang === 'zh' ? '微信支付' : 'WeChat Pay' }}\n            </view>\n            <u-icon v-if=\"paymentMethod === 'wechat'\" name=\"checkmark\" color=\"#2979ff\" size=\"40rpx\"></u-icon>\n          </view>\n        </view>\n        <view class=\"popup-buttons\">\n          <view class=\"cancel-button\" @click=\"showPayment = false\">\n            {{ lang === 'zh' ? '取消' : 'Cancel' }}\n          </view>\n          <view class=\"confirm-button\" @click=\"processPayment\">\n            {{ lang === 'zh' ? '确认支付' : 'Pay Now' }}\n          </view>\n        </view>\n      </view>\n    </u-popup>\n    \n    <!-- 支付成功弹窗 -->\n    <u-popup :show=\"showPaymentSuccess\" mode=\"center\" @close=\"handlePaymentSuccessDone\" :round=\"16\" :safeAreaInsetBottom=\"false\">\n      <view class=\"success-popup\">\n        <u-icon name=\"checkmark-circle\" color=\"#09BB07\" size=\"120rpx\"></u-icon>\n        <text class=\"success-title\">{{ lang === 'zh' ? '支付成功' : 'Payment Successful' }}</text>\n        <text class=\"success-message\">{{ lang === 'zh' ? '您已成功报名参加活动' : 'You have successfully signed up for the event' }}</text>\n        <view class=\"success-button\" @click=\"handlePaymentSuccessDone\">\n          {{ lang === 'zh' ? '完成' : 'Done' }}\n        </view>\n      </view>\n    </u-popup>\n    \n    <!-- 语言切换按钮 -->\n    <view class=\"lang-switch\" @click=\"switchLanguage\">\n      <text>{{ lang === 'zh' ? 'EN' : '中' }}</text>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      lang: 'zh', // 默认语言\n      eventId: null,\n      eventData: {\n        id: 1,\n        title: '2023夏季编程训练营',\n        title_en: '2023 Summer Coding Camp',\n        images: [\n          'https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',\n          'https://images.unsplash.com/photo-1531482615713-2afd69097998?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',\n          'https://images.unsplash.com/photo-1581472723648-909f4851d4ae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80'\n        ],\n        startTime: '2023-07-15 09:00',\n        endTime: '2023-07-20 17:00',\n        location: '深圳市南山区科技园',\n        location_en: 'Nanshan District, Shenzhen',\n        currentParticipants: 45,\n        maxParticipants: 50,\n        fee: 1980,\n        points: 200,\n        status: 'ongoing', // ongoing, coming, full, ended\n        description: '本次训练营将带领学员深入学习人工智能和机器学习的核心概念和实践应用。课程包括Python编程基础、数据分析、机器学习算法以及深度学习入门等内容。学员将通过实际项目来巩固所学知识，提升编程能力和解决问题的能力。',\n        description_en: 'This training camp will lead students to deeply learn the core concepts and practical applications of artificial intelligence and machine learning. The course includes Python programming basics, data analysis, machine learning algorithms, and an introduction to deep learning. Students will consolidate their knowledge and improve their programming and problem-solving abilities through practical projects.'\n      },\n      showProfileSelector: false,\n      showPayment: false,\n      showPaymentSuccess: false,\n      selectedProfileIndex: -1,\n      selectedProfile: null,\n      paymentMethod: 'wechat',\n      statusTextZh: {\n        'ongoing': '进行中',\n        'coming': '即将开始',\n        'full': '已满员',\n        'ended': '已结束'\n      },\n      statusTextEn: {\n        'ongoing': 'Ongoing',\n        'coming': 'Coming Soon',\n        'full': 'Full',\n        'ended': 'Ended'\n      },\n      signupProfiles: [\n        {\n          id: 1,\n          name: '张三',\n          phone: '138****1234',\n          email: '<EMAIL>',\n          idCard: '440******1234',\n          address: '广东省深圳市南山区科技园',\n          isDefault: true\n        },\n        {\n          id: 2,\n          name: '李四',\n          phone: '139****5678',\n          email: '<EMAIL>',\n          idCard: '440******5678',\n          address: '广东省深圳市福田区中心区',\n          isDefault: false\n        },\n        {\n          id: 3,\n          name: 'John Doe',\n          phone: '135****9012',\n          email: '<EMAIL>',\n          idCard: '440******9012',\n          address: 'Nanshan District, Shenzhen',\n          isDefault: false\n        }\n      ]\n    };\n  },\n  onLoad(options) {\n    // 从全局获取语言设置\n    if (getApp().globalData.lang) {\n      this.lang = getApp().globalData.lang;\n    }\n    \n    // 监听语言变化\n    uni.$on('languageChanged', (data) => {\n      this.lang = data.lang;\n      this.setNavigationBarTitle();\n    });\n    \n    // 获取活动ID\n    if (options.id) {\n      this.eventId = options.id;\n      // 实际应用中，这里应该通过API获取活动详情\n      // this.fetchEventDetail(this.eventId);\n    }\n    \n    // 设置默认选中的报名信息\n    this.setDefaultProfile();\n    \n    // 设置导航栏标题\n    this.setNavigationBarTitle();\n  },\n  onUnload() {\n    // 取消监听语言变化\n    uni.$off('languageChanged');\n  },\n  methods: {\n    setNavigationBarTitle() {\n      uni.setNavigationBarTitle({\n        title: this.lang === 'zh' ? '活动详情' : 'Event Details'\n      });\n    },\n    setDefaultProfile() {\n      // 设置默认选中的报名信息\n      const defaultIndex = this.signupProfiles.findIndex(profile => profile.isDefault);\n      if (defaultIndex !== -1) {\n        this.selectedProfileIndex = defaultIndex;\n        this.selectedProfile = this.signupProfiles[defaultIndex];\n      }\n    },\n    selectProfile(index) {\n      this.selectedProfileIndex = index;\n    },\n    confirmProfileSelection() {\n      if (this.selectedProfileIndex !== -1) {\n        this.selectedProfile = this.signupProfiles[this.selectedProfileIndex];\n      }\n      this.showProfileSelector = false;\n    },\n    handleSignup() {\n      // 检查是否已选择报名信息\n      if (!this.selectedProfile) {\n        uni.showToast({\n          title: this.lang === 'zh' ? '请选择报名信息' : 'Please select a profile',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 检查活动状态\n      if (this.eventData.status === 'full') {\n        uni.showToast({\n          title: this.lang === 'zh' ? '活动已满员' : 'Event is full',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 如果活动免费，直接完成报名\n      if (this.eventData.fee <= 0) {\n        this.showPaymentSuccess = true;\n      } else {\n        // 否则显示支付弹窗\n        this.showPayment = true;\n      }\n    },\n    processPayment() {\n      // 模拟支付过程\n      uni.showLoading({\n        title: this.lang === 'zh' ? '处理中...' : 'Processing...'\n      });\n      \n      // 延迟2秒模拟支付过程\n      setTimeout(() => {\n        uni.hideLoading();\n        this.showPayment = false;\n        this.showPaymentSuccess = true;\n      }, 2000);\n    },\n    handlePaymentSuccessDone() {\n      this.showPaymentSuccess = false;\n      // 返回上一页或跳转到我的活动页面\n      uni.navigateTo({\n        url: '/pages/my-join/index'\n      });\n    },\n    fetchEventDetail(id) {\n      // 实际应用中，这里应该通过API获取活动详情\n      // 这里仅作为示例，使用静态数据\n    },\n    switchLanguage() {\n      const newLang = this.lang === 'zh' ? 'en' : 'zh';\n      this.lang = newLang;\n      \n      // 更新全局语言设置\n      if (getApp().globalData) {\n        getApp().globalData.lang = newLang;\n      }\n      \n      // 发送语言变化事件\n      uni.$emit('languageChanged', { lang: newLang });\n      \n      // 设置导航栏标题\n      this.setNavigationBarTitle();\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.event-detail {\n  padding-bottom: calc(120rpx + constant(safe-area-inset-bottom)); /* iOS 11.0 */\n  padding-bottom: calc(120rpx + env(safe-area-inset-bottom)); /* iOS 11.2+ */\n  background-color: #f5f5f5;\n}\n\n.event-swiper {\n  width: 100%;\n  height: 450rpx;\n}\n\n.event-image {\n  width: 100%;\n  height: 100%;\n}\n\n.event-header {\n  padding: 30rpx;\n  background-color: #ffffff;\n}\n\n.event-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #303133;\n  line-height: 1.5;\n  margin-bottom: 16rpx;\n}\n\n.event-status {\n  display: inline-block;\n  padding: 6rpx 16rpx;\n  border-radius: 20rpx;\n  font-size: 24rpx;\n  color: #ffffff;\n  \n  &.ongoing {\n    background-color: #2979ff;\n  }\n  \n  &.coming {\n    background-color: #ff9900;\n  }\n  \n  &.full {\n    background-color: #909399;\n  }\n  \n  &.ended {\n    background-color: #c0c4cc;\n  }\n}\n\n.info-card {\n  margin: 20rpx 30rpx;\n  border-radius: 16rpx;\n  background-color: #ffffff;\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\n  overflow: hidden;\n}\n\n.card-header {\n  padding: 24rpx 30rpx;\n  border-bottom: 1rpx solid #f2f2f2;\n}\n\n.card-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #303133;\n  position: relative;\n  padding-left: 20rpx;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    left: 0;\n    top: 8rpx;\n    bottom: 8rpx;\n    width: 8rpx;\n    background-color: #2979ff;\n    border-radius: 4rpx;\n  }\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  padding: 20rpx 30rpx;\n  border-bottom: 1rpx solid #f8f8f8;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.info-label {\n  margin-left: 16rpx;\n  color: #606266;\n  font-size: 28rpx;\n  width: 120rpx;\n}\n\n.info-content {\n  flex: 1;\n  font-size: 28rpx;\n  color: #303133;\n}\n\n.event-description {\n  padding: 20rpx 30rpx 30rpx;\n  font-size: 28rpx;\n  color: #606266;\n  line-height: 1.6;\n}\n\n.select-profile {\n  display: flex;\n  align-items: center;\n  padding: 20rpx 30rpx 30rpx;\n}\n\n.select-label {\n  font-size: 28rpx;\n  color: #606266;\n}\n\n.profile-selector {\n  flex: 1;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16rpx 20rpx;\n  background-color: #f5f5f5;\n  border-radius: 8rpx;\n  margin-left: 20rpx;\n  \n  text {\n    font-size: 28rpx;\n    color: #303133;\n  }\n}\n\n.selected-profile-info {\n  padding: 20rpx 30rpx 30rpx;\n  border-top: 1rpx solid #f8f8f8;\n}\n\n.profile-detail-item {\n  display: flex;\n  margin-bottom: 16rpx;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.profile-detail-label {\n  font-size: 28rpx;\n  color: #606266;\n  width: 120rpx;\n}\n\n.profile-detail-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #303133;\n}\n\n.bottom-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 100rpx;\n  background-color: #ffffff;\n  display: flex;\n  align-items: center;\n  padding: 0 30rpx;\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\n  padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */\n  padding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */\n}\n\n.safe-area-inset-bottom {\n  height: constant(safe-area-inset-bottom); /* iOS 11.0 */\n  height: env(safe-area-inset-bottom); /* iOS 11.2+ */\n}\n\n.bottom-price {\n  flex: 1;\n}\n\n.price-label {\n  font-size: 28rpx;\n  color: #606266;\n}\n\n.price-value {\n  font-size: 36rpx;\n  color: #ff5722;\n  font-weight: bold;\n}\n\n.bottom-button {\n  width: 240rpx;\n  height: 70rpx;\n  line-height: 70rpx;\n  text-align: center;\n  background-color: #2979ff;\n  color: #ffffff;\n  border-radius: 35rpx;\n  font-size: 30rpx;\n  \n  &.disabled {\n    background-color: #a0cfff;\n  }\n}\n\n.popup-content {\n  padding: 30rpx;\n}\n\n.popup-header {\n  text-align: center;\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 30rpx;\n}\n\n.profile-list {\n  max-height: 600rpx;\n}\n\n.profile-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20rpx;\n  border-bottom: 1rpx solid #f2f2f2;\n  \n  &.selected {\n    background-color: #f5f7fa;\n  }\n  \n  &.default {\n    border: 2rpx solid #2979ff;\n    border-radius: 8rpx;\n    margin-bottom: 10rpx;\n  }\n}\n\n.profile-info {\n  display: flex;\n  flex-direction: column;\n}\n\n.profile-name {\n  font-size: 28rpx;\n  color: #303133;\n  margin-bottom: 8rpx;\n}\n\n.profile-phone {\n  font-size: 24rpx;\n  color: #909399;\n}\n\n.popup-buttons {\n  display: flex;\n  justify-content: space-between;\n  margin-top: 30rpx;\n}\n\n.cancel-button {\n  flex: 1;\n  height: 80rpx;\n  line-height: 80rpx;\n  text-align: center;\n  background-color: #f2f2f2;\n  color: #606266;\n  border-radius: 8rpx;\n  margin-right: 20rpx;\n  font-size: 28rpx;\n}\n\n.confirm-button {\n  flex: 1;\n  height: 80rpx;\n  line-height: 80rpx;\n  text-align: center;\n  background-color: #2979ff;\n  color: #ffffff;\n  border-radius: 8rpx;\n  margin-left: 20rpx;\n  font-size: 28rpx;\n}\n\n.payment-info {\n  margin-bottom: 30rpx;\n}\n\n.payment-item {\n  display: flex;\n  margin-bottom: 16rpx;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.payment-label {\n  width: 160rpx;\n  font-size: 28rpx;\n  color: #606266;\n}\n\n.payment-value {\n  flex: 1;\n  font-size: 28rpx;\n  color: #303133;\n  \n  &.payment-amount {\n    color: #ff5722;\n    font-weight: bold;\n    font-size: 32rpx;\n  }\n}\n\n.payment-methods {\n  margin-bottom: 30rpx;\n}\n\n.payment-title {\n  font-size: 28rpx;\n  color: #606266;\n  margin-bottom: 20rpx;\n}\n\n.payment-method-item {\n  display: flex;\n  align-items: center;\n  padding: 20rpx;\n  border: 1rpx solid #f2f2f2;\n  border-radius: 8rpx;\n  \n  &.selected {\n    border-color: #2979ff;\n    background-color: #f5f7fa;\n  }\n}\n\n.payment-method-icon {\n  width: 60rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 20rpx;\n}\n\n.payment-method-name {\n  flex: 1;\n  font-size: 28rpx;\n  color: #303133;\n}\n\n.success-popup {\n  width: 500rpx;\n  padding: 50rpx 30rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.success-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #303133;\n  margin: 30rpx 0 20rpx;\n}\n\n.success-message {\n  font-size: 28rpx;\n  color: #606266;\n  margin-bottom: 40rpx;\n  text-align: center;\n}\n\n.success-button {\n  width: 60%;\n  height: 80rpx;\n  line-height: 80rpx;\n  text-align: center;\n  background-color: #2979ff;\n  color: #ffffff;\n  border-radius: 40rpx;\n  font-size: 30rpx;\n}\n\n.lang-switch {\n  position: fixed;\n  right: 30rpx;\n  top: 30rpx;\n  width: 80rpx;\n  height: 80rpx;\n  background-color: rgba(0, 0, 0, 0.5);\n  color: #ffffff;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  z-index: 10;\n}\n</style> ", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=5398ca44&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=5398ca44&lang=scss&scoped=true&\""], "sourceRoot": ""}