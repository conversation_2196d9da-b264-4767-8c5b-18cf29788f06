{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/events/index.vue?5be3", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/events/index.vue?bf01", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/events/index.vue?4dc6", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/events/index.vue?22b7", "uni-app:///pages/events/index.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/events/index.vue?66fe"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uSearch", "uImage", "uLoadmore", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "l0", "_self", "_c", "__map", "filteredActivities", "item", "__i0__", "$orig", "__get_orig", "g0", "beginTime", "split", "g1", "endTime", "g2", "activityList", "length", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "CustomTabbar", "LangSwitch", "lang", "searchValue", "currentCategory", "currentSort", "contentHeight", "categoryList", "id", "name", "name_en", "currentPage", "pageSize", "total", "loading", "hasMore", "loadMoreStatus", "computed", "list", "onLoad", "uni", "setTimeout", "onShow", "onUnload", "methods", "calcContentHeight", "onCategoryClick", "onSortChange", "onSearch", "navigateToEventDetail", "url", "onLangChange", "setNavigationBarTitle", "title", "getCategoryList", "pg", "size", "response", "categories", "types", "logo", "sort", "getActivityList", "isRefresh", "apiParams", "selectedCate<PERSON><PERSON>", "newList", "titleEN", "img", "contents", "contentsEN", "address", "addressEN", "price", "nums", "regNums", "integral", "state", "typesName", "icon", "loadMore"], "mappings": "6IAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,yBACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,QAAS,WACP,OAAO,sHAITC,OAAQ,WACN,OAAO,oHAITC,UAAW,WACT,OAAO,2HAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,MAAMP,EAAIQ,oBAAoB,SAAUC,EAAMC,GACzD,IAAIC,EAAQX,EAAIY,WAAWH,GACvBI,EAAKJ,EAAKK,UAAYL,EAAKK,UAAUC,MAAM,KAAO,KAClDC,EAAKP,EAAKQ,QAAUR,EAAKQ,QAAQF,MAAM,KAAO,KAClD,MAAO,CACLJ,MAAOA,EACPE,GAAIA,EACJG,GAAIA,OAGJE,EAAKlB,EAAImB,aAAaC,OAC1BpB,EAAIqB,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLrB,GAAIA,EACJc,GAAIA,MAKRQ,GAAmB,EACnBC,EAAkB,GACtB5B,EAAO6B,eAAgB,G,iCC/DvB,yHAAg4B,eAAG,G,gKCkEn4B,SACA,uOACA,CACAtC,YAAAuC,eAAAC,cACAR,gBACA,OACAS,oCACAC,eACAC,kBACAC,cACAC,gBACAC,cACA,CAAAC,KAAAC,UAAAC,gBAEApB,gBAEAqB,cACAC,YACAC,QACAC,WACAC,WACAC,4BAGAC,UACAtC,8BAGA,uCAcA,OAXA,qBAEAuC,gCAAA,0BACA,qBAEAA,gCAAA,sDACA,uBAEAA,gCAAA,2BAGA,IAGAC,mBAAA,WACAnD,eAEA,8CACA,mCAEA,yCAIAoD,iCACA,SACA,0BACA,qBAIA,6BAGAC,uBACA,wBACA,KAGA,uBAGA,wBAEAC,kBAEA,QAEA,uCACA,qBACAlB,IAEAgB,yCAEA,cACA,0BAIAG,oBAEAH,0BACAA,uBAEAI,SACAC,6BAAA,WACA,wBACAzD,eAEAoD,iFACApD,sBACA,mBACA,oCACA,QAEA0D,4BACA1D,yBACAA,uCACAA,2CAEA,uBAEA,0BAEA2D,yBACA,oBAEAC,qBACA,mBAEA,0BAEAC,kCACAT,cACAU,+CAGAC,yBACA,YACA,6BAEA,2CACA,0BAGAC,iCACAZ,yBACAa,0CAKAC,2BAAA,sKAEA,sBACAC,KACAC,UACA,OAHAC,SAKArE,2BAEA,uBAEAsE,0BAAA,OACA9B,QACAC,gBACAC,6BACA6B,cACAC,sCACAC,mBAIA,gBACA,CAAAjC,KAAAC,UAAAC,cAAA6B,UAAA,qBACAD,KAGAtE,6BACA,mDAEAA,gCAAA,wDA7BA,IAkCA0E,2BAAA,8JAoCA,OApCAC,qCAAA,SAEA,GACA,gBACA,kBACAvB,eACAa,6CAGA,2BAIAW,GACAT,iBACAC,iBAIA,yDACAS,oCAEA,WACAD,cAEA5E,+CAIA,sCAEA,cACA4E,6BAEAA,gCAEA,UAEA,gCAAAP,SAEArE,2BAEA,uBAEA8E,0BAAA,OACAtC,QACA+B,cACAN,kBACAc,sBACA9D,0BACAG,sBACA4D,oCACAC,wBACAC,4BACAC,sBACAC,0BACAC,iBACAC,eACAC,qBACAC,uBACAC,cACAC,8BAIA,eADA,EACA,EAEA,0DAGA,mBAGA,qBACA,0BACA,eAEA,4BACA,gBAGA1F,6BACAoD,aACAa,mDACA0B,cAEA,6BACA,qDAEA3F,gCACAoD,aACAa,2CACA0B,cAEA,oCAIA,OAJA,UAEA,GACAvC,gBACA,4EAlGA,IAuGAwC,oBAAA,uIACA,yFAEA,gCACA,gEAJA,MAOA,c,6DCjWA,yHAAuxC,eAAG,G", "file": "pages/events/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/events/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=338b5f55&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=338b5f55&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"338b5f55\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/events/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=338b5f55&scoped=true&\"", "var components\ntry {\n  components = {\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-search/u-search\" */ \"uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    uImage: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-image/u-image\" */ \"uview-ui/components/u-image/u-image.vue\"\n      )\n    },\n    uLoadmore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loadmore/u-loadmore\" */ \"uview-ui/components/u-loadmore/u-loadmore.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.filteredActivities, function (item, __i0__) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.beginTime ? item.beginTime.split(\" \") : null\n    var g1 = item.endTime ? item.endTime.split(\" \") : null\n    return {\n      $orig: $orig,\n      g0: g0,\n      g1: g1,\n    }\n  })\n  var g2 = _vm.activityList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"events-page\">\n    <view class=\"main-content\">\n      <!-- 左侧分类导航，独立滚动 -->\n      <scroll-view class=\"sidebar-scroll\" scroll-y :show-scrollbar=\"true\" :style=\"{ height: contentHeight + 'px' }\">\n        <view class=\"sidebar\">\n          <view v-for=\"(item, idx) in categoryList\" :key=\"item.id\"\n            :class=\"['sidebar-item', idx === currentCategory ? 'active' : '']\" @click=\"onCategoryClick(idx)\">\n            <text class=\"sidebar-title\">{{ lang === 'zh' ? item.name : item.name_en }}</text>\n          </view>\n        </view>\n      </scroll-view>\n      <!-- 右侧内容区，独立滚动 -->\n      <scroll-view class=\"activity-panel-scroll\" scroll-y :show-scrollbar=\"true\"\n        :style=\"{ height: contentHeight + 'px' }\">\n        <view class=\"activity-panel\">\n          <view class=\"search-bar-fix\">\n            <u-search :placeholder=\"lang === 'zh' ? '搜索活动' : 'Search Events'\" v-model=\"searchValue\" @search=\"onSearch\"\n              :show-action=\"false\" />\n          </view>\n          <!-- <view class=\"sort-tabs\">\n            <u-tabs :list=\"sortTabs\" :current=\"currentSort\" @change=\"onSortChange\" lineColor=\"#2979ff\" />\n          </view> -->\n          <view class=\"activity-list\">\n            <view v-for=\"item in filteredActivities\" :key=\"item.id\" class=\"activity-card\"\n              @click=\"navigateToEventDetail(item)\">\n              <u-image :src=\"item.img\" width=\"100%\" height=\"200rpx\" border-radius=\"16rpx 16rpx 0 0\" />\n              <view class=\"card-content\">\n                <view class=\"card-title\">{{ lang === 'zh' ? item.title : (item.titleEN || item.title) }}</view>\n                <view class=\"card-subtitle\">\n                  <text>{{ item.beginTime ? item.beginTime.split(' ')[0] : '' }}</text>\n                  <text v-if=\"item.endTime\"> - {{ item.endTime.split(' ')[0] }}</text>\n                </view>\n                <view class=\"card-desc\">{{ lang === 'zh' ? item.contents : (item.contentsEN || item.contents) }}</view>\n                <view class=\"card-info\">\n                  <text class=\"price\">¥{{ item.price }}</text>\n                  <text class=\"nums\">{{ item.regNums }}/{{ item.nums }}人</text>\n                </view>\n              </view>\n            </view>\n\n            <!-- 加载更多 -->\n            <u-loadmore\n              :status=\"loadMoreStatus\"\n              :loading-text=\"lang === 'zh' ? '正在加载...' : 'Loading...'\"\n              :loadmore-text=\"lang === 'zh' ? '点击加载更多' : 'Load more'\"\n              :nomore-text=\"lang === 'zh' ? '没有更多了' : 'No more data'\"\n              @loadmore=\"loadMore\"\n              margin-top=\"20\"\n              margin-bottom=\"20\"\n              v-if=\"activityList.length > 0\"\n            ></u-loadmore>\n          </view>\n        </view>\n      </scroll-view>\n    </view>\n    <view class=\"custom-tabbar\">\n      <CustomTabbar :current=\"1\" />\n    </view>\n    <LangSwitch />\n  </view>\n</template>\n\n<script>\nimport CustomTabbar from '../../components/CustomTabbar.vue'\nimport LangSwitch from '../../components/LangSwitch.vue'\nimport { getOperationList, getCategoryList } from '@/api/user.js'\nimport { formatImageUrl } from '@/utils/http.js'\nexport default {\n  components: { CustomTabbar, LangSwitch },\n  data() {\n    return {\n      lang: uni.getStorageSync('lang') || 'zh',\n      searchValue: '',\n      currentCategory: 0,\n      currentSort: 0,\n      contentHeight: 0, // 动态内容区高度\n      categoryList: [\n        { id: 0, name: '全部', name_en: 'All' } // 默认的\"全部\"分类\n      ],\n      activityList: [],\n      // 分页相关\n      currentPage: 1,\n      pageSize: 10,\n      total: 0,\n      loading: false,\n      hasMore: true,\n      loadMoreStatus: 'loadmore'\n    }\n  },\n  computed: {\n    filteredActivities() {\n      // 现在搜索和分类筛选都通过API完成，这里只需要返回活动列表\n      // 可以在这里添加前端排序逻辑\n      let list = [...this.activityList];\n\n      // 排序（如果需要的话）\n      if (this.currentSort === 0) {\n        // 按状态排序（可用的优先）\n        list = list.slice().sort((a, b) => b.state - a.state);\n      } else if (this.currentSort === 1) {\n        // 按时间倒序\n        list = list.slice().sort((a, b) => new Date(b.beginTime) - new Date(a.beginTime));\n      } else if (this.currentSort === 2) {\n        // 按价格排序\n        list = list.slice().sort((a, b) => a.price - b.price);\n      }\n\n      return list;\n    }\n  },\n  onLoad(options) {\n    console.log(options)\n    // 从全局获取语言设置\n    if (getApp().globalData && getApp().globalData.lang) {\n      this.lang = getApp().globalData.lang;\n    } else {\n      this.lang = uni.getStorageSync('lang') || 'zh';\n    }\n\n    // 监听语言变化\n    uni.$on('lang-change', (newLang) => {\n      this.lang = newLang;\n      this.setNavigationBarTitle();\n      this.onLangChange(newLang);\n    });\n\n    // 设置导航栏标题\n    this.setNavigationBarTitle();\n\n    // 计算滚动区域高度\n    setTimeout(() => {\n      this.calcContentHeight();\n    }, 500);\n\n    // 获取分类列表\n    this.getCategoryList();\n\n    // 获取活动列表\n    this.getActivityList();\n  },\n  onShow() {\n    // 检查是否有从首页传递过来的分类选择\n    let currentCategory = 0\n    // 从本地存储获取\n    const storedCategory = uni.getStorageSync('selectedCategory');\n    if (storedCategory !== '' && storedCategory !== undefined) {\n      currentCategory = storedCategory;\n      // 使用后清除\n      uni.removeStorageSync('selectedCategory');\n      // 如果找到有效的分类索引，则设置当前分类\n      if (currentCategory !== -1 && currentCategory >= 0) {\n        this.currentCategory = currentCategory;\n      }\n    }\n  },\n  onUnload() {\n    // 取消监听语言变化\n    uni.$off('languageChanged');\n    uni.$off('lang-change');\n  },\n  methods: {\n    calcContentHeight() {\n      const sys = uni.getSystemInfoSync()\n      console.log(sys)\n      // 获取tabbar实际高度\n      uni.createSelectorQuery().select('.custom-tabbar').boundingClientRect(rect => {\n        console.log(\"大考方便\", rect)\n        let tabbarPx = rect ? rect.height : 0\n        this.contentHeight = sys.windowHeight - tabbarPx\n      }).exec()\n    },\n    onCategoryClick(idx) {\n      console.log('点击分类索引:', idx);\n      console.log('分类列表:', this.categoryList);\n      console.log('选择的分类:', this.categoryList[idx]);\n\n      this.currentCategory = idx;\n      // 切换分类时重新获取数据\n      this.getActivityList(true);\n    },\n    onSortChange(idx) {\n      this.currentSort = idx\n    },\n    onSearch(val) {\n      this.searchValue = val;\n      // 搜索时重新获取数据\n      this.getActivityList(true);\n    },\n    navigateToEventDetail(item) {\n      uni.navigateTo({\n        url: `/pages/events/detail?id=${item.id}`\n      });\n    },\n    onLangChange(newLang) {\n      this.lang = newLang;\n      this.setNavigationBarTitle();\n      // 切换语言时，如果有搜索关键词，需要重新获取数据（因为搜索字段不同）\n      if (this.searchValue && this.searchValue.trim()) {\n        this.getActivityList(true);\n      }\n    },\n    setNavigationBarTitle() {\n      uni.setNavigationBarTitle({\n        title: this.lang === 'zh' ? '活动列表' : 'Events'\n      });\n    },\n\n    // 获取分类列表\n    async getCategoryList() {\n      try {\n        const response = await getCategoryList({\n          pg: 1,\n          size: 20\n        });\n\n        console.log('获取分类列表响应:', response);\n\n        if (response && response.code === 0 && response.data) {\n          // 将API返回的分类数据转换为页面需要的格式\n          const categories = response.data.map(item => ({\n            id: item.id,\n            name: item.name || '',\n            name_en: item.nameEN || item.name || '',\n            types: item.types,\n            logo: formatImageUrl(item.logo || ''),\n            sort: item.sort || 0\n          }));\n\n          // 在分类列表前面添加\"全部\"选项\n          this.categoryList = [\n            { id: 0, name: '全部', name_en: 'All', types: 0 },\n            ...categories\n          ];\n        } else {\n          console.error('获取分类列表失败:', response);\n        }\n      } catch (error) {\n        console.error('获取分类列表异常:', error);\n      }\n    },\n\n    // 获取活动列表\n    async getActivityList(isRefresh = true) {\n      try {\n        if (isRefresh) {\n          this.currentPage = 1;\n          this.activityList = [];\n          uni.showLoading({\n            title: this.lang === 'zh' ? '加载中...' : 'Loading...'\n          });\n        } else {\n          this.loadMoreStatus = 'loading';\n        }\n\n        // 准备API参数\n        const apiParams = {\n          pg: this.currentPage,\n          size: this.pageSize\n        };\n\n        // 如果选择了具体分类（不是\"全部\"），添加分类参数\n        if (this.currentCategory > 0 && this.categoryList[this.currentCategory]) {\n          const selectedCategory = this.categoryList[this.currentCategory];\n          // 只有当分类ID不为0时才传递分类参数（0表示\"全部\"）\n          if (selectedCategory.id !== 0) {\n            apiParams.types = selectedCategory.id; // 使用分类的id作为筛选参数\n          }\n          console.log('选择的分类:', selectedCategory, '传递的types参数:', apiParams.types);\n        }\n\n        // 如果有搜索关键词，添加搜索参数\n        if (this.searchValue && this.searchValue.trim()) {\n          // 根据当前语言选择搜索字段\n          if (this.lang === 'zh') {\n            apiParams.title = this.searchValue.trim();\n          } else {\n            apiParams.titleEN = this.searchValue.trim();\n          }\n        }\n\n        const response = await getOperationList(apiParams);\n\n        console.log('获取活动列表响应:', response);\n\n        if (response && response.code === 0 && response.data) {\n          // 将API返回的数据转换为页面需要的格式\n          const newList = response.data.map(item => ({\n            id: item.id,\n            types: item.types, // 分类ID\n            title: item.title || '',\n            titleEN: item.titleEN || '',\n            beginTime: item.beginTime || '',\n            endTime: item.endTime || '',\n            img: formatImageUrl(item.img || ''),\n            contents: item.contents || '',\n            contentsEN: item.contentsEN || '',\n            address: item.address || '',\n            addressEN: item.addressEN || '',\n            price: item.price || 0,\n            nums: item.nums || 0,\n            regNums: item.regNums || 0,\n            integral: item.integral || 0,\n            state: item.state,\n            typesName: item.typesName || ''\n          }));\n\n          if (isRefresh) {\n            this.activityList = newList;\n          } else {\n            this.activityList = [...this.activityList, ...newList];\n          }\n\n          this.total = response.count || 0;\n\n          // 判断是否还有更多数据\n          if (newList.length < this.pageSize) {\n            this.loadMoreStatus = 'nomore';\n            this.hasMore = false;\n          } else {\n            this.loadMoreStatus = 'loadmore';\n            this.hasMore = true;\n          }\n        } else {\n          console.error('获取活动列表失败:', response);\n          uni.showToast({\n            title: this.lang === 'zh' ? '获取数据失败' : 'Failed to load data',\n            icon: 'none'\n          });\n          this.loadMoreStatus = 'loadmore';\n        }\n      } catch (error) {\n        console.error('获取活动列表异常:', error);\n        uni.showToast({\n          title: this.lang === 'zh' ? '网络错误' : 'Network error',\n          icon: 'none'\n        });\n        this.loadMoreStatus = 'loadmore';\n      } finally {\n        if (isRefresh) {\n          uni.hideLoading();\n        }\n      }\n    },\n\n    // 加载更多\n    async loadMore() {\n      if (this.loadMoreStatus === 'loading' || !this.hasMore) return;\n\n      this.currentPage++;\n      await this.getActivityList(false);\n    }\n  }\n}\n</script>\n\n<style scoped>\n.events-page {\n  min-height: 100vh;\n  box-sizing: border-box;\n}\n\n.main-content {\n  display: flex;\n  flex-direction: row;\n}\n\n.sidebar-scroll {\n  width: 160rpx;\n  background: #fff;\n  border-radius: 0 24rpx 24rpx 0;\n  box-shadow: 2rpx 0 8rpx rgba(0, 0, 0, 0.03);\n  padding: 0;\n}\n\n.sidebar {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 24rpx 0;\n}\n\n.sidebar-item {\n  width: 100%;\n  padding: 24rpx 0;\n  text-align: center;\n  cursor: pointer;\n  border-left: 8rpx solid transparent;\n  transition: border-color 0.2s;\n}\n\n.sidebar-item.active {\n  background: #f0f7ff;\n  border-left: 8rpx solid #2979ff;\n}\n\n.sidebar-title {\n  font-size: 24rpx;\n  color: #333;\n}\n\n.activity-panel-scroll {\n  flex: 1;\n  background: transparent;\n}\n\n.activity-panel {\n  display: flex;\n  flex-direction: column;\n  min-width: 0;\n}\n\n.search-bar-fix {\n  padding: 24rpx;\n  box-sizing: border-box;\n}\n\n.sort-tabs {\n  margin-bottom: 16rpx;\n  background: #f7f8fa;\n}\n\n.activity-list {\n  flex: 1;\n  padding: 0 24rpx 24rpx 24rpx;\n  box-sizing: border-box;\n}\n\n.activity-card {\n  background: #fff;\n  border-radius: 16rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n}\n\n.activity-card:not(:first-child) {\n  margin-top: 24rpx;\n}\n\n.card-content {\n  padding: 24rpx;\n}\n\n.card-title {\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #303133;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.card-subtitle {\n  font-size: 24rpx;\n  color: #909399;\n  margin-top: 8rpx;\n}\n\n.card-desc {\n  font-size: 26rpx;\n  color: #606266;\n  margin-top: 16rpx;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n}\n\n.card-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 16rpx;\n  padding-top: 16rpx;\n  border-top: 1rpx solid #eee;\n}\n\n.price {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #ff5252;\n}\n\n.nums {\n  font-size: 24rpx;\n  color: #909399;\n}\n</style>", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=338b5f55&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=338b5f55&scoped=true&lang=css&\""], "sourceRoot": ""}