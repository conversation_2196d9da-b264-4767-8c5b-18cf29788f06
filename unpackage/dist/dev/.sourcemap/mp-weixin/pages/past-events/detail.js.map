{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/past-events/detail.vue?36c5", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/past-events/detail.vue?411c", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/past-events/detail.vue?7d28", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/past-events/detail.vue?6f3d", "uni-app:///pages/past-events/detail.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/past-events/detail.vue?8d41"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uPopup", "uIcon", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "_isMounted", "e0", "$event", "showSharePopup", "e1", "recyclableRender", "staticRenderFns", "_withStripped", "data", "lang", "eventId", "eventData", "id", "title", "title_en", "coverImage", "date", "views", "summary", "summary_en", "sections", "content", "content_en", "image", "caption", "caption_en", "conclusion", "conclusion_en", "onLoad", "uni", "onUnload", "methods", "setNavigationBarTitle", "switchLanguage", "getApp", "shareEvent", "handleShare", "icon", "success", "navigateToEvents", "url", "fetchEventDetail"], "mappings": "mJAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,+BACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,OAAQ,WACN,OAAO,oHAITC,MAAO,WACL,OAAO,mHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,GACdL,EAAIM,aACPN,EAAIO,GAAK,SAAUC,GACjBR,EAAIS,gBAAiB,GAEvBT,EAAIU,GAAK,SAAUF,GACjBR,EAAIS,gBAAiB,KAIvBE,GAAmB,EACnBC,EAAkB,GACtBb,EAAOc,eAAgB,G,iCC9CvB,yHAAi4B,eAAG,G,sHCiGp4B,CACAC,gBACA,OACAC,UACAC,aACAP,kBACAQ,WACAC,KACAC,oBACAC,uCACAC,oLACAC,kBACAC,WACAC,yEACAC,uNACAC,UACA,CACAP,aACAC,4BACAO,sFACAC,yPAEA,CACAC,+KACAC,uBACAC,qDAEA,CACAZ,aACAC,+BACAO,+FACAC,gUAEA,CACAC,+KACAC,qBACAC,2DAEA,CACAZ,aACAC,kCACAO,sFACAC,mWAGAI,8HACAC,ibAIAC,mBAAA,WAEA,2BACA,oCAIAC,qCACA,cACA,6BAIA,OACA,mBAMA,6BAGA,wBAEAC,oBAEAD,2BAEAE,SACAC,iCACAH,yBACAhB,gDAGAoB,0BACA,iCACA,YAGA,sBACAC,4BAIAL,2BAAApB,SAGA,8BAEA0B,sBACA,wBAEAC,wBAAA,WAEAP,aACAhB,oDACAwB,iBAEA,uBAGA,YACAR,oBACArB,+DACA8B,mBACAT,aACAhB,0CACAwB,qBAMAE,4BACAV,cACAW,6BAGAC,iCAKA,c,6DCvOA,yHAA4pD,eAAG,G", "file": "pages/past-events/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/past-events/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=bd2bec76&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=bd2bec76&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"bd2bec76\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/past-events/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=bd2bec76&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showSharePopup = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showSharePopup = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"past-event-detail\">\n    <!-- 顶部图片 -->\n    <image class=\"cover-image\" :src=\"eventData.coverImage\" mode=\"widthFix\"></image>\n    \n    <!-- 标题区域 -->\n    <view class=\"header\">\n      <view class=\"title\">{{ lang === 'zh' ? eventData.title : eventData.title_en }}</view>\n      <view class=\"meta\">\n        <text class=\"date\">{{ eventData.date }}</text>\n        <text class=\"views\">{{ eventData.views }}{{ lang === 'zh' ? '次浏览' : ' views' }}</text>\n      </view>\n    </view>\n    \n    <!-- 内容区域 -->\n    <view class=\"content\">\n      <!-- 摘要 -->\n      <view class=\"summary\" v-if=\"eventData.summary\">\n        <text>{{ lang === 'zh' ? eventData.summary : eventData.summary_en }}</text>\n      </view>\n      \n      <!-- 正文内容 -->\n      <block v-for=\"(section, index) in eventData.sections\" :key=\"index\">\n        <!-- 段落标题 -->\n        <view class=\"section-title\" v-if=\"section.title\">\n          {{ lang === 'zh' ? section.title : section.title_en }}\n        </view>\n        \n        <!-- 段落文本 -->\n        <view class=\"paragraph\" v-if=\"section.content\">\n          <text>{{ lang === 'zh' ? section.content : section.content_en }}</text>\n        </view>\n        \n        <!-- 段落图片 -->\n        <view class=\"section-image\" v-if=\"section.image\">\n          <image :src=\"section.image\" mode=\"widthFix\"></image>\n          <view class=\"image-caption\" v-if=\"section.caption\">\n            {{ lang === 'zh' ? section.caption : section.caption_en }}\n          </view>\n        </view>\n      </block>\n      \n      <!-- 活动总结 -->\n      <view class=\"conclusion\" v-if=\"eventData.conclusion\">\n        <view class=\"conclusion-title\">\n          {{ lang === 'zh' ? '活动总结' : 'Event Summary' }}\n        </view>\n        <view class=\"conclusion-content\">\n          {{ lang === 'zh' ? eventData.conclusion : eventData.conclusion_en }}\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部分享和参与 -->\n    <!-- <view class=\"footer\">\n      <view class=\"share-btn\" @click=\"shareEvent\">\n        <u-icon name=\"share\" size=\"40rpx\" color=\"#2979ff\"></u-icon>\n        <text>{{ lang === 'zh' ? '分享' : 'Share' }}</text>\n      </view>\n      <view class=\"join-btn\" @click=\"navigateToEvents\">\n        <text>{{ lang === 'zh' ? '查看更多活动' : 'More Events' }}</text>\n        <u-icon name=\"arrow-right\" size=\"32rpx\" color=\"#ffffff\"></u-icon>\n      </view>\n    </view> -->\n    \n    <!-- 语言切换按钮 -->\n    <view class=\"lang-switch\" @click=\"switchLanguage\">\n      <text>{{ lang === 'zh' ? 'EN' : '中' }}</text>\n    </view>\n    \n    <!-- 分享弹窗 -->\n    <u-popup :show=\"showSharePopup\" mode=\"bottom\" @close=\"showSharePopup = false\">\n      <view class=\"share-popup\">\n        <view class=\"share-title\">{{ lang === 'zh' ? '分享到' : 'Share to' }}</view>\n        <view class=\"share-options\">\n          <view class=\"share-option\" @click=\"handleShare('wechat')\">\n            <u-icon name=\"weixin-fill\" size=\"60rpx\" color=\"#09BB07\"></u-icon>\n            <text>{{ lang === 'zh' ? '微信' : 'WeChat' }}</text>\n          </view>\n          <view class=\"share-option\" @click=\"handleShare('moments')\">\n            <u-icon name=\"moment\" size=\"60rpx\" color=\"#09BB07\"></u-icon>\n            <text>{{ lang === 'zh' ? '朋友圈' : 'Moments' }}</text>\n          </view>\n          <view class=\"share-option\" @click=\"handleShare('link')\">\n            <u-icon name=\"link\" size=\"60rpx\" color=\"#2979ff\"></u-icon>\n            <text>{{ lang === 'zh' ? '复制链接' : 'Copy Link' }}</text>\n          </view>\n        </view>\n        <view class=\"cancel-btn\" @click=\"showSharePopup = false\">\n          {{ lang === 'zh' ? '取消' : 'Cancel' }}\n        </view>\n      </view>\n    </u-popup>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      lang: 'zh',\n      eventId: null,\n      showSharePopup: false,\n      eventData: {\n        id: 1,\n        title: '2023夏令营精彩回顾',\n        title_en: '2023 Summer Camp Highlights',\n        coverImage: 'https://images.unsplash.com/photo-1530124566582-a618bc2615dc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',\n        date: '2023-09-01',\n        views: 1286,\n        summary: '2023年夏令营圆满结束，本次活动共有120名学员参与，通过丰富多彩的课程和活动，学员们不仅学到了知识，还收获了友谊和成长。',\n        summary_en: 'The 2023 Summer Camp has successfully concluded with 120 students participating. Through a variety of courses and activities, students not only gained knowledge but also friendship and personal growth.',\n        sections: [\n          {\n            title: '活动亮点',\n            title_en: 'Event Highlights',\n            content: '本次夏令营为期两周，涵盖了编程、艺术、体育和科学探索等多个领域的课程。学员们在专业教师的指导下，完成了多个实践项目，展示了自己的创造力和团队协作能力。',\n            content_en: 'This two-week summer camp covered courses in programming, arts, sports, and scientific exploration. Under the guidance of professional teachers, students completed multiple practical projects, showcasing their creativity and teamwork.'\n          },\n          {\n            image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',\n            caption: '学员们在编程课上专注学习',\n            caption_en: 'Students focusing on programming class'\n          },\n          {\n            title: '编程课程',\n            title_en: 'Programming Courses',\n            content: '编程课程是本次夏令营的重点之一。学员们从基础的编程概念开始学习，逐步掌握了算法思维和问题解决能力。在课程结束时，每位学员都能独立完成一个小型项目，如简单游戏或网页应用。',\n            content_en: 'Programming was one of the highlights of this summer camp. Students started with basic programming concepts and gradually mastered algorithmic thinking and problem-solving skills. By the end of the course, each student could independently complete a small project such as a simple game or web application.'\n          },\n          {\n            image: 'https://images.unsplash.com/photo-1517164850305-99a3e65bb47e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',\n            caption: '团队合作完成科学实验',\n            caption_en: 'Team collaboration on scientific experiments'\n          },\n          {\n            title: '科学探索',\n            title_en: 'Scientific Exploration',\n            content: '科学探索课程让学员们亲身体验了科学实验的乐趣。从物理现象到化学反应，从生物观察到天文知识，学员们在动手实践中理解了科学原理，培养了科学思维和探索精神。',\n            content_en: 'The scientific exploration courses allowed students to experience the joy of scientific experiments firsthand. From physical phenomena to chemical reactions, from biological observations to astronomical knowledge, students understood scientific principles through hands-on practice and developed scientific thinking and exploratory spirit.'\n          }\n        ],\n        conclusion: '本次夏令营不仅丰富了学员们的暑假生活，也为他们提供了一个展示才能、发掘潜力的平台。我们看到了每位学员的成长和进步，也期待在未来的活动中再次相见。感谢所有参与和支持本次活动的家长、老师和工作人员，让我们共同为孩子们的成长助力！',\n        conclusion_en: 'This summer camp not only enriched the students\\' summer vacation but also provided them with a platform to showcase their talents and discover their potential. We witnessed the growth and progress of each student and look forward to meeting them again in future activities. Thanks to all parents, teachers, and staff who participated in and supported this event, let\\'s continue to help our children grow together!'\n      }\n    };\n  },\n  onLoad(options) {\n    // 从全局获取语言设置\n    if (getApp().globalData.lang) {\n      this.lang = getApp().globalData.lang;\n    }\n    \n    // 监听语言变化\n    uni.$on('languageChanged', (data) => {\n      this.lang = data.lang;\n      this.setNavigationBarTitle();\n    });\n    \n    // 获取活动ID\n    if (options.id) {\n      this.eventId = options.id;\n      // 实际应用中，这里应该通过API获取活动详情\n      // this.fetchEventDetail(this.eventId);\n    }\n    \n    // 设置导航栏标题\n    this.setNavigationBarTitle();\n    \n    // 模拟增加浏览量\n    this.eventData.views++;\n  },\n  onUnload() {\n    // 取消监听语言变化\n    uni.$off('languageChanged');\n  },\n  methods: {\n    setNavigationBarTitle() {\n      uni.setNavigationBarTitle({\n        title: this.lang === 'zh' ? '活动回顾' : 'Event Review'\n      });\n    },\n    switchLanguage() {\n      const newLang = this.lang === 'zh' ? 'en' : 'zh';\n      this.lang = newLang;\n      \n      // 更新全局语言设置\n      if (getApp().globalData) {\n        getApp().globalData.lang = newLang;\n      }\n      \n      // 发送语言变化事件\n      uni.$emit('languageChanged', { lang: newLang });\n      \n      // 设置导航栏标题\n      this.setNavigationBarTitle();\n    },\n    shareEvent() {\n      this.showSharePopup = true;\n    },\n    handleShare(platform) {\n      // 模拟分享功能\n      uni.showToast({\n        title: this.lang === 'zh' ? '分享成功' : 'Shared successfully',\n        icon: 'success'\n      });\n      this.showSharePopup = false;\n      \n      // 实际应用中，这里应该调用相应的分享API\n      if (platform === 'link') {\n        uni.setClipboardData({\n          data: 'https://example.com/past-events/detail?id=' + this.eventId,\n          success: () => {\n            uni.showToast({\n              title: this.lang === 'zh' ? '链接已复制' : 'Link copied',\n              icon: 'success'\n            });\n          }\n        });\n      }\n    },\n    navigateToEvents() {\n      uni.navigateTo({\n        url: '/pages/events/index'\n      });\n    },\n    fetchEventDetail(id) {\n      // 实际应用中，这里应该通过API获取活动详情\n      // 这里仅作为示例，使用静态数据\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.past-event-detail {\n  padding-bottom: 40rpx;\n  background-color: #ffffff;\n}\n\n.cover-image {\n  width: 100%;\n  height: auto;\n}\n\n.header {\n  padding: 30rpx;\n  border-bottom: 1rpx solid #f2f2f2;\n}\n\n.title {\n  font-size: 40rpx;\n  font-weight: bold;\n  color: #303133;\n  line-height: 1.4;\n  margin-bottom: 20rpx;\n}\n\n.meta {\n  display: flex;\n  align-items: center;\n  font-size: 24rpx;\n  color: #909399;\n}\n\n.date {\n  margin-right: 20rpx;\n}\n\n.content {\n  padding: 30rpx;\n}\n\n.summary {\n  padding: 20rpx;\n  background-color: #f8f8f8;\n  border-left: 8rpx solid #2979ff;\n  margin-bottom: 30rpx;\n  font-size: 28rpx;\n  color: #606266;\n  line-height: 1.6;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #303133;\n  margin: 30rpx 0 20rpx;\n  position: relative;\n  padding-left: 20rpx;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    left: 0;\n    top: 8rpx;\n    bottom: 8rpx;\n    width: 8rpx;\n    background-color: #2979ff;\n    border-radius: 4rpx;\n  }\n}\n\n.paragraph {\n  font-size: 28rpx;\n  color: #606266;\n  line-height: 1.8;\n  margin-bottom: 30rpx;\n  text-align: justify;\n}\n\n.section-image {\n  margin: 20rpx 0 30rpx;\n  \n  image {\n    width: 100%;\n    height: auto;\n    border-radius: 8rpx;\n  }\n}\n\n.image-caption {\n  font-size: 24rpx;\n  color: #909399;\n  text-align: center;\n  margin-top: 10rpx;\n}\n\n.conclusion {\n  margin-top: 40rpx;\n  padding: 30rpx;\n  background-color: #f8f8f8;\n  border-radius: 8rpx;\n}\n\n.conclusion-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 20rpx;\n}\n\n.conclusion-content {\n  font-size: 28rpx;\n  color: #606266;\n  line-height: 1.8;\n  text-align: justify;\n}\n\n.footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 30rpx;\n  margin-top: 20rpx;\n  border-top: 1rpx solid #f2f2f2;\n}\n\n.share-btn {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  \n  text {\n    font-size: 24rpx;\n    color: #606266;\n    margin-top: 8rpx;\n  }\n}\n\n.join-btn {\n  display: flex;\n  align-items: center;\n  background-color: #2979ff;\n  color: #ffffff;\n  padding: 20rpx 30rpx;\n  border-radius: 40rpx;\n  font-size: 28rpx;\n  \n  text {\n    margin-right: 10rpx;\n  }\n}\n\n.lang-switch {\n  position: fixed;\n  right: 30rpx;\n  top: 30rpx;\n  width: 80rpx;\n  height: 80rpx;\n  background-color: rgba(0, 0, 0, 0.5);\n  color: #ffffff;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28rpx;\n  z-index: 10;\n}\n\n.share-popup {\n  padding: 30rpx;\n}\n\n.share-title {\n  text-align: center;\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 30rpx;\n}\n\n.share-options {\n  display: flex;\n  justify-content: space-around;\n  padding: 20rpx 0 40rpx;\n}\n\n.share-option {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  \n  text {\n    font-size: 24rpx;\n    color: #606266;\n    margin-top: 16rpx;\n  }\n}\n\n.cancel-btn {\n  height: 90rpx;\n  line-height: 90rpx;\n  text-align: center;\n  font-size: 30rpx;\n  color: #303133;\n  border-top: 1rpx solid #f2f2f2;\n}\n</style> ", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=bd2bec76&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=bd2bec76&lang=scss&scoped=true&\""], "sourceRoot": ""}