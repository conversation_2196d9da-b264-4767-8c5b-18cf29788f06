{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/my-join/index.vue?3a5c", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/my-join/index.vue?4bb3", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/my-join/index.vue?fa75", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/my-join/index.vue?c772", "uni-app:///pages/my-join/index.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/pages/my-join/index.vue?4058"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uSearch", "uImage", "uTag", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "l0", "_self", "_c", "__map", "filteredActivityList", "item", "__i0__", "$orig", "__get_orig", "m0", "statusText", "status", "m1", "statusType", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "CustomTabbar", "LangSwitch", "lang", "searchValue", "showPopup", "activityList", "id", "title", "time", "img", "desc", "computed", "mounted", "uni", "<PERSON><PERSON><PERSON><PERSON>", "methods", "not_started", "in_progress", "ended", "answered", "onSearch", "icon", "onCategoryClick", "name_en", "onActivityClick", "url", "onLangChange", "setNavTitle"], "mappings": "8IAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,0BACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,QAAS,WACP,OAAO,sHAITC,OAAQ,WACN,OAAO,oHAITC,KAAM,WACJ,OAAO,iHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,MAAMP,EAAIQ,sBAAsB,SAAUC,EAAMC,GAC3D,IAAIC,EAAQX,EAAIY,WAAWH,GACvBI,EAAKb,EAAIc,WAAWL,EAAKM,QACzBC,EAAKhB,EAAIiB,WAAWR,EAAKM,QAC7B,MAAO,CACLJ,MAAOA,EACPE,GAAIA,EACJG,GAAIA,OAGRhB,EAAIkB,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLlB,GAAIA,MAKRmB,GAAmB,EACnBC,EAAkB,GACtBzB,EAAO0B,eAAgB,G,iCC7DvB,yHAAg4B,eAAG,G,kVCiCn4B,CACAnC,YACAoC,eACAC,cAEAR,gBACA,OACAS,oCACAC,eACAC,aACAC,eACAC,KACAC,cACAC,kBACAC,6CACAC,sBACArB,sBAEA,CACAiB,KACAC,cACAC,kBACAC,6CACAC,uBACArB,mBAEA,CACAiB,KACAC,gBACAC,kBACAC,6CACAC,0BACArB,sBAEA,CACAiB,KACAC,iBACAC,kBACAC,6CACAC,yBACArB,mBAKAsB,UACA7B,gCAEA,qBACA,yBAIA,qCACA,oDACAC,mCACAA,sCAIA6B,mBACAC,uCACA,oBAEAC,yBACAD,yCAEAE,SACA3B,uBACA,OACA4B,iDACAC,iDACAC,qCACAC,4CAEA,gBACA5B,uBACA,OACAyB,mBACAC,sBACAC,gBACAC,oBAEA,wBAEAC,qBAEA,mBAIA,sCACAP,aACAN,oCACA,yDACA,gDACAc,cAEA,yCACAR,aACAN,2DACAc,eAIAC,4BAEAT,aACAN,+EACAgB,SACAF,eAGAG,4BAEA,yBAEAX,cACAY,8CAEA,sBAEAZ,cACAY,8CAIAZ,cACAY,8CAIAC,yBACA,YACA,oBAGAC,uBACA,0CACAd,yBACAN,aAIA,c,6DCjLA,yHAAuxC,eAAG,G", "file": "pages/my-join/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my-join/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=66a8d3c5&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=66a8d3c5&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"66a8d3c5\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my-join/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=66a8d3c5&scoped=true&\"", "var components\ntry {\n  components = {\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-search/u-search\" */ \"uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    uImage: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-image/u-image\" */ \"uview-ui/components/u-image/u-image.vue\"\n      )\n    },\n    uTag: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-tag/u-tag\" */ \"uview-ui/components/u-tag/u-tag.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.filteredActivityList, function (item, __i0__) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.statusText(item.status)\n    var m1 = _vm.statusType(item.status)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n    <view class=\"index-page\">\n        <!-- 搜索栏 -->\n        <view class=\"search-bar\">\n            <u-search :placeholder=\"lang === 'zh' ? '搜索我参与的活动' : 'Search My Joined Events'\" v-model=\"searchValue\"\n                @search=\"onSearch\" :show-action=\"false\" />\n        </view>\n\n        <view class=\"activity-list\">\n            <view v-for=\"item in filteredActivityList\" :key=\"item.id\" class=\"activity-card\" @click=\"onActivityClick(item)\">\n                <view class=\"card-img-wrap\">\n                    <view style=\"width: 100%;\"><u-image :src=\"item.img\" width=\"100%\" height=\"250rpx\"\n                            border-radius=\"16rpx 16rpx 0 0\" /></view>\n                    <view class=\"status-tag\">\n                        <u-tag :text=\"statusText(item.status)\" :type=\"statusType(item.status)\" size=\"mini\" />\n                    </view>\n                </view>\n                <view class=\"card-content\">\n                    <view class=\"card-title\">{{ item.title }}</view>\n                    <view class=\"card-subtitle\">{{ item.time }}</view>\n                    <view class=\"card-desc\">{{ item.desc }}</view>\n                </view>\n            </view>\n        </view>\n\n        <CustomTabbar :current=\"2\" />\n        <LangSwitch />\n    </view>\n</template>\n\n<script>\nimport CustomTabbar from '../../components/CustomTabbar.vue'\nimport LangSwitch from '../../components/LangSwitch.vue'\nexport default {\n    components: {\n        CustomTabbar,\n        LangSwitch\n    },\n    data() {\n        return {\n            lang: uni.getStorageSync('lang') || 'zh',\n            searchValue: '',\n            showPopup: true,\n            activityList: [{\n                id: 1,\n                title: '篮球训练营',\n                time: '2024-07-10',\n                img: 'https://picsum.photos/800/600?random=4',\n                desc: '专业教练指导，提升篮球技能。',\n                status: 'not_started'\n            },\n            {\n                id: 2,\n                title: '少儿美术班',\n                time: '2024-07-15',\n                img: 'https://picsum.photos/800/600?random=5',\n                desc: '培养孩子艺术素养，激发创造力。',\n                status: 'answered'\n            },\n            {\n                id: 3,\n                title: '青少年编程课程',\n                time: '2024-07-05',\n                img: 'https://picsum.photos/800/600?random=6',\n                desc: '学习基础编程知识，培养逻辑思维能力。',\n                status: 'in_progress'\n            },\n            {\n                id: 4,\n                title: '儿童英语口语训练',\n                time: '2024-06-28',\n                img: 'https://picsum.photos/800/600?random=7',\n                desc: '外教一对一，提高英语口语表达能力。',\n                status: 'ended'\n            }\n            ]\n        }\n    },\n    computed: {\n        filteredActivityList() {\n            // 如果没有搜索值，返回全部活动\n            if (!this.searchValue) {\n                return this.activityList;\n            }\n            \n            // 搜索过滤\n            const val = this.searchValue.toLowerCase();\n            return this.activityList.filter(item => \n                item.title.toLowerCase().includes(val) || \n                item.desc.toLowerCase().includes(val)\n            );\n        }\n    },\n    mounted() {\n        uni.$on('lang-change', this.onLangChange)\n        this.setNavTitle() // 新增：页面加载时设置标题\n    },\n    beforeDestroy() {\n        uni.$off('lang-change', this.onLangChange)\n    },\n    methods: {\n        statusText(status) {\n            const map = {\n                not_started: this.lang === 'zh' ? '未开始' : 'Not Started',\n                in_progress: this.lang === 'zh' ? '进行中' : 'In Progress',\n                ended: this.lang === 'zh' ? '已结束' : 'Ended',\n                answered: this.lang === 'zh' ? '已答题' : 'Answered'\n            }\n            return map[status] || status\n        }, statusType(status) {\n            const map = {\n                not_started: 'info',\n                in_progress: 'primary',\n                ended: 'warning',\n                answered: 'success'\n            }\n            return map[status] || 'default'\n        },\n        onSearch(val) {\n            // 更新搜索值\n            this.searchValue = val;\n            \n            // 通过计算属性自动过滤\n            // 如果搜索内容不为空且有结果，显示提示\n            if (val && this.filteredActivityList.length > 0) {\n                uni.showToast({\n                    title: this.lang === 'zh' ? \n                        `找到 ${this.filteredActivityList.length} 个活动` : \n                        `Found ${this.filteredActivityList.length} activities`,\n                    icon: 'none'\n                });\n            } else if (val && this.filteredActivityList.length === 0) {\n                uni.showToast({\n                    title: this.lang === 'zh' ? '没有找到相关活动' : 'No matching activities',\n                    icon: 'none'\n                });\n            }\n        },\n        onCategoryClick(idx) {\n            // 分类点击逻辑\n            uni.showToast({\n                title: '点击分类：' + (this.lang === 'zh' ? this.categoryList[idx].name : this.categoryList[idx]\n                    .name_en),\n                icon: 'none'\n            })\n        },\n        onActivityClick(item) {\n            // 根据活动状态决定跳转\n            if (item.status === 'not_started') {\n                // 未开始的活动，跳转到活动详情页\n                uni.navigateTo({\n                    url: `/pages/events/detail?id=${item.id}`\n                });\n            } else if (item.status === 'answered') {\n                // 已答题的活动，跳转到答题结果反馈页面\n                uni.navigateTo({\n                    url: `/pages/survey/result?id=${item.id}`\n                });\n            } else {\n                // 其他状态，跳转到调查问卷页面\n                uni.navigateTo({\n                    url: `/pages/survey/index?id=${item.id}`\n                });\n            }\n        },\n        onLangChange(newLang) {\n            this.lang = newLang\n            this.setNavTitle() // 新增：语言切换时设置标题\n        },\n        // 新增：设置导航栏标题的方法\n        setNavTitle() {\n            const title = this.lang === 'zh' ? '我参与的' : 'My Joined'\n            uni.setNavigationBarTitle({\n                title: title\n            })\n        }\n    }\n}\n</script>\n\n<style scoped>\n.card-img-wrap {\n    position: relative;\n    display: flex;\n    align-items: flex-start;\n    justify-content: flex-end;\n    width: 100%;\n}\n\n.status-tag {\n    position: absolute;\n    top: 16rpx;\n    right: 16rpx;\n    z-index: 0;\n}\n\n.poster-popup {\n    /* 消除 u-popup 内部的默认 padding */\n    line-height: 1;\n}\n\n.search-bar {\n    margin: 24rpx;\n    margin-top: 0rpx;\n    padding-top: 24rpx;\n    box-sizing: border-box;\n}\n\n.index-page {\n    min-height: 100vh;\n    padding-bottom: 0;\n}\n\n.banner-swiper {\n    margin: 24rpx;\n}\n\n.category-grid {\n    margin-top: 24rpx;\n}\n\n.cat-title {\n    font-size: 24rpx;\n    margin-top: 8rpx;\n    color: #333;\n}\n\n.section-title {\n    font-size: 32rpx;\n    font-weight: bold;\n    margin: 32rpx 24rpx 16rpx 24rpx;\n    color: #222;\n}\n\n.activity-list {\n    padding: 0 24rpx;\n}\n\n.activity-card {\n    margin-bottom: 24rpx;\n    background: #fff;\n    border-radius: 16rpx;\n    width: 100%;\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n    overflow: hidden;\n}\n\n.card-content {\n    padding: 24rpx;\n}\n\n.card-title {\n    font-size: 30rpx;\n    font-weight: bold;\n    color: #303133;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n}\n\n.card-subtitle {\n    font-size: 24rpx;\n    color: #909399;\n    margin-top: 8rpx;\n}\n\n.card-desc {\n    font-size: 26rpx;\n    color: #606266;\n    margin-top: 16rpx;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 2;\n    overflow: hidden;\n}\n\n.popup-content {\n    padding: 48rpx 32rpx;\n    text-align: center;\n}\n</style>", "import mod from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=66a8d3c5&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=66a8d3c5&scoped=true&lang=css&\""], "sourceRoot": ""}