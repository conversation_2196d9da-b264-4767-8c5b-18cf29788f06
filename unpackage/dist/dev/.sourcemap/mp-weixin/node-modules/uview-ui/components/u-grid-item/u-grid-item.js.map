{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/node_modules/uview-ui/components/u-grid-item/u-grid-item.vue?4956", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/node_modules/uview-ui/components/u-grid-item/u-grid-item.vue?aa48", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/node_modules/uview-ui/components/u-grid-item/u-grid-item.vue?cfcb", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/node_modules/uview-ui/components/u-grid-item/u-grid-item.vue?4da0", "uni-app:///node_modules/uview-ui/components/u-grid-item/u-grid-item.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/node_modules/uview-ui/components/u-grid-item/u-grid-item.vue?a4cb"], "names": ["renderjs", "component", "options", "__file", "components", "render", "_vm", "this", "_h", "$createElement", "s0", "_self", "_c", "__get_style", "itemStyle", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "name", "mixins", "parentData", "col", "border", "classes", "mounted", "computed", "width", "background", "methods", "init", "uni", "updateParentData", "clickHandler", "getItemWidth", "parentWidth", "getParent<PERSON>idth", "gridItemClasses", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "yJAAA,oIACIA,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,+DACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,YAAY,CAACP,EAAIQ,aAC9BR,EAAIS,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLT,GAAIA,MAKRU,GAAmB,EACnBC,EAAkB,GACtBhB,EAAOiB,eAAgB,G,iCCjBvB,yHAAo6B,eAAG,G,qJC2Bv6B,YACA,EAUA,CACAC,mBACAC,2CACAR,gBACA,OACAS,YACAC,MACAC,WAKAC,aAGAC,mBACA,aAEAC,UAGAC,iBACA,4CAGAjB,qBACA,OACAkB,wBACAD,kBAEA,2DAGAE,SACAC,gBAAA,WAGAC,+BACA,uBAGA,wBAQAA,sBACA,wBAGAC,4BAEA,8BAEAC,wBAAA,aACA,YAEA,uDACA,sBACAd,2BAAA,iBAGA,uCACA,uBAEAe,wBAAA,4IAEA,GAAAP,KACA,yCAEA,0BAAAQ,SACAR,kCAAA,OAEA,oDARA,IAWAS,4BAYAC,2BAAA,WACA,2BACA,SACA,wCACA,UACA,gCAEA,oCACAb,yBAIA,iEAEA,OACAA,8BAQA,kBAIAc,yBAEAP,uBAEA,c,6DChKA,yHAAutD,eAAG,G", "file": "node-modules/uview-ui/components/u-grid-item/u-grid-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-grid-item.vue?vue&type=template&id=99a45d26&scoped=true&\"\nvar renderjs\nimport script from \"./u-grid-item.vue?vue&type=script&lang=js&\"\nexport * from \"./u-grid-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-grid-item.vue?vue&type=style&index=0&id=99a45d26&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"99a45d26\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-grid-item/u-grid-item.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-grid-item.vue?vue&type=template&id=99a45d26&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.itemStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-grid-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-grid-item.vue?vue&type=script&lang=js&\"", "<template>\n\t<!-- #ifndef APP-NVUE -->\n\t<view\n\t    class=\"u-grid-item\"\n\t    hover-class=\"u-grid-item--hover-class\"\n\t    :hover-stay-time=\"200\"\n\t    @tap=\"clickHandler\"\n\t    :class=\"classes\"\n\t    :style=\"[itemStyle]\"\n\t>\n\t\t<slot />\n\t</view>\n\t<!-- #endif -->\n\t<!-- #ifdef APP-NVUE -->\n\t<view\n\t    class=\"u-grid-item\"\n\t    :hover-stay-time=\"200\"\n\t    @tap=\"clickHandler\"\n\t    :class=\"classes\"\n\t    :style=\"[itemStyle]\"\n\t>\n\t\t<slot />\n\t</view>\n\t<!-- #endif -->\n</template>\n\n<script>\n\timport props from './props.js';\n\t/**\n\t * gridItem 提示\n\t * @description 宫格组件一般用于同时展示多个同类项目的场景，可以给宫格的项目设置徽标组件(badge)，或者图标等，也可以扩展为左右滑动的轮播形式。搭配u-grid使用\n\t * @tutorial https://www.uviewui.com/components/grid.html\n\t * @property {String | Number}\tname\t\t宫格的name ( 默认 null )\n\t * @property {String}\t\t\tbgColor\t\t宫格的背景颜色 （默认 'transparent' ）\n\t * @property {Object}\t\t\tcustomStyle\t自定义样式，对象形式\n\t * @event {Function} click 点击宫格触发\n\t * @example <u-grid-item></u-grid-item>\n\t */\n\texport default {\n\t\tname: \"u-grid-item\",\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tparentData: {\n\t\t\t\t\tcol: 3, // 父组件划分的宫格数\n\t\t\t\t\tborder: true, // 是否显示边框，根据父组件决定\n\t\t\t\t},\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\twidth: 0, // nvue下才这么计算，vue下放到computed中，否则会因为延时造成闪烁\n\t\t\t\t// #endif\n\t\t\t\tclasses: [], // 类名集合，用于判断是否显示右边和下边框\n\t\t\t};\n\t\t},\n\t\tmounted() {\n\t\t\tthis.init()\n\t\t},\n\t\tcomputed: {\n\t\t\t// #ifndef APP-NVUE\n\t\t\t// vue下放到computed中，否则会因为延时造成闪烁\n\t\t\twidth() {\n\t\t\t\treturn 100 / Number(this.parentData.col) + '%'\n\t\t\t},\n\t\t\t// #endif\n\t\t\titemStyle() {\n\t\t\t\tconst style = {\n\t\t\t\t\tbackground: this.bgColor,\n\t\t\t\t\twidth: this.width\n\t\t\t\t}\n\t\t\t\treturn uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle))\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\t// 用于在父组件u-grid的children中被添加入子组件时，\n\t\t\t\t// 重新计算item的边框\n\t\t\t\tuni.$on('$uGridItem', () => {\n\t\t\t\t\tthis.gridItemClasses()\n\t\t\t\t})\n\t\t\t\t// 父组件的实例\n\t\t\t\tthis.updateParentData()\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// 获取元素该有的长度，nvue下要延时才准确\n\t\t\t\tthis.$nextTick(function(){\n\t\t\t\t\tthis.getItemWidth()\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t\t// 发出事件，通知所有的grid-item都重新计算自己的边框\n\t\t\t\tuni.$emit('$uGridItem')\n\t\t\t\tthis.gridItemClasses()\n\t\t\t},\n\t\t\t// 获取父组件的参数\n\t\t\tupdateParentData() {\n\t\t\t\t// 此方法写在mixin中\n\t\t\t\tthis.getParentData('u-grid');\n\t\t\t},\n\t\t\tclickHandler() {\n\t\t\t\tlet name = this.name\n\t\t\t\t// 如果没有设置name属性，历遍父组件的children数组，判断当前的元素是否和本实例this相等，找出当前组件的索引\n\t\t\t\tconst children = this.parent?.children\n\t\t\t\tif(children && this.name === null) {\n\t\t\t\t\tname = children.findIndex(child => child === this)\n\t\t\t\t}\n\t\t\t\t// 调用父组件方法，发出事件\n\t\t\t\tthis.parent && this.parent.childClick(name)\n\t\t\t\tthis.$emit('click', name)\n\t\t\t},\n\t\t\tasync getItemWidth() {\n\t\t\t\t// 如果是nvue，不能使用百分比，只能使用固定宽度\n\t\t\t\tlet width = 0\n\t\t\t\tif(this.parent) {\n\t\t\t\t\t// 获取父组件宽度后，除以栅格数，得出每个item的宽度\n\t\t\t\t\tconst parentWidth = await this.getParentWidth()\n\t\t\t\t\twidth = parentWidth / Number(this.parentData.col) + 'px'\n\t\t\t\t}\n\t\t\t\tthis.width = width\n\t\t\t},\n\t\t\t// 获取父元素的尺寸\n\t\t\tgetParentWidth() {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// 返回一个promise，让调用者可以用await同步获取\n\t\t\t\tconst dom = uni.requireNativePlugin('dom')\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\t// 调用父组件的ref\n\t\t\t\t\tdom.getComponentRect(this.parent.$refs['u-grid'], res => {\n\t\t\t\t\t\tresolve(res.size.width)\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tgridItemClasses() {\n\t\t\t\tif(this.parentData.border) {\n\t\t\t\t\tconst classes = []\n\t\t\t\t\tthis.parent.children.map((child, index) =>{\n\t\t\t\t\t\tif(this === child) {\n\t\t\t\t\t\t\tconst len = this.parent.children.length\n\t\t\t\t\t\t\t// 贴近右边屏幕边沿的child，并且最后一个（比如只有横向2个的时候），无需右边框\n\t\t\t\t\t\t\tif((index + 1) % this.parentData.col !== 0 && index + 1 !== len) {\n\t\t\t\t\t\t\t\tclasses.push('u-border-right')\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// 总的宫格数量对列数取余的值\n\t\t\t\t\t\t\t// 如果取余后，值为0，则意味着要将最后一排的宫格，都不需要下边框\n\t\t\t\t\t\t\tconst lessNum = len % this.parentData.col === 0 ? this.parentData.col : len % this.parentData.col\n\t\t\t\t\t\t\t// 最下面的一排child，无需下边框\n\t\t\t\t\t\t\tif(index < len - lessNum) {\n\t\t\t\t\t\t\t\tclasses.push('u-border-bottom')\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t// 支付宝，头条小程序无法动态绑定一个数组类名，否则解析出来的结果会带有\",\"，而导致失效\n\t\t\t\t\t// #ifdef MP-ALIPAY || MP-TOUTIAO\n\t\t\t\t\tclasses = classes.join(' ')\n\t\t\t\t\t// #endif\n\t\t\t\t\tthis.classes = classes\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tbeforeDestroy() {\n\t\t\t// 移除事件监听，释放性能\n\t\t\tuni.$off('$uGridItem')\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n      $u-grid-item-hover-class-opcatiy:.5 !default;\n      $u-grid-item-margin-top:1rpx !default;\n      $u-grid-item-border-right-width:0.5px !default;\n      $u-grid-item-border-bottom-width:0.5px !default;\n      $u-grid-item-border-right-color:$u-border-color !default;\n      $u-grid-item-border-bottom-color:$u-border-color !default;\n\t.u-grid-item {\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t\t/* #ifndef APP-NVUE */\n\t\tbox-sizing: border-box;\n\t\tdisplay: flex;\n\t\t/* #endif */\n\n\t\t/* #ifdef MP */\n\t\tposition: relative;\n\t\tfloat: left;\n\t\t/* #endif */\n\n\t\t/* #ifdef MP-WEIXIN */\n\t\tmargin-top:$u-grid-item-margin-top;\n\t\t/* #endif */\n\n\t\t&--hover-class {\n\t\t\topacity:$u-grid-item-hover-class-opcatiy;\n\t\t}\n\t}\n\n\t/* #ifdef APP-NVUE */\n\t// 由于nvue不支持组件内引入app.vue中再引入的样式，所以需要写在这里\n\t.u-border-right {\n\t\tborder-right-width:$u-grid-item-border-right-width;\n\t\tborder-color: $u-grid-item-border-right-color;\n\t}\n\n\t.u-border-bottom {\n\t\tborder-bottom-width:$u-grid-item-border-bottom-width;\n\t\tborder-color:$u-grid-item-border-bottom-color;\n\t}\n\n\t/* #endif */\n</style>\n", "import mod from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-grid-item.vue?vue&type=style&index=0&id=99a45d26&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-grid-item.vue?vue&type=style&index=0&id=99a45d26&lang=scss&scoped=true&\""], "sourceRoot": ""}