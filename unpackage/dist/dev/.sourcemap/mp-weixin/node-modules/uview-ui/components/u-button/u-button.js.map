{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/node_modules/uview-ui/components/u-button/u-button.vue?42a1", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/node_modules/uview-ui/components/u-button/u-button.vue?375a", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/node_modules/uview-ui/components/u-button/u-button.vue?d1e1", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/node_modules/uview-ui/components/u-button/u-button.vue?1b80", "uni-app:///node_modules/uview-ui/components/u-button/u-button.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/node_modules/uview-ui/components/u-button/u-button.vue?8ea0"], "names": ["renderjs", "component", "options", "__file", "components", "uLoadingIcon", "uIcon", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "s0", "_self", "_c", "__get_style", "baseColor", "$u", "addStyle", "customStyle", "m0", "Number", "hoverStartTime", "m1", "hoverStayTime", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "name", "mixins", "computed", "bemClass", "loadingColor", "uni", "iconColorCom", "style", "nvueTextStyle", "textSize", "size", "methods", "clickHandler", "getphonenumber", "getuserinfo", "opensetting", "launchapp", "agreeprivacyauthorization"], "mappings": "mJAAA,oIACIA,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,yDACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,aAAc,WACZ,OAAO,kIAITC,MAAO,WACL,OAAO,mHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,YAAY,CAACP,EAAIQ,UAAWR,EAAIS,GAAGC,SAASV,EAAIW,gBACzDC,EAAKC,OAAOb,EAAIc,gBAChBC,EAAKF,OAAOb,EAAIgB,eACpBhB,EAAIiB,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLjB,GAAIA,EACJQ,GAAIA,EACJG,GAAIA,MAKRO,GAAmB,EACnBC,EAAkB,GACtBxB,EAAOyB,eAAgB,G,iCCnDvB,yHAAi6B,eAAG,G,2HCgHp6B,gBACA,YACA,YACA,EA4CA,CACAC,gBAEAC,+DAKAR,gBACA,UAEAS,UAEAC,oBAEA,kBAQA,SACA,SACA,iBACA,iCAVA,SACA,SACA,wBACA,kCAWAC,wBACA,kBAEA,WACA,WACAC,0CAEA,mBACA,UAEA,sBAEAC,wBAGA,qCACA,WACA,gCAEA,wCAGAvB,qBACA,SA0BA,OAzBA,aAEAwB,sCACA,aAEAA,mCAEA,oCAIAA,mBACAA,qBACAA,sBACAA,oBACA,aACAA,gCAIAA,yBACAA,oBACAA,wBAGA,GAGAC,yBACA,SASA,MAPA,qBACAD,mBAEA,aACAA,uCAEAA,8BACA,GAGAE,oBACA,SACAC,YAKA,MAJA,oBACA,qBACA,oBACA,mBACA,IAGAC,SACAC,wBAAA,WAEA,6BAEAP,0BACA,mBACA,oBAIAQ,2BACA,gCAEAC,wBACA,6BAEAzC,kBACA,uBAEA0C,wBACA,6BAEAC,sBACA,2BAEAC,sCACA,6CAGA,c,6DCtSA,yHAAotD,eAAG,G", "file": "node-modules/uview-ui/components/u-button/u-button.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-button.vue?vue&type=template&id=3bf2dba7&scoped=true&\"\nvar renderjs\nimport script from \"./u-button.vue?vue&type=script&lang=js&\"\nexport * from \"./u-button.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-button.vue?vue&type=style&index=0&id=3bf2dba7&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3bf2dba7\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-button/u-button.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-button.vue?vue&type=template&id=3bf2dba7&scoped=true&\"", "var components\ntry {\n  components = {\n    uLoadingIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loading-icon/u-loading-icon\" */ \"uview-ui/components/u-loading-icon/u-loading-icon.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.baseColor, _vm.$u.addStyle(_vm.customStyle)])\n  var m0 = Number(_vm.hoverStartTime)\n  var m1 = Number(_vm.hoverStayTime)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-button.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-button.vue?vue&type=script&lang=js&\"", "<template>\n    <!-- #ifndef APP-NVUE -->\n    <button\n        :hover-start-time=\"Number(hoverStartTime)\"\n        :hover-stay-time=\"Number(hoverStayTime)\"\n        :form-type=\"formType\"\n        :open-type=\"openType\"\n        :app-parameter=\"appParameter\"\n        :hover-stop-propagation=\"hoverStopPropagation\"\n        :send-message-title=\"sendMessageTitle\"\n        :send-message-path=\"sendMessagePath\"\n        :lang=\"lang\"\n        :data-name=\"dataName\"\n        :session-from=\"sessionFrom\"\n        :send-message-img=\"sendMessageImg\"\n        :show-message-card=\"showMessageCard\"\n        @getphonenumber=\"getphonenumber\"\n        @getuserinfo=\"getuserinfo\"\n        @error=\"error\"\n        @opensetting=\"opensetting\"\n        @launchapp=\"launchapp\"\n        @agreeprivacyauthorization=\"agreeprivacyauthorization\"\n        :hover-class=\"!disabled && !loading ? 'u-button--active' : ''\"\n        class=\"u-button u-reset-button\"\n        :style=\"[baseColor, $u.addStyle(customStyle)]\"\n        @tap=\"clickHandler\"\n        :class=\"bemClass\"\n    >\n        <template v-if=\"loading\">\n            <u-loading-icon\n                :mode=\"loadingMode\"\n                :size=\"loadingSize * 1.15\"\n                :color=\"loadingColor\"\n            ></u-loading-icon>\n            <text\n                class=\"u-button__loading-text\"\n                :style=\"[{ fontSize: textSize + 'px' }]\"\n                >{{ loadingText || text }}</text\n            >\n        </template>\n        <template v-else>\n            <u-icon\n                v-if=\"icon\"\n                :name=\"icon\"\n                :color=\"iconColorCom\"\n                :size=\"textSize * 1.35\"\n                :customStyle=\"{ marginRight: '2px' }\"\n            ></u-icon>\n            <slot>\n                <text\n                    class=\"u-button__text\"\n                    :style=\"[{ fontSize: textSize + 'px' }]\"\n                    >{{ text }}</text\n                >\n            </slot>\n        </template>\n    </button>\n    <!-- #endif -->\n\n    <!-- #ifdef APP-NVUE -->\n    <view\n        :hover-start-time=\"Number(hoverStartTime)\"\n        :hover-stay-time=\"Number(hoverStayTime)\"\n        class=\"u-button\"\n        :hover-class=\"\n            !disabled && !loading && !color && (plain || type === 'info')\n                ? 'u-button--active--plain'\n                : !disabled && !loading && !plain\n                ? 'u-button--active'\n                : ''\n        \"\n        @tap=\"clickHandler\"\n        :class=\"bemClass\"\n        :style=\"[baseColor, $u.addStyle(customStyle)]\"\n    >\n        <template v-if=\"loading\">\n            <u-loading-icon\n                :mode=\"loadingMode\"\n                :size=\"loadingSize * 1.15\"\n                :color=\"loadingColor\"\n            ></u-loading-icon>\n            <text\n                class=\"u-button__loading-text\"\n                :style=\"[nvueTextStyle]\"\n                :class=\"[plain && `u-button__text--plain--${type}`]\"\n                >{{ loadingText || text }}</text\n            >\n        </template>\n        <template v-else>\n            <u-icon\n                v-if=\"icon\"\n                :name=\"icon\"\n                :color=\"iconColorCom\"\n                :size=\"textSize * 1.35\"\n            ></u-icon>\n            <text\n                class=\"u-button__text\"\n                :style=\"[\n                    {\n                        marginLeft: icon ? '2px' : 0,\n                    },\n                    nvueTextStyle,\n                ]\"\n                :class=\"[plain && `u-button__text--plain--${type}`]\"\n                >{{ text }}</text\n            >\n        </template>\n    </view>\n    <!-- #endif -->\n</template>\n\n<script>\nimport button from \"../../libs/mixin/button.js\";\nimport openType from \"../../libs/mixin/openType.js\";\nimport props from \"./props.js\";\n/**\n * button 按钮\n * @description Button 按钮\n * @tutorial https://www.uviewui.com/components/button.html\n *\n * @property {Boolean}\t\t\thairline\t\t\t\t是否显示按钮的细边框 (默认 true )\n * @property {String}\t\t\ttype\t\t\t\t\t按钮的预置样式，info，primary，error，warning，success (默认 'info' )\n * @property {String}\t\t\tsize\t\t\t\t\t按钮尺寸，large，normal，mini （默认 normal）\n * @property {String}\t\t\tshape\t\t\t\t\t按钮形状，circle（两边为半圆），square（带圆角） （默认 'square' ）\n * @property {Boolean}\t\t\tplain\t\t\t\t\t按钮是否镂空，背景色透明 （默认 false）\n * @property {Boolean}\t\t\tdisabled\t\t\t\t是否禁用 （默认 false）\n * @property {Boolean}\t\t\tloading\t\t\t\t\t按钮名称前是否带 loading 图标(App-nvue 平台，在 ios 上为雪花，Android上为圆圈) （默认 false）\n * @property {String | Number}\tloadingText\t\t\t\t加载中提示文字\n * @property {String}\t\t\tloadingMode\t\t\t\t加载状态图标类型 （默认 'spinner' ）\n * @property {String | Number}\tloadingSize\t\t\t\t加载图标大小 （默认 15 ）\n * @property {String}\t\t\topenType\t\t\t\t开放能力，具体请看uniapp稳定关于button组件部分说明\n * @property {String}\t\t\tformType\t\t\t\t用于 <form> 组件，点击分别会触发 <form> 组件的 submit/reset 事件\n * @property {String}\t\t\tappParameter\t\t\t打开 APP 时，向 APP 传递的参数，open-type=launchApp时有效 （注：只微信小程序、QQ小程序有效）\n * @property {Boolean}\t\t\thoverStopPropagation\t指定是否阻止本节点的祖先节点出现点击态，微信小程序有效（默认 true ）\n * @property {String}\t\t\tlang\t\t\t\t\t指定返回用户信息的语言，zh_CN 简体中文，zh_TW 繁体中文，en 英文（默认 en ）\n * @property {String}\t\t\tsessionFrom\t\t\t\t会话来源，openType=\"contact\"时有效\n * @property {String}\t\t\tsendMessageTitle\t\t会话内消息卡片标题，openType=\"contact\"时有效\n * @property {String}\t\t\tsendMessagePath\t\t\t会话内消息卡片点击跳转小程序路径，openType=\"contact\"时有效\n * @property {String}\t\t\tsendMessageImg\t\t\t会话内消息卡片图片，openType=\"contact\"时有效\n * @property {Boolean}\t\t\tshowMessageCard\t\t\t是否显示会话内消息卡片，设置此参数为 true，用户进入客服会话会在右下角显示\"可能要发送的小程序\"提示，用户点击后可以快速发送小程序消息，openType=\"contact\"时有效（默认false）\n * @property {String}\t\t\tdataName\t\t\t\t额外传参参数，用于小程序的data-xxx属性，通过target.dataset.name获取\n * @property {String | Number}\tthrottleTime\t\t\t节流，一定时间内只能触发一次 （默认 0 )\n * @property {String | Number}\thoverStartTime\t\t\t按住后多久出现点击态，单位毫秒 （默认 0 )\n * @property {String | Number}\thoverStayTime\t\t\t手指松开后点击态保留时间，单位毫秒 （默认 200 )\n * @property {String | Number}\ttext\t\t\t\t\t按钮文字，之所以通过props传入，是因为slot传入的话（注：nvue中无法控制文字的样式）\n * @property {String}\t\t\ticon\t\t\t\t\t按钮图标\n * @property {String}\t\t\ticonColor\t\t\t\t按钮图标颜色\n * @property {String}\t\t\tcolor\t\t\t\t\t按钮颜色，支持传入linear-gradient渐变色\n * @property {Object}\t\t\tcustomStyle\t\t\t\t定义需要用到的外部样式\n *\n * @event {Function}\tclick\t\t\t非禁止并且非加载中，才能点击\n * @event {Function}\tgetphonenumber\topen-type=\"getPhoneNumber\"时有效\n * @event {Function}\tgetuserinfo\t\t用户点击该按钮时，会返回获取到的用户信息，从返回参数的detail中获取到的值同uni.getUserInfo\n * @event {Function}\terror\t\t\t当使用开放能力时，发生错误的回调\n * @event {Function}\topensetting\t\t在打开授权设置页并关闭后回调\n * @event {Function}\tlaunchapp\t\t打开 APP 成功的回调\n * @event {Function}\tagreeprivacyauthorization\t用户同意隐私协议事件回调\n * @example <u-button>月落</u-button>\n */\nexport default {\n    name: \"u-button\",\n    // #ifdef MP\n    mixins: [uni.$u.mpMixin, uni.$u.mixin, button, openType, props],\n    // #endif\n    // #ifndef MP\n    mixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n    // #endif\n    data() {\n        return {};\n    },\n    computed: {\n        // 生成bem风格的类名\n        bemClass() {\n            // this.bem为一个computed变量，在mixin中\n            if (!this.color) {\n                return this.bem(\n                    \"button\",\n                    [\"type\", \"shape\", \"size\"],\n                    [\"disabled\", \"plain\", \"hairline\"]\n                );\n            } else {\n                // 由于nvue的原因，在有color参数时，不需要传入type，否则会生成type相关的类型，影响最终的样式\n                return this.bem(\n                    \"button\",\n                    [\"shape\", \"size\"],\n                    [\"disabled\", \"plain\", \"hairline\"]\n                );\n            }\n        },\n        loadingColor() {\n            if (this.plain) {\n                // 如果有设置color值，则用color值，否则使用type主题颜色\n                return this.color\n                    ? this.color\n                    : uni.$u.config.color[`u-${this.type}`];\n            }\n            if (this.type === \"info\") {\n                return \"#c9c9c9\";\n            }\n            return \"rgb(200, 200, 200)\";\n        },\n        iconColorCom() {\n            // 如果是镂空状态，设置了color就用color值，否则使用主题颜色，\n            // u-icon的color能接受一个主题颜色的值\n\t\t\tif (this.iconColor) return this.iconColor;\n\t\t\tif (this.plain) {\n                return this.color ? this.color : this.type;\n            } else {\n                return this.type === \"info\" ? \"#000000\" : \"#ffffff\";\n            }\n        },\n        baseColor() {\n            let style = {};\n            if (this.color) {\n                // 针对自定义了color颜色的情况，镂空状态下，就是用自定义的颜色\n                style.color = this.plain ? this.color : \"white\";\n                if (!this.plain) {\n                    // 非镂空，背景色使用自定义的颜色\n                    style[\"background-color\"] = this.color;\n                }\n                if (this.color.indexOf(\"gradient\") !== -1) {\n                    // 如果自定义的颜色为渐变色，不显示边框，以及通过backgroundImage设置渐变色\n                    // weex文档说明可以写borderWidth的形式，为什么这里需要分开写？\n                    // 因为weex是阿里巴巴为了部门业绩考核而做的你懂的东西，所以需要这么写才有效\n                    style.borderTopWidth = 0;\n                    style.borderRightWidth = 0;\n                    style.borderBottomWidth = 0;\n                    style.borderLeftWidth = 0;\n                    if (!this.plain) {\n                        style.backgroundImage = this.color;\n                    }\n                } else {\n                    // 非渐变色，则设置边框相关的属性\n                    style.borderColor = this.color;\n                    style.borderWidth = \"1px\";\n                    style.borderStyle = \"solid\";\n                }\n            }\n            return style;\n        },\n        // nvue版本按钮的字体不会继承父组件的颜色，需要对每一个text组件进行单独的设置\n        nvueTextStyle() {\n            let style = {};\n            // 针对自定义了color颜色的情况，镂空状态下，就是用自定义的颜色\n            if (this.type === \"info\") {\n                style.color = \"#323233\";\n            }\n            if (this.color) {\n                style.color = this.plain ? this.color : \"white\";\n            }\n            style.fontSize = this.textSize + \"px\";\n            return style;\n        },\n        // 字体大小\n        textSize() {\n            let fontSize = 14,\n                { size } = this;\n            if (size === \"large\") fontSize = 16;\n            if (size === \"normal\") fontSize = 14;\n            if (size === \"small\") fontSize = 12;\n            if (size === \"mini\") fontSize = 10;\n            return fontSize;\n        },\n    },\n    methods: {\n        clickHandler() {\n            // 非禁止并且非加载中，才能点击\n            if (!this.disabled && !this.loading) {\n\t\t\t\t// 进行节流控制，每this.throttle毫秒内，只在开始处执行\n\t\t\t\tuni.$u.throttle(() => {\n\t\t\t\t\tthis.$emit(\"click\");\n\t\t\t\t}, this.throttleTime);\n            }\n        },\n        // 下面为对接uniapp官方按钮开放能力事件回调的对接\n        getphonenumber(res) {\n            this.$emit(\"getphonenumber\", res);\n        },\n        getuserinfo(res) {\n            this.$emit(\"getuserinfo\", res);\n        },\n        error(res) {\n            this.$emit(\"error\", res);\n        },\n        opensetting(res) {\n            this.$emit(\"opensetting\", res);\n        },\n        launchapp(res) {\n            this.$emit(\"launchapp\", res);\n        },\n        agreeprivacyauthorization(res) {\n            this.$emit(\"agreeprivacyauthorization\", res);\n        },\n    },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"../../libs/css/components.scss\";\n\n/* #ifndef APP-NVUE */\n@import \"./vue.scss\";\n/* #endif */\n\n/* #ifdef APP-NVUE */\n@import \"./nvue.scss\";\n/* #endif */\n\n$u-button-u-button-height: 40px !default;\n$u-button-text-font-size: 15px !default;\n$u-button-loading-text-font-size: 15px !default;\n$u-button-loading-text-margin-left: 4px !default;\n$u-button-large-width: 100% !default;\n$u-button-large-height: 50px !default;\n$u-button-normal-padding: 0 12px !default;\n$u-button-large-padding: 0 15px !default;\n$u-button-normal-font-size: 14px !default;\n$u-button-small-min-width: 60px !default;\n$u-button-small-height: 30px !default;\n$u-button-small-padding: 0px 8px !default;\n$u-button-mini-padding: 0px 8px !default;\n$u-button-small-font-size: 12px !default;\n$u-button-mini-height: 22px !default;\n$u-button-mini-font-size: 10px !default;\n$u-button-mini-min-width: 50px !default;\n$u-button-disabled-opacity: 0.5 !default;\n$u-button-info-color: #323233 !default;\n$u-button-info-background-color: #fff !default;\n$u-button-info-border-color: #ebedf0 !default;\n$u-button-info-border-width: 1px !default;\n$u-button-info-border-style: solid !default;\n$u-button-success-color: #fff !default;\n$u-button-success-background-color: $u-success !default;\n$u-button-success-border-color: $u-button-success-background-color !default;\n$u-button-success-border-width: 1px !default;\n$u-button-success-border-style: solid !default;\n$u-button-primary-color: #fff !default;\n$u-button-primary-background-color: $u-primary !default;\n$u-button-primary-border-color: $u-button-primary-background-color !default;\n$u-button-primary-border-width: 1px !default;\n$u-button-primary-border-style: solid !default;\n$u-button-error-color: #fff !default;\n$u-button-error-background-color: $u-error !default;\n$u-button-error-border-color: $u-button-error-background-color !default;\n$u-button-error-border-width: 1px !default;\n$u-button-error-border-style: solid !default;\n$u-button-warning-color: #fff !default;\n$u-button-warning-background-color: $u-warning !default;\n$u-button-warning-border-color: $u-button-warning-background-color !default;\n$u-button-warning-border-width: 1px !default;\n$u-button-warning-border-style: solid !default;\n$u-button-block-width: 100% !default;\n$u-button-circle-border-top-right-radius: 100px !default;\n$u-button-circle-border-top-left-radius: 100px !default;\n$u-button-circle-border-bottom-left-radius: 100px !default;\n$u-button-circle-border-bottom-right-radius: 100px !default;\n$u-button-square-border-top-right-radius: 3px !default;\n$u-button-square-border-top-left-radius: 3px !default;\n$u-button-square-border-bottom-left-radius: 3px !default;\n$u-button-square-border-bottom-right-radius: 3px !default;\n$u-button-icon-min-width: 1em !default;\n$u-button-plain-background-color: #fff !default;\n$u-button-hairline-border-width: 0.5px !default;\n\n.u-button {\n    height: $u-button-u-button-height;\n    position: relative;\n    align-items: center;\n    justify-content: center;\n    @include flex;\n    /* #ifndef APP-NVUE */\n    box-sizing: border-box;\n    /* #endif */\n    flex-direction: row;\n\n    &__text {\n        font-size: $u-button-text-font-size;\n    }\n\n    &__loading-text {\n        font-size: $u-button-loading-text-font-size;\n        margin-left: $u-button-loading-text-margin-left;\n    }\n\n    &--large {\n        /* #ifndef APP-NVUE */\n        width: $u-button-large-width;\n        /* #endif */\n        height: $u-button-large-height;\n        padding: $u-button-large-padding;\n    }\n\n    &--normal {\n        padding: $u-button-normal-padding;\n        font-size: $u-button-normal-font-size;\n    }\n\n    &--small {\n        /* #ifndef APP-NVUE */\n        min-width: $u-button-small-min-width;\n        /* #endif */\n        height: $u-button-small-height;\n        padding: $u-button-small-padding;\n        font-size: $u-button-small-font-size;\n    }\n\n    &--mini {\n        height: $u-button-mini-height;\n        font-size: $u-button-mini-font-size;\n        /* #ifndef APP-NVUE */\n        min-width: $u-button-mini-min-width;\n        /* #endif */\n        padding: $u-button-mini-padding;\n    }\n\n    &--disabled {\n        opacity: $u-button-disabled-opacity;\n    }\n\n    &--info {\n        color: $u-button-info-color;\n        background-color: $u-button-info-background-color;\n        border-color: $u-button-info-border-color;\n        border-width: $u-button-info-border-width;\n        border-style: $u-button-info-border-style;\n    }\n\n    &--success {\n        color: $u-button-success-color;\n        background-color: $u-button-success-background-color;\n        border-color: $u-button-success-border-color;\n        border-width: $u-button-success-border-width;\n        border-style: $u-button-success-border-style;\n    }\n\n    &--primary {\n        color: $u-button-primary-color;\n        background-color: $u-button-primary-background-color;\n        border-color: $u-button-primary-border-color;\n        border-width: $u-button-primary-border-width;\n        border-style: $u-button-primary-border-style;\n    }\n\n    &--error {\n        color: $u-button-error-color;\n        background-color: $u-button-error-background-color;\n        border-color: $u-button-error-border-color;\n        border-width: $u-button-error-border-width;\n        border-style: $u-button-error-border-style;\n    }\n\n    &--warning {\n        color: $u-button-warning-color;\n        background-color: $u-button-warning-background-color;\n        border-color: $u-button-warning-border-color;\n        border-width: $u-button-warning-border-width;\n        border-style: $u-button-warning-border-style;\n    }\n\n    &--block {\n        @include flex;\n        width: $u-button-block-width;\n    }\n\n    &--circle {\n        border-top-right-radius: $u-button-circle-border-top-right-radius;\n        border-top-left-radius: $u-button-circle-border-top-left-radius;\n        border-bottom-left-radius: $u-button-circle-border-bottom-left-radius;\n        border-bottom-right-radius: $u-button-circle-border-bottom-right-radius;\n    }\n\n    &--square {\n        border-bottom-left-radius: $u-button-square-border-top-right-radius;\n        border-bottom-right-radius: $u-button-square-border-top-left-radius;\n        border-top-left-radius: $u-button-square-border-bottom-left-radius;\n        border-top-right-radius: $u-button-square-border-bottom-right-radius;\n    }\n\n    &__icon {\n        /* #ifndef APP-NVUE */\n        min-width: $u-button-icon-min-width;\n        line-height: inherit !important;\n        vertical-align: top;\n        /* #endif */\n    }\n\n    &--plain {\n        background-color: $u-button-plain-background-color;\n    }\n\n    &--hairline {\n        border-width: $u-button-hairline-border-width !important;\n    }\n}\n</style>\n", "import mod from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-button.vue?vue&type=style&index=0&id=3bf2dba7&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-button.vue?vue&type=style&index=0&id=3bf2dba7&lang=scss&scoped=true&\""], "sourceRoot": ""}