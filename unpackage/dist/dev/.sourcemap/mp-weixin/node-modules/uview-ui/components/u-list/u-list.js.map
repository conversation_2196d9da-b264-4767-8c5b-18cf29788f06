{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/node_modules/uview-ui/components/u-list/u-list.vue?e167", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/node_modules/uview-ui/components/u-list/u-list.vue?2fc5", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/node_modules/uview-ui/components/u-list/u-list.vue?55e8", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/node_modules/uview-ui/components/u-list/u-list.vue?1d94", "uni-app:///node_modules/uview-ui/components/u-list/u-list.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/node_modules/uview-ui/components/u-list/u-list.vue?ebd0"], "names": ["renderjs", "component", "options", "__file", "components", "render", "_vm", "this", "_h", "$createElement", "s0", "_self", "_c", "__get_style", "listStyle", "m0", "Number", "scrollTop", "m1", "lowerThreshold", "m2", "upperThreshold", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "name", "mixins", "watch", "scrollIntoView", "innerScrollTop", "offset", "sys", "computed", "addUnit", "provide", "uList", "created", "mounted", "methods", "updateOffsetFromChild", "onScroll", "scrollIntoViewById", "scrolltolower", "uni", "scrolltoupper"], "mappings": "+IAAA,oIACIA,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,qDACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,YAAY,CAACP,EAAIQ,aAC1BC,EAAKC,OAAOV,EAAIW,WAChBC,EAAKF,OAAOV,EAAIa,gBAChBC,EAAKJ,OAAOV,EAAIe,gBACpBf,EAAIgB,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLhB,GAAIA,EACJK,GAAIA,EACJG,GAAIA,EACJE,GAAIA,MAKRO,GAAmB,EACnBC,EAAkB,GACtBvB,EAAOwB,eAAgB,G,iCCvBvB,yHAA+5B,eAAG,G,2HCuCl6B,gBAIA,EAsBA,CACAC,cACAC,2CACAC,OACAC,2BACA,6BAGAV,gBACA,OAEAW,iBAEAC,SACAC,iBAGAC,UACAvB,qBACA,SACAwB,eAKA,OAJA,uCACA,0CAEA,mDACA,oDAGAC,mBACA,OACAC,aAGAC,mBACA,aACA,iBACA,iBAEAC,qBACAC,SACAC,kCACA,eAEAC,qBACA,QAKA5B,qBAEA,sBACA,kCAEA6B,iCAWAC,0BAAA,WACAC,gCACA,6BAKAC,0BAAA,WACAD,gCACA,yBAEA,iBAKA,c,6DClJA,yHAAktD,eAAG,G", "file": "node-modules/uview-ui/components/u-list/u-list.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-list.vue?vue&type=template&id=9cd1e132&scoped=true&\"\nvar renderjs\nimport script from \"./u-list.vue?vue&type=script&lang=js&\"\nexport * from \"./u-list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-list.vue?vue&type=style&index=0&id=9cd1e132&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9cd1e132\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-list/u-list.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-list.vue?vue&type=template&id=9cd1e132&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.listStyle])\n  var m0 = Number(_vm.scrollTop)\n  var m1 = Number(_vm.lowerThreshold)\n  var m2 = Number(_vm.upperThreshold)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-list.vue?vue&type=script&lang=js&\"", "<template>\n\t<!-- #ifdef APP-NVUE -->\n\t<list\n\t\tclass=\"u-list\"\n\t\t:enableBackToTop=\"enableBackToTop\"\n\t\t:loadmoreoffset=\"lowerThreshold\"\n\t\t:showScrollbar=\"showScrollbar\"\n\t\t:style=\"[listStyle]\"\n\t\t:offset-accuracy=\"Number(offsetAccuracy)\"\n\t\t@scroll=\"onScroll\"\n\t\t@loadmore=\"scrolltolower\"\n\t>\n\t\t<slot />\n\t</list>\n\t<!-- #endif -->\n\t<!-- #ifndef APP-NVUE -->\n\t<scroll-view\n\t\tclass=\"u-list\"\n\t\t:scroll-into-view=\"scrollIntoView\"\n\t\t:style=\"[listStyle]\"\n\t\tscroll-y\n\t\t:scroll-top=\"Number(scrollTop)\"\n\t\t:lower-threshold=\"Number(lowerThreshold)\"\n\t\t:upper-threshold=\"Number(upperThreshold)\"\n\t\t:show-scrollbar=\"showScrollbar\"\n\t\t:enable-back-to-top=\"enableBackToTop\"\n\t\t:scroll-with-animation=\"scrollWithAnimation\"\n\t\t@scroll=\"onScroll\"\n\t\t@scrolltolower=\"scrolltolower\"\n\t\t@scrolltoupper=\"scrolltoupper\"\n\t>\n\t\t<view>\n\t\t\t<slot />\n\t\t</view>\n\t</scroll-view>\n\t<!-- #endif -->\n</template>\n\n<script>\n\timport props from './props.js';\n\t// #ifdef APP-NVUE\n\tconst dom = uni.requireNativePlugin('dom')\n\t// #endif\n\t/**\n\t * List 列表\n\t * @description 该组件为高性能列表组件\n\t * @tutorial https://www.uviewui.com/components/list.html\n\t * @property {Boolean}\t\t\tshowScrollbar\t\t控制是否出现滚动条，仅nvue有效 （默认 false ）\n\t * @property {String ｜ Number}\tlowerThreshold\t\t距底部多少时触发scrolltolower事件 （默认 50 ）\n\t * @property {String ｜ Number}\tupperThreshold\t\t距顶部多少时触发scrolltoupper事件，非nvue有效 （默认 0 ）\n\t * @property {String ｜ Number}\tscrollTop\t\t\t设置竖向滚动条位置（默认 0 ）\n\t * @property {String ｜ Number}\toffsetAccuracy\t\t控制 onscroll 事件触发的频率，仅nvue有效（默认 10 ）\n\t * @property {Boolean}\t\t\tenableFlex\t\t\t启用 flexbox 布局。开启后，当前节点声明了display: flex就会成为flex container，并作用于其孩子节点，仅微信小程序有效（默认 false ）\n\t * @property {Boolean}\t\t\tpagingEnabled\t\t是否按分页模式显示List，（默认 false ）\n\t * @property {Boolean}\t\t\tscrollable\t\t\t是否允许List滚动（默认 true ）\n\t * @property {String}\t\t\tscrollIntoView\t\t值应为某子元素id（id不能以数字开头）\n\t * @property {Boolean}\t\t\tscrollWithAnimation\t在设置滚动条位置时使用动画过渡 （默认 false ）\n\t * @property {Boolean}\t\t\tenableBackToTop\t\tiOS点击顶部状态栏、安卓双击标题栏时，滚动条返回顶部，只对微信小程序有效 （默认 false ）\n\t * @property {String ｜ Number}\theight\t\t\t\t列表的高度 （默认 0 ）\n\t * @property {String ｜ Number}\twidth\t\t\t\t列表宽度 （默认 0 ）\n\t * @property {String ｜ Number}\tpreLoadScreen\t\t列表前后预渲染的屏数，1代表一个屏幕的高度，1.5代表1个半屏幕高度  （默认 1 ）\n\t * @property {Object}\t\t\tcustomStyle\t\t\t定义需要用到的外部样式\n\t *\n\t * @example <u-list @scrolltolower=\"scrolltolower\"></u-list>\n\t */\n\texport default {\n\t\tname: 'u-list',\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\n\t\twatch: {\n\t\t\tscrollIntoView(n) {\n\t\t\t\tthis.scrollIntoViewById(n)\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 记录内部滚动的距离\n\t\t\t\tinnerScrollTop: 0,\n\t\t\t\t// vue下，scroll-view在上拉加载时的偏移值\n\t\t\t\toffset: 0,\n\t\t\t\tsys: uni.$u.sys()\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tlistStyle() {\n\t\t\t\tconst style = {},\n\t\t\t\t\taddUnit = uni.$u.addUnit\n\t\t\t\tif (this.width != 0) style.width = addUnit(this.width)\n\t\t\t\tif (this.height != 0) style.height = addUnit(this.height)\n\t\t\t\t// 如果没有定义列表高度，则默认使用屏幕高度\n\t\t\t\tif (!style.height) style.height = addUnit(this.sys.windowHeight, 'px')\n\t\t\t\treturn uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle))\n\t\t\t}\n\t\t},\n\t\tprovide() {\n\t\t\treturn {\n\t\t\t\tuList: this\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.refs = []\n\t\t\tthis.children = []\n\t\t\tthis.anchors = []\n\t\t},\n\t\tmounted() {},\n\t\tmethods: {\n\t\t\tupdateOffsetFromChild(top) {\n\t\t\t\tthis.offset = top\n\t\t\t},\n\t\t\tonScroll(e) {\n\t\t\t\tlet scrollTop = 0\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tscrollTop = e.contentOffset.y\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tscrollTop = e.detail.scrollTop\n\t\t\t\t// #endif\n\t\t\t\tthis.innerScrollTop = scrollTop\n\t\t\t\tthis.$emit('scroll', Math.abs(scrollTop))\n\t\t\t},\n\t\t\tscrollIntoViewById(id) {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// 根据id参数，找到所有u-list-item中匹配的节点，再通过dom模块滚动到对应的位置\n\t\t\t\tconst item = this.refs.find(item => item.$refs[id] ? true : false)\n\t\t\t\tdom.scrollToElement(item.$refs[id], {\n\t\t\t\t\t// 是否需要滚动动画\n\t\t\t\t\tanimated: this.scrollWithAnimation\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 滚动到底部触发事件\n\t\t\tscrolltolower(e) {\n\t\t\t\tuni.$u.sleep(30).then(() => {\n\t\t\t\t\tthis.$emit('scrolltolower')\n\t\t\t\t})\n\t\t\t},\n\t\t\t// #ifndef APP-NVUE\n\t\t\t// 滚动到底部时触发，非nvue有效\n\t\t\tscrolltoupper(e) {\n\t\t\t\tuni.$u.sleep(30).then(() => {\n\t\t\t\t\tthis.$emit('scrolltoupper')\n\t\t\t\t\t// 这一句很重要，能绝对保证在性功能障碍的webview，滚动条到顶时，取消偏移值，让页面置顶\n\t\t\t\t\tthis.offset = 0\n\t\t\t\t})\n\t\t\t}\n\t\t\t// #endif\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-list {\n\t\t@include flex(column);\n\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-list.vue?vue&type=style&index=0&id=9cd1e132&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-list.vue?vue&type=style&index=0&id=9cd1e132&lang=scss&scoped=true&\""], "sourceRoot": ""}