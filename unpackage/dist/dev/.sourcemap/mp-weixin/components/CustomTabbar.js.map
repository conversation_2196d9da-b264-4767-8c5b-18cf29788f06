{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/components/CustomTabbar.vue?13e1", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/components/CustomTabbar.vue?b319", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/components/CustomTabbar.vue?98e6", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/components/CustomTabbar.vue?f08c", "uni-app:///components/CustomTabbar.vue", "webpack:////Users/<USER>/Documents/项目管家/shenzhenguaguakeji/TrainingCourseSummerCamp/qianduan/TrainingCourseSummerCamp/components/CustomTabbar.vue?2633"], "names": ["renderjs", "component", "options", "__file", "components", "uTabbar", "uTabbarItem", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "props", "current", "type", "default", "data", "lang", "tabList", "page", "icon", "text_zh", "text_en", "mounted", "uni", "<PERSON><PERSON><PERSON><PERSON>", "methods", "switchTab", "url", "onLangChange"], "mappings": "wHAAA,oIACIA,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,8BACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,QAAS,WACP,OAAO,sHAITC,YAAa,WACX,OAAO,iIAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,iCCtCvB,yHAAw3B,eAAG,G,sHCoB33B,CACAC,OACAC,SACAC,YACAC,YAGAC,gBACA,OACAC,oCACAC,SACA,CAAAC,0BAAAC,YAAAC,aAAAC,gBACA,CAAAH,2BAAAC,gBAAAC,aAAAC,kBACA,CAAAH,4BAAAC,YAAAC,eAAAC,kBACA,CAAAH,4BAAAC,eAAAC,aAAAC,sBAIAC,mBAEAC,wCAEAC,yBACAD,yCAEAE,SACAC,sBACA,kBACAH,aAAAI,4BAEAC,yBACA,eAGA,c,6DCtDA,yHAAivC,eAAG,G", "file": "components/CustomTabbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./CustomTabbar.vue?vue&type=template&id=7ba212ec&\"\nvar renderjs\nimport script from \"./CustomTabbar.vue?vue&type=script&lang=js&\"\nexport * from \"./CustomTabbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./CustomTabbar.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/CustomTabbar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./CustomTabbar.vue?vue&type=template&id=7ba212ec&\"", "var components\ntry {\n  components = {\n    uTabbar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-tabbar/u-tabbar\" */ \"uview-ui/components/u-tabbar/u-tabbar.vue\"\n      )\n    },\n    uTabbarItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-tabbar-item/u-tabbar-item\" */ \"uview-ui/components/u-tabbar-item/u-tabbar-item.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./CustomTabbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./CustomTabbar.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"custom-tabbar\">\n    <u-tabbar\n    :value=\"current\"\n    :fixed=\"true\"\n    :placeholder=\"true\"\n    :safe-area-inset-bottom=\"true\"\n    @change=\"switchTab\"\n  >\n    <u-tabbar-item\n      v-for=\"(item, idx) in tabList\"\n      :key=\"item.page\"\n      :icon=\"item.icon\"\n      :text=\"lang === 'zh' ? item.text_zh : item.text_en\"\n    />\n  </u-tabbar>\n  </view>\n</template>\n\n<script>\nexport default {\n  props: {\n    current: {\n      type: Number,\n      default: 0\n    }\n  },\n  data() {\n    return {\n      lang: uni.getStorageSync('lang') || 'zh',\n      tabList: [\n        { page: '/pages/index/index', icon: 'home', text_zh: '首页', text_en: 'Home' },\n        { page: '/pages/events/index', icon: 'calendar', text_zh: '活动', text_en: 'Events' },\n        { page: '/pages/my-join/index', icon: 'star', text_zh: '我参与的', text_en: 'Joined' },\n        { page: '/pages/profile/index', icon: 'account', text_zh: '我的', text_en: 'Profile' }\n      ]\n    }\n  },\n  mounted() {\n    // 监听全局语言切换事件\n    uni.$on('lang-change', this.onLangChange)\n  },\n  beforeDestroy() {\n    uni.$off('lang-change', this.onLangChange)\n  },\n  methods: {\n    switchTab(idx) {\n      if (idx === this.current) return\n      uni.switchTab({ url: this.tabList[idx].page })\n    },\n    onLangChange(newLang) {\n      this.lang = newLang\n    }\n  }\n}\n</script>\n\n<style>\n.custom-tabbar {\n  \n}\n\n</style>", "import mod from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./CustomTabbar.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./CustomTabbar.vue?vue&type=style&index=0&lang=css&\""], "sourceRoot": ""}