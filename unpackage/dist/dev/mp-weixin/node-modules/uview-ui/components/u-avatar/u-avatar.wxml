<view data-event-opts="{{[['tap',[['clickHandler',['$event']]]]]}}" class="{{['u-avatar','data-v-b36130f2','u-avatar--'+shape]}}" style="{{$root.s0}}" bindtap="__e"><block wx:if="{{$slots.default}}"><slot></slot></block><block wx:else><block wx:if="{{mpAvatar&&allowMp}}"><open-data style="{{'width:'+($root.g0)+';'+('height:'+($root.g1)+';')}}" type="userAvatarUrl" class="data-v-b36130f2"></open-data></block><block wx:if="{{mpAvatar&&allowMp}}"></block><block wx:else><block wx:if="{{icon}}"><u-icon vue-id="1b1b8aa6-1" name="{{icon}}" size="{{fontSize}}" color="{{color}}" class="data-v-b36130f2" bind:__l="__l"></u-icon></block><block wx:else><block wx:if="{{text}}"><u--text vue-id="1b1b8aa6-2" text="{{text}}" size="{{fontSize}}" color="{{color}}" align="center" customStyle="justify-content: center" class="data-v-b36130f2" bind:__l="__l"></u--text></block><block wx:else><image class="{{['u-avatar__image','data-v-b36130f2','u-avatar__image--'+shape]}}" style="{{'width:'+($root.g2)+';'+('height:'+($root.g3)+';')}}" src="{{avatarUrl||defaultUrl}}" mode="{{mode}}" data-event-opts="{{[['error',[['errorHandler',['$event']]]]]}}" binderror="__e"></image></block></block></block></block></view>