@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
view.data-v-643b3322, scroll-view.data-v-643b3322, swiper-item.data-v-643b3322 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-radio.data-v-643b3322 {

  display: flex;

  flex-direction: row;
  overflow: hidden;
  flex-direction: row;
  align-items: center;
}
.u-radio-label--left.data-v-643b3322 {
  flex-direction: row;
}
.u-radio-label--right.data-v-643b3322 {
  flex-direction: row-reverse;
  justify-content: space-between;
}
.u-radio__icon-wrap.data-v-643b3322 {
  box-sizing: border-box;
  transition-property: border-color, background-color, color;
  transition-duration: 0.2s;
  color: #606266;

  display: flex;

  flex-direction: row;
  align-items: center;
  justify-content: center;
  color: transparent;
  text-align: center;
  margin-right: 6px;
  font-size: 20px;
  border-width: 1px;
  border-color: #c8c9cc;
  border-style: solid;
}
.u-radio__icon-wrap--circle.data-v-643b3322 {
  border-radius: 100%;
}
.u-radio__icon-wrap--square.data-v-643b3322 {
  border-radius: 3px;
}
.u-radio__icon-wrap--checked.data-v-643b3322 {
  color: #fff;
  background-color: red;
  border-color: #2979ff;
}
.u-radio__icon-wrap--disabled.data-v-643b3322 {
  background-color: #ebedf0 !important;
}
.u-radio__icon-wrap--disabled--checked.data-v-643b3322 {
  color: #c8c9cc !important;
}
.u-radio__label.data-v-643b3322 {
  word-wrap: break-word;
  margin-left: 5px;
  margin-right: 12px;
  color: #606266;
  font-size: 15px;
}
.u-radio__label--disabled.data-v-643b3322 {
  color: #c8c9cc;
}
