@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
view.data-v-15831087, scroll-view.data-v-15831087, swiper-item.data-v-15831087 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-text.data-v-15831087 {

  display: flex;

  flex-direction: row;
  align-items: center;
  flex-wrap: nowrap;
  flex: 1;
  width: 100%;
}
.u-text__price.data-v-15831087 {
  font-size: 14px;
  color: #606266;
}
.u-text__value.data-v-15831087 {
  font-size: 14px;

  display: flex;

  flex-direction: row;
  color: #606266;
  flex-wrap: wrap;
  text-overflow: ellipsis;
  align-items: center;
}
.u-text__value--primary.data-v-15831087 {
  color: #3c9cff;
}
.u-text__value--warning.data-v-15831087 {
  color: #f9ae3d;
}
.u-text__value--success.data-v-15831087 {
  color: #5ac725;
}
.u-text__value--info.data-v-15831087 {
  color: #909399;
}
.u-text__value--error.data-v-15831087 {
  color: #f56c6c;
}
.u-text__value--main.data-v-15831087 {
  color: #303133;
}
.u-text__value--content.data-v-15831087 {
  color: #606266;
}
.u-text__value--tips.data-v-15831087 {
  color: #909193;
}
.u-text__value--light.data-v-15831087 {
  color: #c0c4cc;
}
