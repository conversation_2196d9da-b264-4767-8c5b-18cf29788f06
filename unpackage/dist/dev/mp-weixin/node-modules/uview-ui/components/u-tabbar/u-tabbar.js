(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["node-modules/uview-ui/components/u-tabbar/u-tabbar"],{510:function(e,t,n){"use strict";n.r(t);var r=n(511),u=n(513);for(var o in u)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return u[e]}))}(o);n(516);var i,a=n(32),c=Object(a["default"])(u["default"],r["render"],r["staticRenderFns"],!1,null,"3426a5b2",null,!1,r["components"],i);c.options.__file="node_modules/uview-ui/components/u-tabbar/u-tabbar.vue",t["default"]=c.exports},511:function(e,t,n){"use strict";n.r(t);var r=n(512);n.d(t,"render",(function(){return r["render"]})),n.d(t,"staticRenderFns",(function(){return r["staticRenderFns"]})),n.d(t,"recyclableRender",(function(){return r["recyclableRender"]})),n.d(t,"components",(function(){return r["components"]}))},512:function(e,t,n){"use strict";var r;n.r(t),n.d(t,"render",(function(){return u})),n.d(t,"staticRenderFns",(function(){return i})),n.d(t,"recyclableRender",(function(){return o})),n.d(t,"components",(function(){return r}));try{r={uSafeBottom:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-safe-bottom/u-safe-bottom")]).then(n.bind(null,502))}}}catch(a){if(-1===a.message.indexOf("Cannot find module")||-1===a.message.indexOf(".vue"))throw a;console.error(a.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var u=function(){var e=this,t=e.$createElement,n=(e._self._c,e.__get_style([e.tabbarStyle]));e.$mp.data=Object.assign({},{$root:{s0:n}})},o=!1,i=[];u._withStripped=!0},513:function(e,t,n){"use strict";n.r(t);var r=n(514),u=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=u.a},514:function(e,t,n){"use strict";(function(e){var r=n(4);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var u=r(n(56)),o=r(n(58)),i=r(n(515)),a={name:"u-tabbar",mixins:[e.$u.mpMixin,e.$u.mixin,i.default],data:function(){return{placeholderHeight:0}},computed:{tabbarStyle:function(){var t={zIndex:this.zIndex};return e.$u.deepMerge(t,e.$u.addStyle(this.customStyle))},updateChild:function(){return[this.value,this.activeColor,this.inactiveColor]},updatePlaceholder:function(){return[this.fixed,this.placeholder]}},watch:{updateChild:function(){this.updateChildren()},updatePlaceholder:function(){this.setPlaceholderHeight()}},created:function(){this.children=[]},mounted:function(){this.setPlaceholderHeight()},methods:{updateChildren:function(){this.children.length&&this.children.map((function(e){return e.updateFromParent()}))},setPlaceholderHeight:function(){var t=this;return(0,o.default)(u.default.mark((function n(){return u.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.fixed&&t.placeholder){n.next=2;break}return n.abrupt("return");case 2:return n.next=4,e.$u.sleep(20);case 4:t.$uGetRect(".u-tabbar__content").then((function(e){var n=e.height,r=void 0===n?50:n;t.placeholderHeight=r}));case 5:case"end":return n.stop()}}),n)})))()}}};t.default=a}).call(this,n(2)["default"])},516:function(e,t,n){"use strict";n.r(t);var r=n(517),u=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=u.a},517:function(e,t,n){}}]);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/node-modules/uview-ui/components/u-tabbar/u-tabbar.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'node-modules/uview-ui/components/u-tabbar/u-tabbar-create-component',
    {
        'node-modules/uview-ui/components/u-tabbar/u-tabbar-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(510))
        })
    },
    [['node-modules/uview-ui/components/u-tabbar/u-tabbar-create-component']]
]);
