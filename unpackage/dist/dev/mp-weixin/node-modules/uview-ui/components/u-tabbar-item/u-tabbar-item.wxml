<view data-event-opts="{{[['tap',[['clickHandler',['$event']]]]]}}" class="u-tabbar-item data-v-b8fe2b06" style="{{$root.s0}}" bindtap="__e"><view class="u-tabbar-item__icon data-v-b8fe2b06"><block wx:if="{{icon}}"><u-icon vue-id="61a38cf7-1" name="{{icon}}" color="{{isActive?parentData.activeColor:parentData.inactiveColor}}" size="{{20}}" class="data-v-b8fe2b06" bind:__l="__l"></u-icon></block><block wx:else><block wx:if="{{isActive}}"><slot name="active-icon"></slot></block><block wx:else><slot name="inactive-icon"></slot></block></block><u-badge vue-id="61a38cf7-2" absolute="{{true}}" offset="{{[0,dot?'34rpx':badge>9?'14rpx':'20rpx']}}" customStyle="{{badgeStyle}}" isDot="{{dot}}" value="{{badge||(dot?1:null)}}" show="{{dot||badge>0}}" class="data-v-b8fe2b06" bind:__l="__l"></u-badge></view><block wx:if="{{$slots.text}}"><slot name="text"></slot></block><block wx:else><text class="u-tabbar-item__text data-v-b8fe2b06" style="{{'color:'+(isActive?parentData.activeColor:parentData.inactiveColor)+';'}}">{{text}}</text></block></view>