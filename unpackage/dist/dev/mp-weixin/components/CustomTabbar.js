(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/CustomTabbar"],{348:function(n,e,t){"use strict";t.r(e);var o=t(349),r=t(351);for(var a in r)["default"].indexOf(a)<0&&function(n){t.d(e,n,(function(){return r[n]}))}(a);t(353);var u,i=t(32),c=Object(i["default"])(r["default"],o["render"],o["staticRenderFns"],!1,null,null,null,!1,o["components"],u);c.options.__file="components/CustomTabbar.vue",e["default"]=c.exports},349:function(n,e,t){"use strict";t.r(e);var o=t(350);t.d(e,"render",(function(){return o["render"]})),t.d(e,"staticRenderFns",(function(){return o["staticRenderFns"]})),t.d(e,"recyclableRender",(function(){return o["recyclableRender"]})),t.d(e,"components",(function(){return o["components"]}))},350:function(n,e,t){"use strict";var o;t.r(e),t.d(e,"render",(function(){return r})),t.d(e,"staticRenderFns",(function(){return u})),t.d(e,"recyclableRender",(function(){return a})),t.d(e,"components",(function(){return o}));try{o={uTabbar:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-tabbar/u-tabbar")]).then(t.bind(null,510))},uTabbarItem:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-tabbar-item/u-tabbar-item")]).then(t.bind(null,518))}}}catch(i){if(-1===i.message.indexOf("Cannot find module")||-1===i.message.indexOf(".vue"))throw i;console.error(i.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var r=function(){var n=this,e=n.$createElement;n._self._c},a=!1,u=[];r._withStripped=!0},351:function(n,e,t){"use strict";t.r(e);var o=t(352),r=t.n(o);for(var a in o)["default"].indexOf(a)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(a);e["default"]=r.a},352:function(n,e,t){"use strict";(function(n){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t={props:{current:{type:Number,default:0}},data:function(){return{lang:n.getStorageSync("lang")||"zh",tabList:[{page:"/pages/index/index",icon:"home",text_zh:"首页",text_en:"Home"},{page:"/pages/events/index",icon:"calendar",text_zh:"活动",text_en:"Events"},{page:"/pages/my-join/index",icon:"star",text_zh:"我参与的",text_en:"Joined"},{page:"/pages/profile/index",icon:"account",text_zh:"我的",text_en:"Profile"}]}},mounted:function(){n.$on("lang-change",this.onLangChange)},beforeDestroy:function(){n.$off("lang-change",this.onLangChange)},methods:{switchTab:function(e){e!==this.current&&n.switchTab({url:this.tabList[e].page})},onLangChange:function(n){this.lang=n}}};e.default=t}).call(this,t(2)["default"])},353:function(n,e,t){"use strict";t.r(e);var o=t(354),r=t.n(o);for(var a in o)["default"].indexOf(a)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(a);e["default"]=r.a},354:function(n,e,t){}}]);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/CustomTabbar.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/CustomTabbar-create-component',
    {
        'components/CustomTabbar-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(348))
        })
    },
    [['components/CustomTabbar-create-component']]
]);
