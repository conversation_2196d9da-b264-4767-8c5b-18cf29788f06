(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/LangSwitch"],{355:function(n,t,e){"use strict";e.r(t);var r=e(356),u=e(358);for(var c in u)["default"].indexOf(c)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(c);e(360);var i,a=e(32),o=Object(a["default"])(u["default"],r["render"],r["staticRenderFns"],!1,null,null,null,!1,r["components"],i);o.options.__file="components/LangSwitch.vue",t["default"]=o.exports},356:function(n,t,e){"use strict";e.r(t);var r=e(357);e.d(t,"render",(function(){return r["render"]})),e.d(t,"staticRenderFns",(function(){return r["staticRenderFns"]})),e.d(t,"recyclableRender",(function(){return r["recyclableRender"]})),e.d(t,"components",(function(){return r["components"]}))},357:function(n,t,e){"use strict";var r;e.r(t),e.d(t,"render",(function(){return u})),e.d(t,"staticRenderFns",(function(){return i})),e.d(t,"recyclableRender",(function(){return c})),e.d(t,"components",(function(){return r}));var u=function(){var n=this,t=n.$createElement;n._self._c},c=!1,i=[];u._withStripped=!0},358:function(n,t,e){"use strict";e.r(t);var r=e(359),u=e.n(r);for(var c in r)["default"].indexOf(c)<0&&function(n){e.d(t,n,(function(){return r[n]}))}(c);t["default"]=u.a},359:function(n,t,e){"use strict";(function(n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var e={data:function(){return{lang:n.getStorageSync("lang")||"zh"}},methods:{toggleLang:function(){this.lang="zh"===this.lang?"en":"zh",n.setStorageSync("lang",this.lang),n.$emit("lang-change",this.lang)}}};t.default=e}).call(this,e(2)["default"])},360:function(n,t,e){"use strict";e.r(t);var r=e(361),u=e.n(r);for(var c in r)["default"].indexOf(c)<0&&function(n){e.d(t,n,(function(){return r[n]}))}(c);t["default"]=u.a},361:function(n,t,e){}}]);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/LangSwitch.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/LangSwitch-create-component',
    {
        'components/LangSwitch-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(355))
        })
    },
    [['components/LangSwitch-create-component']]
]);
