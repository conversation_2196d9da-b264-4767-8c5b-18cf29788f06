<view class="custom-tabbar"><u-tabbar vue-id="f89f4888-1" value="{{current}}" fixed="{{true}}" placeholder="{{true}}" safe-area-inset-bottom="{{true}}" data-event-opts="{{[['^change',[['switchTab']]]]}}" bind:change="__e" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{tabList}}" wx:for-item="item" wx:for-index="idx" wx:key="page"><u-tabbar-item vue-id="{{('f89f4888-2-'+idx)+','+('f89f4888-1')}}" icon="{{item.icon}}" text="{{lang==='zh'?item.text_zh:item.text_en}}" bind:__l="__l"></u-tabbar-item></block></u-tabbar></view>