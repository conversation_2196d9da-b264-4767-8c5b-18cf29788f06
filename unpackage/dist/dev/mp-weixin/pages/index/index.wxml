<view class="index-page data-v-57280228"><view class="search-bar data-v-57280228"><u-search vue-id="8dd740cc-1" placeholder="{{lang==='zh'?'搜索往期风采':'Search past events'}}" show-action="{{false}}" value="{{searchValue}}" data-event-opts="{{[['^search',[['onSearch']]],['^input',[['__set_model',['','searchValue','$event',[]]]]]]}}" bind:search="__e" bind:input="__e" class="data-v-57280228" bind:__l="__l"></u-search></view><view class="banner-swiper data-v-57280228"><u-swiper vue-id="8dd740cc-2" list="{{bannerList}}" height="300rpx" border-radius="16rpx" indicator="{{true}}" indicator-active-color="#2979ff" class="data-v-57280228" bind:__l="__l"></u-swiper></view><u-grid class="category-grid data-v-57280228" vue-id="8dd740cc-3" col="{{4}}" border="{{false}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{categoryList}}" wx:for-item="item" wx:for-index="idx" wx:key="name"><u-grid-item bind:click="__e" vue-id="{{('8dd740cc-4-'+idx)+','+('8dd740cc-3')}}" data-event-opts="{{[['^click',[['navigateToCategory',[idx]]]]]}}" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><u-image vue-id="{{('8dd740cc-5-'+idx)+','+('8dd740cc-4-'+idx)}}" src="{{item.image}}" width="96rpx" height="96rpx" shape="circle" class="data-v-57280228" bind:__l="__l"></u-image><text class="cat-title data-v-57280228">{{lang==='zh'?item.name:item.name_en}}</text></u-grid-item></block></u-grid><view class="section-title data-v-57280228">{{lang==='zh'?'推荐活动':'Recommended'}}</view><view class="activity-list data-v-57280228"><block wx:for="{{activityList}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['navigateToEventDetail',['$0'],[[['activityList','id',item.id,'id']]]]]]]}}" class="activity-card data-v-57280228" bindtap="__e"><u-image vue-id="{{'8dd740cc-6-'+__i0__}}" src="{{item.img}}" width="100%" height="250rpx" border-radius="16rpx 16rpx 0 0" class="data-v-57280228" bind:__l="__l"></u-image><view class="card-content data-v-57280228"><view class="card-title data-v-57280228">{{item.title}}</view><view class="card-subtitle data-v-57280228">{{item.time}}</view><view class="card-desc data-v-57280228">{{item.desc}}</view></view></view></block></view><view class="section-title data-v-57280228">{{lang==='zh'?'往期风采':'Past Events'}}</view><view class="activity-list data-v-57280228"><block wx:for="{{pastList}}" wx:for-item="item" wx:for-index="__i1__" wx:key="id"><view data-event-opts="{{[['tap',[['navigateToPastEventDetail',['$0'],[[['pastList','id',item.id,'id']]]]]]]}}" class="activity-card data-v-57280228" bindtap="__e"><u-image vue-id="{{'8dd740cc-7-'+__i1__}}" src="{{item.img}}" width="100%" height="250rpx" border-radius="16rpx 16rpx 0 0" class="data-v-57280228" bind:__l="__l"></u-image><view class="card-content data-v-57280228"><view class="card-title data-v-57280228">{{item.title}}</view><view class="card-desc data-v-57280228">{{item.desc}}</view><view class="card-footer data-v-57280228"><view class="card-date data-v-57280228"><u-icon vue-id="{{'8dd740cc-8-'+__i1__}}" name="calendar" size="24rpx" color="#909399" class="data-v-57280228" bind:__l="__l"></u-icon><text class="data-v-57280228">{{item.date}}</text></view><view class="card-views data-v-57280228"><u-icon vue-id="{{'8dd740cc-9-'+__i1__}}" name="eye" size="24rpx" color="#909399" class="data-v-57280228" bind:__l="__l"></u-icon><text class="data-v-57280228">{{item.views}}</text></view></view></view></view></block><u-loadmore vue-id="8dd740cc-10" status="{{loadMoreStatus}}" loading-text="{{lang==='zh'?'正在加载...':'Loading...'}}" loadmore-text="{{lang==='zh'?'点击加载更多':'Load more'}}" nomore-text="{{lang==='zh'?'没有更多了':'No more data'}}" margin-top="20" margin-bottom="20" data-event-opts="{{[['^loadmore',[['loadMorePast']]]]}}" bind:loadmore="__e" class="data-v-57280228" bind:__l="__l"></u-loadmore></view><u-popup vue-id="8dd740cc-11" safeAreaInsetBottom="{{false}}" round="16" show="{{showPopup}}" mode="center" mask-close-able="{{true}}" data-event-opts="{{[['^close',[['e0']]]]}}" bind:close="__e" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><view class="poster-popup data-v-57280228"><u-image vue-id="{{('8dd740cc-12')+','+('8dd740cc-11')}}" src="{{popupImage||'https://picsum.photos/100/100?random=10'}}" width="500rpx" height="750rpx" fade="{{true}}" duration="450" data-event-opts="{{[['^click',[['onPopupClick']]]]}}" bind:click="__e" class="data-v-57280228" bind:__l="__l"></u-image></view></u-popup><custom-tabbar vue-id="8dd740cc-13" current="{{0}}" class="data-v-57280228" bind:__l="__l"></custom-tabbar><lang-switch vue-id="8dd740cc-14" class="data-v-57280228" bind:__l="__l"></lang-switch></view>