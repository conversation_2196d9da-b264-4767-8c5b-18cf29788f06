
.poster-popup.data-v-57280228 {
  /* 消除 u-popup 内部的默认 padding */
  line-height: 1;
}
.search-bar.data-v-57280228 {
  margin: 24rpx;
  margin-top: 0rpx;
  padding-top: 24rpx;
  box-sizing: border-box;
}
.index-page.data-v-57280228 {
  min-height: 100vh;
  padding-bottom: 0;
}
.banner-swiper.data-v-57280228 {
  margin: 24rpx;
}
.category-grid.data-v-57280228 {
  margin-top: 24rpx;
}
.cat-title.data-v-57280228 {
  font-size: 24rpx;
  margin-top: 8rpx;
  color: #333;
}
.section-title.data-v-57280228 {
  font-size: 32rpx;
  font-weight: bold;
  margin: 32rpx 24rpx 16rpx 24rpx;
  color: #222;
}
.activity-list.data-v-57280228 {
  padding: 0 24rpx;
}
.activity-card.data-v-57280228 {
  margin-bottom: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.card-content.data-v-57280228 {
  padding: 24rpx;
}
.card-title.data-v-57280228 {
  font-size: 30rpx;
  font-weight: bold;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.card-subtitle.data-v-57280228 {
  font-size: 24rpx;
  color: #909399;
  margin-top: 8rpx;
}
.card-desc.data-v-57280228 {
  font-size: 26rpx;
  color: #606266;
  margin-top: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.card-footer.data-v-57280228 {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #eee;
}
.card-date.data-v-57280228, .card-views.data-v-57280228 {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #909399;
}
.card-date text.data-v-57280228, .card-views text.data-v-57280228 {
  margin-left: 6rpx;
}
.popup-content.data-v-57280228 {
  padding: 48rpx 32rpx;
  text-align: center;
}

