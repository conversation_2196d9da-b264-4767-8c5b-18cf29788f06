<view class="signup-manage-page data-v-28aa4093"><view class="tip-bar data-v-28aa4093"><u-icon vue-id="26c35a5a-1" name="info-circle" size="28" color="#4285f4" class="data-v-28aa4093" bind:__l="__l"></u-icon><text class="tip-text data-v-28aa4093">{{lang==='zh'?'报名资料用于活动报名，可添加多个资料':'Registration info for activities, you can add multiple profiles'}}</text></view><view class="profile-list data-v-28aa4093"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['editProfile',[index]]]]]}}" class="{{['profile-item','data-v-28aa4093',(item.$orig.isDefault)?'default-profile':'']}}" bindtap="__e"><view class="profile-main data-v-28aa4093"><view class="profile-info data-v-28aa4093"><view class="profile-name data-v-28aa4093">{{''+item.$orig.name+''}}<block wx:if="{{item.$orig.isDefault}}"><text class="default-tag data-v-28aa4093">{{lang==='zh'?'默认':'Default'}}</text></block></view><view class="profile-phone data-v-28aa4093">{{item.$orig.phone}}</view></view><view class="profile-actions data-v-28aa4093"><view data-event-opts="{{[['tap',[['editProfile',[index]]]]]}}" class="action-btn edit-btn data-v-28aa4093" catchtap="__e"><u-icon vue-id="{{'26c35a5a-2-'+index}}" name="edit-pen" size="24" color="#4285f4" class="data-v-28aa4093" bind:__l="__l"></u-icon></view><view data-event-opts="{{[['tap',[['confirmDelete',[index]]]]]}}" class="action-btn delete-btn data-v-28aa4093" catchtap="__e"><u-icon vue-id="{{'26c35a5a-3-'+index}}" name="trash" size="24" color="#ff5252" class="data-v-28aa4093" bind:__l="__l"></u-icon></view></view></view><view class="profile-detail data-v-28aa4093"><block wx:if="{{item.$orig.idCard}}"><view class="detail-item data-v-28aa4093"><text class="detail-label data-v-28aa4093">{{(lang==='zh'?'身份证':'ID Card')+":"}}</text><text class="detail-value data-v-28aa4093">{{item.m0}}</text></view></block><block wx:if="{{item.$orig.email}}"><view class="detail-item data-v-28aa4093"><text class="detail-label data-v-28aa4093">{{(lang==='zh'?'邮箱':'Email')+":"}}</text><text class="detail-value data-v-28aa4093">{{item.$orig.email}}</text></view></block><block wx:if="{{item.$orig.school}}"><view class="detail-item data-v-28aa4093"><text class="detail-label data-v-28aa4093">{{(lang==='zh'?'学校':'School')+":"}}</text><text class="detail-value data-v-28aa4093">{{item.$orig.school}}</text></view></block><block wx:if="{{item.$orig.address}}"><view class="detail-item data-v-28aa4093"><text class="detail-label data-v-28aa4093">{{(lang==='zh'?'地址':'Address')+":"}}</text><text class="detail-value data-v-28aa4093">{{item.$orig.address}}</text></view></block></view></view></block></view><view data-event-opts="{{[['tap',[['addProfile',['$event']]]]]}}" class="add-btn data-v-28aa4093" bindtap="__e"><text class="data-v-28aa4093">{{lang==='zh'?'添加报名资料':'Add Profile'}}</text></view><u-popup vue-id="26c35a5a-4" show="{{showForm}}" mode="bottom" safeAreaInsetBottom="{{true}}" round="16" data-event-opts="{{[['^close',[['closeForm']]]]}}" bind:close="__e" class="data-v-28aa4093" bind:__l="__l" vue-slots="{{['default']}}"><view class="form-popup data-v-28aa4093"><view class="form-header data-v-28aa4093"><text class="form-title data-v-28aa4093">{{isEdit?lang==='zh'?'编辑资料':'Edit Profile':lang==='zh'?'添加资料':'Add Profile'}}</text><u-icon vue-id="{{('26c35a5a-5')+','+('26c35a5a-4')}}" name="close" size="24" color="#999" data-event-opts="{{[['^click',[['closeForm']]]]}}" bind:click="__e" class="data-v-28aa4093" bind:__l="__l"></u-icon></view><scroll-view class="form-scroll data-v-28aa4093" scroll-y="{{true}}"><view class="form-content data-v-28aa4093"><view class="form-item data-v-28aa4093"><text class="form-label required data-v-28aa4093">{{lang==='zh'?'姓名':'Name'}}</text><u-input bind:input="__e" vue-id="{{('26c35a5a-6')+','+('26c35a5a-4')}}" placeholder="{{lang==='zh'?'请输入姓名':'Enter name'}}" border="bottom" value="{{formData.name}}" data-event-opts="{{[['^input',[['__set_model',['$0','name','$event',[]],['formData']]]]]}}" class="data-v-28aa4093" bind:__l="__l"></u-input></view><view class="form-item data-v-28aa4093"><text class="form-label required data-v-28aa4093">{{lang==='zh'?'手机号':'Phone'}}</text><u-input bind:input="__e" vue-id="{{('26c35a5a-7')+','+('26c35a5a-4')}}" placeholder="{{lang==='zh'?'请输入手机号':'Enter phone'}}" border="bottom" type="number" value="{{formData.phone}}" data-event-opts="{{[['^input',[['__set_model',['$0','phone','$event',[]],['formData']]]]]}}" class="data-v-28aa4093" bind:__l="__l"></u-input></view><view class="form-item data-v-28aa4093"><text class="form-label data-v-28aa4093">{{lang==='zh'?'身份证号':'ID Card'}}</text><u-input bind:input="__e" vue-id="{{('26c35a5a-8')+','+('26c35a5a-4')}}" placeholder="{{lang==='zh'?'请输入身份证号':'Enter ID card'}}" border="bottom" value="{{formData.idCard}}" data-event-opts="{{[['^input',[['__set_model',['$0','idCard','$event',[]],['formData']]]]]}}" class="data-v-28aa4093" bind:__l="__l"></u-input></view><view class="form-item data-v-28aa4093"><text class="form-label data-v-28aa4093">{{lang==='zh'?'邮箱':'Email'}}</text><u-input bind:input="__e" vue-id="{{('26c35a5a-9')+','+('26c35a5a-4')}}" placeholder="{{lang==='zh'?'请输入邮箱':'Enter email'}}" border="bottom" value="{{formData.email}}" data-event-opts="{{[['^input',[['__set_model',['$0','email','$event',[]],['formData']]]]]}}" class="data-v-28aa4093" bind:__l="__l"></u-input></view><view class="form-item data-v-28aa4093"><text class="form-label data-v-28aa4093">{{lang==='zh'?'学校':'School'}}</text><u-input bind:input="__e" vue-id="{{('26c35a5a-10')+','+('26c35a5a-4')}}" placeholder="{{lang==='zh'?'请输入学校':'Enter school'}}" border="bottom" value="{{formData.school}}" data-event-opts="{{[['^input',[['__set_model',['$0','school','$event',[]],['formData']]]]]}}" class="data-v-28aa4093" bind:__l="__l"></u-input></view><view class="form-item data-v-28aa4093"><text class="form-label data-v-28aa4093">{{lang==='zh'?'地址':'Address'}}</text><u-input bind:input="__e" vue-id="{{('26c35a5a-11')+','+('26c35a5a-4')}}" placeholder="{{lang==='zh'?'请输入地址':'Enter address'}}" border="bottom" value="{{formData.address}}" data-event-opts="{{[['^input',[['__set_model',['$0','address','$event',[]],['formData']]]]]}}" class="data-v-28aa4093" bind:__l="__l"></u-input></view><view class="form-item data-v-28aa4093"><view class="checkbox-item data-v-28aa4093"><u-checkbox vue-id="{{('26c35a5a-12')+','+('26c35a5a-4')}}" disabled="{{isDefaultDisabled}}" activeColor="#4285f4" value="{{formData.isDefault}}" data-event-opts="{{[['^change',[['onDefaultChange']]],['^input',[['__set_model',['$0','isDefault','$event',[]],['formData']]]]]}}" bind:change="__e" bind:input="__e" class="data-v-28aa4093" bind:__l="__l"></u-checkbox><text class="checkbox-text data-v-28aa4093">{{lang==='zh'?'设为默认资料':'Set as default'}}</text></view><text class="debug-text data-v-28aa4093" style="font-size:24rpx;color:#999;margin-top:10rpx;">{{'调试: isDefault = '+formData.isDefault+''}}</text></view></view></scroll-view><view class="form-footer data-v-28aa4093"><u-button vue-id="{{('26c35a5a-13')+','+('26c35a5a-4')}}" type="primary" data-event-opts="{{[['^click',[['saveProfile']]]]}}" bind:click="__e" class="data-v-28aa4093" bind:__l="__l" vue-slots="{{['default']}}">{{''+(lang==='zh'?'保存':'Save')+''}}</u-button></view></view></u-popup><lang-switch vue-id="26c35a5a-14" class="data-v-28aa4093" bind:__l="__l"></lang-switch></view>