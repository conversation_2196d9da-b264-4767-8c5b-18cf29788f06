(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/signup/manage"],{259:function(e,t,n){"use strict";(function(e,t){var i=n(4);n(26);i(n(25));var o=i(n(260));e.__webpack_require_UNI_MP_PLUGIN__=n,t(o.default)}).call(this,n(1)["default"],n(2)["createPage"])},260:function(e,t,n){"use strict";n.r(t);var i=n(261),o=n(263);for(var s in o)["default"].indexOf(s)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(s);n(265);var r,a=n(32),u=Object(a["default"])(o["default"],i["render"],i["staticRenderFns"],!1,null,"28aa4093",null,!1,i["components"],r);u.options.__file="pages/signup/manage.vue",t["default"]=u.exports},261:function(e,t,n){"use strict";n.r(t);var i=n(262);n.d(t,"render",(function(){return i["render"]})),n.d(t,"staticRenderFns",(function(){return i["staticRenderFns"]})),n.d(t,"recyclableRender",(function(){return i["recyclableRender"]})),n.d(t,"components",(function(){return i["components"]}))},262:function(e,t,n){"use strict";var i;n.r(t),n.d(t,"render",(function(){return o})),n.d(t,"staticRenderFns",(function(){return r})),n.d(t,"recyclableRender",(function(){return s})),n.d(t,"components",(function(){return i}));try{i={uIcon:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(n.bind(null,323))},uPopup:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-popup/u-popup")]).then(n.bind(null,340))},uInput:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-input/u-input")]).then(n.bind(null,396))},uCheckbox:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-checkbox/u-checkbox")]).then(n.bind(null,428))},uButton:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-button/u-button")]).then(n.bind(null,378))}}}catch(a){if(-1===a.message.indexOf("Cannot find module")||-1===a.message.indexOf(".vue"))throw a;console.error(a.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var o=function(){var e=this,t=e.$createElement,n=(e._self._c,e.__map(e.profileList,(function(t,n){var i=e.__get_orig(t),o=t.idCard?e.formatIdCard(t.idCard):null;return{$orig:i,m0:o}})));e.$mp.data=Object.assign({},{$root:{l0:n}})},s=!1,r=[];o._withStripped=!0},263:function(e,t,n){"use strict";n.r(t);var i=n(264),o=n.n(i);for(var s in i)["default"].indexOf(s)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(s);t["default"]=o.a},264:function(e,t,n){"use strict";(function(e){var i=n(4);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=i(n(11));function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function r(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){(0,o.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var a=function(){n.e("components/LangSwitch").then(function(){return resolve(n(355))}.bind(null,n)).catch(n.oe)},u={components:{LangSwitch:a},data:function(){return{lang:"zh",profileList:[{name:"张三",phone:"13812345678",idCard:"******************",email:"<EMAIL>",school:"深圳大学",address:"广东省深圳市南山区科技园南区",isDefault:!0},{name:"李四",phone:"13987654321",idCard:"******************",email:"<EMAIL>",school:"华南理工大学",address:"广东省广州市天河区五山路",isDefault:!1},{name:"王五",phone:"13500001111",idCard:"******************",email:"<EMAIL>",school:"北京大学",address:"北京市海淀区颐和园路5号",isDefault:!1},{name:"赵六",phone:"13600002222",idCard:"******************",email:"<EMAIL>",school:"清华大学",address:"北京市海淀区清华园1号",isDefault:!1},{name:"钱七",phone:"13700003333",idCard:"******************",email:"<EMAIL>",school:"复旦大学",address:"上海市杨浦区邯郸路220号",isDefault:!1},{name:"孙八",phone:"13800004444",idCard:"******************",email:"<EMAIL>",school:"浙江大学",address:"浙江省杭州市西湖区余杭塘路866号",isDefault:!1},{name:"周九",phone:"13900005555",idCard:"******************",email:"<EMAIL>",school:"南京大学",address:"江苏省南京市栖霞区仙林大道163号",isDefault:!1}],showForm:!1,isEdit:!1,currentIndex:-1,formData:{name:"",phone:"",idCard:"",email:"",school:"",address:"",isDefault:!1},isDefaultDisabled:!1}},computed:{hasDefaultProfile:function(){return this.profileList.some((function(e){return e.isDefault}))}},onReady:function(){},mounted:function(){var t=e.getStorageSync("lang")||"zh";this.lang=t,e.$on("lang-change",this.onLangChange),this.setNavTitle(),this.getProfileList()},beforeDestroy:function(){e.$off("lang-change",this.onLangChange)},methods:{onLangChange:function(e){this.lang=e,this.setNavTitle()},setNavTitle:function(){var t="zh"===this.lang?"报名资料管理":"Registration Info";e.setNavigationBarTitle({title:t})},getProfileList:function(){console.log("获取报名资料列表",this.profileList.length)},formatIdCard:function(e){return e?e.substring(0,4)+"************"+e.substring(e.length-4):""},addProfile:function(){this.isEdit=!1,this.currentIndex=-1;var e=this.profileList.some((function(e){return e.isDefault}));this.formData={name:"",phone:"",idCard:"",email:"",school:"",address:"",isDefault:!e},this.isDefaultDisabled=!e,this.showForm=!0},editProfile:function(e){this.isEdit=!0,this.currentIndex=e;var t=this.profileList[e];this.formData=r({},t),this.isDefaultDisabled=t.isDefault,this.showForm=!0},closeForm:function(){this.showForm=!1},saveProfile:function(){var t=this;return this.formData.name?this.formData.phone?(e.showLoading({title:"zh"===this.lang?"保存中...":"Saving..."}),this.formData.isDefault?this.profileList.forEach((function(e){e.isDefault=!1})):this.isEdit&&this.profileList[this.currentIndex].isDefault?this.formData.isDefault=!0:this.profileList.some((function(e){return e.isDefault}))||0!==this.profileList.length&&!this.isEdit||(this.formData.isDefault=!0),this.isEdit?this.profileList[this.currentIndex]=r({},this.formData):this.profileList.push(r({},this.formData)),void setTimeout((function(){e.hideLoading(),e.showToast({title:"zh"===t.lang?"保存成功":"Saved successfully",icon:"success"}),t.closeForm()}),500)):e.showToast({title:"zh"===this.lang?"请输入手机号":"Please enter phone",icon:"none"}):e.showToast({title:"zh"===this.lang?"请输入姓名":"Please enter name",icon:"none"})},confirmDelete:function(t){var n=this;e.showModal({title:"zh"===this.lang?"确认删除":"Confirm Delete",content:"zh"===this.lang?"确定要删除该报名资料吗？":"Are you sure you want to delete this profile?",success:function(e){e.confirm&&n.deleteProfile(t)}})},deleteProfile:function(t){var n=this,i=this.profileList[t].isDefault;this.profileList.splice(t,1),i&&this.profileList.length>0&&(this.profileList[0].isDefault=!0),setTimeout((function(){e.showToast({title:"zh"===n.lang?"删除成功":"Deleted successfully",icon:"success"})}),300)}}};t.default=u}).call(this,n(2)["default"])},265:function(e,t,n){"use strict";n.r(t);var i=n(266),o=n.n(i);for(var s in i)["default"].indexOf(s)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(s);t["default"]=o.a},266:function(e,t,n){}},[[259,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/signup/manage.js.map