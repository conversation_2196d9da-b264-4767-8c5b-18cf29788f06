(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/signup/manage"],{259:function(e,t,n){"use strict";(function(e,t){var o=n(4);n(26);o(n(25));var r=o(n(260));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n(1)["default"],n(2)["createPage"])},260:function(e,t,n){"use strict";n.r(t);var o=n(261),r=n(263);for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(a);n(265);var i,s=n(32),u=Object(s["default"])(r["default"],o["render"],o["staticRenderFns"],!1,null,"28aa4093",null,!1,o["components"],i);u.options.__file="pages/signup/manage.vue",t["default"]=u.exports},261:function(e,t,n){"use strict";n.r(t);var o=n(262);n.d(t,"render",(function(){return o["render"]})),n.d(t,"staticRenderFns",(function(){return o["staticRenderFns"]})),n.d(t,"recyclableRender",(function(){return o["recyclableRender"]})),n.d(t,"components",(function(){return o["components"]}))},262:function(e,t,n){"use strict";var o;n.r(t),n.d(t,"render",(function(){return r})),n.d(t,"staticRenderFns",(function(){return i})),n.d(t,"recyclableRender",(function(){return a})),n.d(t,"components",(function(){return o}));try{o={uIcon:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(n.bind(null,323))},uLoadmore:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-loadmore/u-loadmore")]).then(n.bind(null,332))},uPopup:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-popup/u-popup")]).then(n.bind(null,340))},uInput:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-input/u-input")]).then(n.bind(null,396))},uCheckbox:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-checkbox/u-checkbox")]).then(n.bind(null,428))},uButton:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-button/u-button")]).then(n.bind(null,378))}}}catch(s){if(-1===s.message.indexOf("Cannot find module")||-1===s.message.indexOf(".vue"))throw s;console.error(s.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var r=function(){var e=this,t=e.$createElement,n=(e._self._c,e.__map(e.profileList,(function(t,n){var o=e.__get_orig(t),r=t.idCard?e.formatIdCard(t.idCard):null;return{$orig:o,m0:r}}))),o=e.profileList.length;e.$mp.data=Object.assign({},{$root:{l0:n,g0:o}})},a=!1,i=[];r._withStripped=!0},263:function(e,t,n){"use strict";n.r(t);var o=n(264),r=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=r.a},264:function(e,t,n){"use strict";(function(e){var o=n(4);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n(56)),a=o(n(18)),i=o(n(58)),s=n(208),u=function(){n.e("components/LangSwitch").then(function(){return resolve(n(355))}.bind(null,n)).catch(n.oe)},l={components:{LangSwitch:u},data:function(){return{lang:"zh",profileList:[],showForm:!1,isEdit:!1,currentIndex:-1,formData:{id:null,name:"",phone:"",idCard:"",email:"",school:"",address:"",isDefault:!1},isDefaultDisabled:!1,currentPage:1,pageSize:10,total:0,loading:!1,hasMore:!0,loadMoreStatus:"loadmore"}},computed:{hasDefaultProfile:function(){return this.profileList.some((function(e){return e.isDefault}))}},onReady:function(){},mounted:function(){var t=e.getStorageSync("lang")||"zh";this.lang=t,e.$on("lang-change",this.onLangChange),this.setNavTitle(),this.getProfileList()},beforeDestroy:function(){e.$off("lang-change",this.onLangChange)},methods:{onLangChange:function(e){this.lang=e,this.setNavTitle()},setNavTitle:function(){var t="zh"===this.lang?"报名资料管理":"Registration Info";e.setNavigationBarTitle({title:t})},getProfileList:function(){var t=arguments,n=this;return(0,i.default)(r.default.mark((function o(){var i,u,l;return r.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:return i=!(t.length>0&&void 0!==t[0])||t[0],o.prev=1,i?(n.currentPage=1,n.profileList=[],e.showLoading({title:"zh"===n.lang?"加载中...":"Loading..."})):n.loadMoreStatus="loading",o.next=5,(0,s.getUserInfoRegList)({pg:n.currentPage,size:n.pageSize});case 5:u=o.sent,console.log("获取报名资料列表响应:",u),u&&0===u.code&&u.data?(l=u.data.map((function(e){return{id:e.id,name:e.userName||"",phone:e.mobile||"",idCard:e.idCard||"",email:e.email||"",school:e.school||"",address:e.address||"",isDefault:1===e.isDefault}})),n.profileList=i?l:[].concat((0,a.default)(n.profileList),(0,a.default)(l)),n.total=u.count||0,l.length<n.pageSize?(n.loadMoreStatus="nomore",n.hasMore=!1):(n.loadMoreStatus="loadmore",n.hasMore=!0)):(console.error("获取报名资料列表失败:",u),e.showToast({title:"zh"===n.lang?"获取数据失败":"Failed to load data",icon:"none"}),n.loadMoreStatus="loadmore"),o.next=15;break;case 10:o.prev=10,o.t0=o["catch"](1),console.error("获取报名资料列表异常:",o.t0),e.showToast({title:"zh"===n.lang?"网络错误":"Network error",icon:"none"}),n.loadMoreStatus="loadmore";case 15:return o.prev=15,i&&e.hideLoading(),o.finish(15);case 18:case"end":return o.stop()}}),o,null,[[1,10,15,18]])})))()},loadMore:function(){var e=this;return(0,i.default)(r.default.mark((function t(){return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("loading"!==e.loadMoreStatus&&e.hasMore){t.next=2;break}return t.abrupt("return");case 2:return e.currentPage++,t.next=5,e.getProfileList(!1);case 5:case"end":return t.stop()}}),t)})))()},formatIdCard:function(e){return e?e.substring(0,4)+"************"+e.substring(e.length-4):""},addProfile:function(){this.isEdit=!1,this.currentIndex=-1;var e=this.profileList.some((function(e){return e.isDefault}));this.formData={id:null,name:"",phone:"",idCard:"",email:"",school:"",address:"",isDefault:!e},this.isDefaultDisabled=!e,this.showForm=!0},editProfile:function(e){this.isEdit=!0,this.currentIndex=e;var t=this.profileList[e];this.formData={id:t.id,name:t.name||"",phone:t.phone||"",idCard:t.idCard||"",email:t.email||"",school:t.school||"",address:t.address||"",isDefault:Boolean(t.isDefault)},this.isDefaultDisabled=t.isDefault,this.showForm=!0},closeForm:function(){this.showForm=!1},saveProfile:function(){var t=this;return(0,i.default)(r.default.mark((function n(){var o,a,i;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.formData.name){n.next=2;break}return n.abrupt("return",e.showToast({title:"zh"===t.lang?"请输入姓名":"Please enter name",icon:"none"}));case 2:if(t.formData.phone){n.next=4;break}return n.abrupt("return",e.showToast({title:"zh"===t.lang?"请输入手机号":"Please enter phone",icon:"none"}));case 4:if(e.showLoading({title:"zh"===t.lang?"保存中...":"Saving..."}),n.prev=5,t.formData.isDefault?t.profileList.forEach((function(e){e.isDefault=!1})):t.isEdit&&t.profileList[t.currentIndex].isDefault?t.formData.isDefault=!0:t.profileList.some((function(e){return e.isDefault}))||0!==t.profileList.length&&!t.isEdit||(t.formData.isDefault=!0),o={userName:t.formData.name,mobile:t.formData.phone,idCard:t.formData.idCard||"",email:t.formData.email||"",school:t.formData.school||"",address:t.formData.address||"",isDefault:t.formData.isDefault?1:0},!t.isEdit){n.next=15;break}return o.id=t.formData.id,n.next=12,(0,s.updateUserInfoReg)(o);case 12:a=n.sent,n.next=18;break;case 15:return n.next=17,(0,s.addUserInfoReg)(o);case 17:a=n.sent;case 18:a&&0===a.code?(e.showToast({title:"zh"===t.lang?"保存成功":"Saved successfully",icon:"success"}),t.closeForm(),t.getProfileList(!0)):e.showToast({title:(null===(i=a)||void 0===i?void 0:i.msg)||("zh"===t.lang?"保存失败":"Save failed"),icon:"none"}),n.next=25;break;case 21:n.prev=21,n.t0=n["catch"](5),console.error("保存报名资料异常:",n.t0),e.showToast({title:"zh"===t.lang?"网络错误":"Network error",icon:"none"});case 25:return n.prev=25,e.hideLoading(),n.finish(25);case 28:case"end":return n.stop()}}),n,null,[[5,21,25,28]])})))()},confirmDelete:function(t){var n=this;e.showModal({title:"zh"===this.lang?"确认删除":"Confirm Delete",content:"zh"===this.lang?"确定要删除该报名资料吗？":"Are you sure you want to delete this profile?",success:function(e){e.confirm&&n.deleteProfile(t)}})},deleteProfile:function(t){var n=this;return(0,i.default)(r.default.mark((function o(){var a,i;return r.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:return a=n.profileList[t],o.prev=1,e.showLoading({title:"zh"===n.lang?"删除中...":"Deleting..."}),o.next=5,(0,s.delUserInfoReg)({id:a.id});case 5:i=o.sent,i&&0===i.code?(e.showToast({title:"zh"===n.lang?"删除成功":"Deleted successfully",icon:"success"}),n.getProfileList(!0)):e.showToast({title:(null===i||void 0===i?void 0:i.msg)||("zh"===n.lang?"删除失败":"Delete failed"),icon:"none"}),o.next=13;break;case 9:o.prev=9,o.t0=o["catch"](1),console.error("删除报名资料异常:",o.t0),e.showToast({title:"zh"===n.lang?"网络错误":"Network error",icon:"none"});case 13:return o.prev=13,e.hideLoading(),o.finish(13);case 16:case"end":return o.stop()}}),o,null,[[1,9,13,16]])})))()}}};t.default=l}).call(this,n(2)["default"])},265:function(e,t,n){"use strict";n.r(t);var o=n(266),r=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=r.a},266:function(e,t,n){}},[[259,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/signup/manage.js.map