(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/signup/manage"],{259:function(e,n,t){"use strict";(function(e,n){var o=t(4);t(26);o(t(25));var i=o(t(260));e.__webpack_require_UNI_MP_PLUGIN__=t,n(i.default)}).call(this,t(1)["default"],t(2)["createPage"])},260:function(e,n,t){"use strict";t.r(n);var o=t(261),i=t(263);for(var a in i)["default"].indexOf(a)<0&&function(e){t.d(n,e,(function(){return i[e]}))}(a);t(265);var r,s=t(32),l=Object(s["default"])(i["default"],o["render"],o["staticRenderFns"],!1,null,"28aa4093",null,!1,o["components"],r);l.options.__file="pages/signup/manage.vue",n["default"]=l.exports},261:function(e,n,t){"use strict";t.r(n);var o=t(262);t.d(n,"render",(function(){return o["render"]})),t.d(n,"staticRenderFns",(function(){return o["staticRenderFns"]})),t.d(n,"recyclableRender",(function(){return o["recyclableRender"]})),t.d(n,"components",(function(){return o["components"]}))},262:function(e,n,t){"use strict";var o;t.r(n),t.d(n,"render",(function(){return i})),t.d(n,"staticRenderFns",(function(){return r})),t.d(n,"recyclableRender",(function(){return a})),t.d(n,"components",(function(){return o}));try{o={uIcon:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(t.bind(null,323))},uPopup:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-popup/u-popup")]).then(t.bind(null,340))},uInput:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-input/u-input")]).then(t.bind(null,396))},uCheckbox:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-checkbox/u-checkbox")]).then(t.bind(null,428))},uButton:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-button/u-button")]).then(t.bind(null,378))}}}catch(s){if(-1===s.message.indexOf("Cannot find module")||-1===s.message.indexOf(".vue"))throw s;console.error(s.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var i=function(){var e=this,n=e.$createElement,t=(e._self._c,e.__map(e.profileList,(function(n,t){var o=e.__get_orig(n),i=n.idCard?e.formatIdCard(n.idCard):null;return{$orig:o,m0:i}})));e.$mp.data=Object.assign({},{$root:{l0:t}})},a=!1,r=[];i._withStripped=!0},263:function(e,n,t){"use strict";t.r(n);var o=t(264),i=t.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(a);n["default"]=i.a},264:function(e,n,t){"use strict";(function(e){var o=t(4);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i=o(t(56)),a=o(t(58)),r=t(208),s=function(){t.e("components/LangSwitch").then(function(){return resolve(t(355))}.bind(null,t)).catch(t.oe)},l={components:{LangSwitch:s},data:function(){return{lang:"zh",profileList:[],showForm:!1,isEdit:!1,currentIndex:-1,formData:{id:null,name:"",phone:"",idCard:"",email:"",school:"",address:"",isDefault:!1},isDefaultDisabled:!1}},computed:{hasDefaultProfile:function(){return this.profileList.some((function(e){return e.isDefault}))}},onReady:function(){},mounted:function(){var n=e.getStorageSync("lang")||"zh";this.lang=n,e.$on("lang-change",this.onLangChange),this.setNavTitle(),this.getProfileList()},beforeDestroy:function(){e.$off("lang-change",this.onLangChange)},methods:{onLangChange:function(e){this.lang=e,this.setNavTitle()},setNavTitle:function(){var n="zh"===this.lang?"报名资料管理":"Registration Info";e.setNavigationBarTitle({title:n})},getProfileList:function(){var n=this;return(0,a.default)(i.default.mark((function t(){var o;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.showLoading({title:"zh"===n.lang?"加载中...":"Loading..."}),t.next=4,(0,r.getUserInfoRegList)({pg:1,size:100});case 4:o=t.sent,console.log("获取报名资料列表响应:",o),o&&0===o.code&&o.data?n.profileList=o.data.map((function(e){return{id:e.id,name:e.userName||"",phone:e.mobile||"",idCard:e.idCard||"",email:e.email||"",school:e.school||"",address:e.address||"",isDefault:1===e.isDefault}})):(console.error("获取报名资料列表失败:",o),e.showToast({title:"zh"===n.lang?"获取数据失败":"Failed to load data",icon:"none"})),t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](0),console.error("获取报名资料列表异常:",t.t0),e.showToast({title:"zh"===n.lang?"网络错误":"Network error",icon:"none"});case 13:return t.prev=13,e.hideLoading(),t.finish(13);case 16:case"end":return t.stop()}}),t,null,[[0,9,13,16]])})))()},formatIdCard:function(e){return e?e.substring(0,4)+"************"+e.substring(e.length-4):""},addProfile:function(){this.isEdit=!1,this.currentIndex=-1;var e=this.profileList.some((function(e){return e.isDefault}));this.formData={id:null,name:"",phone:"",idCard:"",email:"",school:"",address:"",isDefault:!e},this.isDefaultDisabled=!e,this.showForm=!0},editProfile:function(e){this.isEdit=!0,this.currentIndex=e;var n=this.profileList[e];this.formData={id:n.id,name:n.name||"",phone:n.phone||"",idCard:n.idCard||"",email:n.email||"",school:n.school||"",address:n.address||"",isDefault:Boolean(n.isDefault)},console.log("编辑资料 - formData:",this.formData),this.isDefaultDisabled=n.isDefault,this.showForm=!0},closeForm:function(){this.showForm=!1},onDefaultChange:function(e){console.log("复选框变化:",e),this.formData.isDefault=e},saveProfile:function(){var n=this;return(0,a.default)(i.default.mark((function t(){var o,a,s;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n.formData.name){t.next=2;break}return t.abrupt("return",e.showToast({title:"zh"===n.lang?"请输入姓名":"Please enter name",icon:"none"}));case 2:if(n.formData.phone){t.next=4;break}return t.abrupt("return",e.showToast({title:"zh"===n.lang?"请输入手机号":"Please enter phone",icon:"none"}));case 4:if(e.showLoading({title:"zh"===n.lang?"保存中...":"Saving..."}),t.prev=5,n.formData.isDefault?n.profileList.forEach((function(e){e.isDefault=!1})):n.isEdit&&n.profileList[n.currentIndex].isDefault?n.formData.isDefault=!0:n.profileList.some((function(e){return e.isDefault}))||0!==n.profileList.length&&!n.isEdit||(n.formData.isDefault=!0),o={userName:n.formData.name,mobile:n.formData.phone,idCard:n.formData.idCard||"",email:n.formData.email||"",school:n.formData.school||"",address:n.formData.address||"",isDefault:n.formData.isDefault?1:0},console.log("保存前 - formData.isDefault:",n.formData.isDefault),console.log("保存前 - apiData.isDefault:",o.isDefault),!n.isEdit){t.next=17;break}return o.id=n.formData.id,t.next=14,(0,r.updateUserInfoReg)(o);case 14:a=t.sent,t.next=20;break;case 17:return t.next=19,(0,r.addUserInfoReg)(o);case 19:a=t.sent;case 20:console.log("保存报名资料响应:",a),a&&0===a.code?(e.showToast({title:"zh"===n.lang?"保存成功":"Saved successfully",icon:"success"}),n.closeForm(),n.getProfileList()):e.showToast({title:(null===(s=a)||void 0===s?void 0:s.msg)||("zh"===n.lang?"保存失败":"Save failed"),icon:"none"}),t.next=28;break;case 24:t.prev=24,t.t0=t["catch"](5),console.error("保存报名资料异常:",t.t0),e.showToast({title:"zh"===n.lang?"网络错误":"Network error",icon:"none"});case 28:return t.prev=28,e.hideLoading(),t.finish(28);case 31:case"end":return t.stop()}}),t,null,[[5,24,28,31]])})))()},confirmDelete:function(n){var t=this;e.showModal({title:"zh"===this.lang?"确认删除":"Confirm Delete",content:"zh"===this.lang?"确定要删除该报名资料吗？":"Are you sure you want to delete this profile?",success:function(e){e.confirm&&t.deleteProfile(n)}})},deleteProfile:function(n){var t=this;return(0,a.default)(i.default.mark((function o(){var a,s;return i.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:return a=t.profileList[n],o.prev=1,e.showLoading({title:"zh"===t.lang?"删除中...":"Deleting..."}),o.next=5,(0,r.delUserInfoReg)({id:a.id});case 5:s=o.sent,console.log("删除报名资料响应:",s),s&&0===s.code?(e.showToast({title:"zh"===t.lang?"删除成功":"Deleted successfully",icon:"success"}),t.getProfileList()):e.showToast({title:(null===s||void 0===s?void 0:s.msg)||("zh"===t.lang?"删除失败":"Delete failed"),icon:"none"}),o.next=14;break;case 10:o.prev=10,o.t0=o["catch"](1),console.error("删除报名资料异常:",o.t0),e.showToast({title:"zh"===t.lang?"网络错误":"Network error",icon:"none"});case 14:return o.prev=14,e.hideLoading(),o.finish(14);case 17:case"end":return o.stop()}}),o,null,[[1,10,14,17]])})))()}}};n.default=l}).call(this,t(2)["default"])},265:function(e,n,t){"use strict";t.r(n);var o=t(266),i=t.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(a);n["default"]=i.a},266:function(e,n,t){}},[[259,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/signup/manage.js.map