
.signup-manage-page.data-v-28aa4093 {
  min-height: 100vh;
  box-sizing: border-box;
  padding-bottom: calc(120rpx + constant(safe-area-inset-bottom)); /* iOS 11.2 以下 */
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom)); /* iOS 11.2 及以上 */
  background-color: #f5f7fa;
}
.tip-bar.data-v-28aa4093 {
  display: flex;
  align-items: center;
  background-color: #e6f2ff;
  padding: 20rpx 30rpx;
}
.tip-text.data-v-28aa4093 {
  font-size: 26rpx;
  color: #4285f4;
  margin-left: 10rpx;
}
.profile-list.data-v-28aa4093 {
  padding: 20rpx;
}
.profile-item.data-v-28aa4093 {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
  border: 2rpx solid transparent; /* 添加透明边框，避免选中时的跳动 */
}
.profile-item.default-profile.data-v-28aa4093 {
  border: 2rpx solid #4285f4; /* 默认资料的边框 */
}
.profile-main.data-v-28aa4093 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.profile-info.data-v-28aa4093 {
  flex: 1;
}
.profile-name.data-v-28aa4093 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.profile-name .default-tag.data-v-28aa4093 {
  background-color: #4285f4;
  color: #fff;
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-radius: 0 12rpx 0 12rpx;
  margin-left: 10rpx;
}
.profile-phone.data-v-28aa4093 {
  font-size: 28rpx;
  color: #666;
}
.profile-actions.data-v-28aa4093 {
  display: flex;
}
.action-btn.data-v-28aa4093 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}
.profile-detail.data-v-28aa4093 {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
}
.detail-item.data-v-28aa4093 {
  display: flex;
  margin-bottom: 10rpx;
}
.detail-item.data-v-28aa4093:last-child {
  margin-bottom: 0;
}
.detail-label.data-v-28aa4093 {
  width: 120rpx;
  font-size: 26rpx;
  color: #999;
}
.detail-value.data-v-28aa4093 {
  flex: 1;
  font-size: 26rpx;
  color: #666;
}
.add-btn.data-v-28aa4093 {
  position: fixed;
  bottom: calc(40rpx + constant(safe-area-inset-bottom)); /* iOS 11.2 以下 */
  bottom: calc(40rpx + env(safe-area-inset-bottom)); /* iOS 11.2 及以上 */
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  background-color: #4285f4;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90%;
  height: 80rpx;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(66,133,244,0.3);
  z-index: 9;
}
.add-btn text.data-v-28aa4093 {
  margin-left: 10rpx;
  font-size: 28rpx;
}
.form-popup.data-v-28aa4093 {
  padding: 30rpx;
  box-sizing: border-box;
  height: 80vh;
  display: flex;
  flex-direction: column;
}
.form-header.data-v-28aa4093 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.form-title.data-v-28aa4093 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.form-scroll.data-v-28aa4093 {
  flex: 1;
  height: 0;
}
.form-content.data-v-28aa4093 {
  padding-bottom: 30rpx;
}
.form-item.data-v-28aa4093 {
  margin-bottom: 30rpx;
}
.form-label.data-v-28aa4093 {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}
.required.data-v-28aa4093::before {
  content: '*';
  color: #ff5252;
  margin-right: 6rpx;
}
.checkbox-item.data-v-28aa4093 {
  display: flex;
  align-items: center;
}
.checkbox-text.data-v-28aa4093 {
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}
.form-footer.data-v-28aa4093 {
  padding-top: 20rpx;
  border-top: 1px solid #eee;
}

