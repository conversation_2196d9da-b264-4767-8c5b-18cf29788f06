@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.past-event-detail.data-v-bd2bec76 {
  padding-bottom: 40rpx;
  background-color: #ffffff;
}
.cover-image.data-v-bd2bec76 {
  width: 100%;
  height: auto;
}
.header.data-v-bd2bec76 {
  padding: 30rpx;
  border-bottom: 1rpx solid #f2f2f2;
}
.title.data-v-bd2bec76 {
  font-size: 40rpx;
  font-weight: bold;
  color: #303133;
  line-height: 1.4;
  margin-bottom: 20rpx;
}
.meta.data-v-bd2bec76 {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #909399;
}
.date.data-v-bd2bec76 {
  margin-right: 20rpx;
}
.content.data-v-bd2bec76 {
  padding: 30rpx;
}
.summary.data-v-bd2bec76 {
  padding: 20rpx;
  background-color: #f8f8f8;
  border-left: 8rpx solid #2979ff;
  margin-bottom: 30rpx;
  font-size: 28rpx;
  color: #606266;
  line-height: 1.6;
}
.section-title.data-v-bd2bec76 {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  margin: 30rpx 0 20rpx;
  position: relative;
  padding-left: 20rpx;
}
.section-title.data-v-bd2bec76::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8rpx;
  bottom: 8rpx;
  width: 8rpx;
  background-color: #2979ff;
  border-radius: 4rpx;
}
.paragraph.data-v-bd2bec76 {
  font-size: 28rpx;
  color: #606266;
  line-height: 1.8;
  margin-bottom: 30rpx;
  text-align: justify;
}
.section-image.data-v-bd2bec76 {
  margin: 20rpx 0 30rpx;
}
.section-image image.data-v-bd2bec76 {
  width: 100%;
  height: auto;
  border-radius: 8rpx;
}
.image-caption.data-v-bd2bec76 {
  font-size: 24rpx;
  color: #909399;
  text-align: center;
  margin-top: 10rpx;
}
.conclusion.data-v-bd2bec76 {
  margin-top: 40rpx;
  padding: 30rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}
.conclusion-title.data-v-bd2bec76 {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 20rpx;
}
.conclusion-content.data-v-bd2bec76 {
  font-size: 28rpx;
  color: #606266;
  line-height: 1.8;
  text-align: justify;
}
.footer.data-v-bd2bec76 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  margin-top: 20rpx;
  border-top: 1rpx solid #f2f2f2;
}
.share-btn.data-v-bd2bec76 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.share-btn text.data-v-bd2bec76 {
  font-size: 24rpx;
  color: #606266;
  margin-top: 8rpx;
}
.join-btn.data-v-bd2bec76 {
  display: flex;
  align-items: center;
  background-color: #2979ff;
  color: #ffffff;
  padding: 20rpx 30rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
}
.join-btn text.data-v-bd2bec76 {
  margin-right: 10rpx;
}
.lang-switch.data-v-bd2bec76 {
  position: fixed;
  right: 30rpx;
  top: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  z-index: 10;
}
.share-popup.data-v-bd2bec76 {
  padding: 30rpx;
}
.share-title.data-v-bd2bec76 {
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 30rpx;
}
.share-options.data-v-bd2bec76 {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0 40rpx;
}
.share-option.data-v-bd2bec76 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.share-option text.data-v-bd2bec76 {
  font-size: 24rpx;
  color: #606266;
  margin-top: 16rpx;
}
.cancel-btn.data-v-bd2bec76 {
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 30rpx;
  color: #303133;
  border-top: 1rpx solid #f2f2f2;
}
