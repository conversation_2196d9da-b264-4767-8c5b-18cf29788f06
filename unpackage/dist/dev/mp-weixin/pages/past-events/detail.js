(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/past-events/detail"],{186:function(e,t,n){"use strict";(function(e,t){var o=n(4);n(26);o(n(25));var i=o(n(187));e.__webpack_require_UNI_MP_PLUGIN__=n,t(i.default)}).call(this,n(1)["default"],n(2)["createPage"])},187:function(e,t,n){"use strict";n.r(t);var o=n(188),i=n(190);for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(a);n(192);var s,r=n(32),c=Object(r["default"])(i["default"],o["render"],o["staticRenderFns"],!1,null,"bd2bec76",null,!1,o["components"],s);c.options.__file="pages/past-events/detail.vue",t["default"]=c.exports},188:function(e,t,n){"use strict";n.r(t);var o=n(189);n.d(t,"render",(function(){return o["render"]})),n.d(t,"staticRenderFns",(function(){return o["staticRenderFns"]})),n.d(t,"recyclableRender",(function(){return o["recyclableRender"]})),n.d(t,"components",(function(){return o["components"]}))},189:function(e,t,n){"use strict";var o;n.r(t),n.d(t,"render",(function(){return i})),n.d(t,"staticRenderFns",(function(){return s})),n.d(t,"recyclableRender",(function(){return a})),n.d(t,"components",(function(){return o}));try{o={uPopup:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-popup/u-popup")]).then(n.bind(null,340))},uIcon:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(n.bind(null,323))}}}catch(r){if(-1===r.message.indexOf("Cannot find module")||-1===r.message.indexOf(".vue"))throw r;console.error(r.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var i=function(){var e=this,t=e.$createElement;e._self._c;e._isMounted||(e.e0=function(t){e.showSharePopup=!1},e.e1=function(t){e.showSharePopup=!1})},a=!1,s=[];i._withStripped=!0},190:function(e,t,n){"use strict";n.r(t);var o=n(191),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=i.a},191:function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={data:function(){return{lang:"zh",eventId:null,showSharePopup:!1,eventData:{id:1,title:"2023夏令营精彩回顾",title_en:"2023 Summer Camp Highlights",coverImage:"https://images.unsplash.com/photo-1530124566582-a618bc2615dc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",date:"2023-09-01",views:1286,summary:"2023年夏令营圆满结束，本次活动共有120名学员参与，通过丰富多彩的课程和活动，学员们不仅学到了知识，还收获了友谊和成长。",summary_en:"The 2023 Summer Camp has successfully concluded with 120 students participating. Through a variety of courses and activities, students not only gained knowledge but also friendship and personal growth.",sections:[{title:"活动亮点",title_en:"Event Highlights",content:"本次夏令营为期两周，涵盖了编程、艺术、体育和科学探索等多个领域的课程。学员们在专业教师的指导下，完成了多个实践项目，展示了自己的创造力和团队协作能力。",content_en:"This two-week summer camp covered courses in programming, arts, sports, and scientific exploration. Under the guidance of professional teachers, students completed multiple practical projects, showcasing their creativity and teamwork."},{image:"https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",caption:"学员们在编程课上专注学习",caption_en:"Students focusing on programming class"},{title:"编程课程",title_en:"Programming Courses",content:"编程课程是本次夏令营的重点之一。学员们从基础的编程概念开始学习，逐步掌握了算法思维和问题解决能力。在课程结束时，每位学员都能独立完成一个小型项目，如简单游戏或网页应用。",content_en:"Programming was one of the highlights of this summer camp. Students started with basic programming concepts and gradually mastered algorithmic thinking and problem-solving skills. By the end of the course, each student could independently complete a small project such as a simple game or web application."},{image:"https://images.unsplash.com/photo-1517164850305-99a3e65bb47e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",caption:"团队合作完成科学实验",caption_en:"Team collaboration on scientific experiments"},{title:"科学探索",title_en:"Scientific Exploration",content:"科学探索课程让学员们亲身体验了科学实验的乐趣。从物理现象到化学反应，从生物观察到天文知识，学员们在动手实践中理解了科学原理，培养了科学思维和探索精神。",content_en:"The scientific exploration courses allowed students to experience the joy of scientific experiments firsthand. From physical phenomena to chemical reactions, from biological observations to astronomical knowledge, students understood scientific principles through hands-on practice and developed scientific thinking and exploratory spirit."}],conclusion:"本次夏令营不仅丰富了学员们的暑假生活，也为他们提供了一个展示才能、发掘潜力的平台。我们看到了每位学员的成长和进步，也期待在未来的活动中再次相见。感谢所有参与和支持本次活动的家长、老师和工作人员，让我们共同为孩子们的成长助力！",conclusion_en:"This summer camp not only enriched the students' summer vacation but also provided them with a platform to showcase their talents and discover their potential. We witnessed the growth and progress of each student and look forward to meeting them again in future activities. Thanks to all parents, teachers, and staff who participated in and supported this event, let's continue to help our children grow together!"}}},onLoad:function(t){var n=this;getApp().globalData.lang&&(this.lang=getApp().globalData.lang),e.$on("languageChanged",(function(e){n.lang=e.lang,n.setNavigationBarTitle()})),t.id&&(this.eventId=t.id),this.setNavigationBarTitle(),this.eventData.views++},onUnload:function(){e.$off("languageChanged")},methods:{setNavigationBarTitle:function(){e.setNavigationBarTitle({title:"zh"===this.lang?"活动回顾":"Event Review"})},switchLanguage:function(){var t="zh"===this.lang?"en":"zh";this.lang=t,getApp().globalData&&(getApp().globalData.lang=t),e.$emit("languageChanged",{lang:t}),this.setNavigationBarTitle()},shareEvent:function(){this.showSharePopup=!0},handleShare:function(t){var n=this;e.showToast({title:"zh"===this.lang?"分享成功":"Shared successfully",icon:"success"}),this.showSharePopup=!1,"link"===t&&e.setClipboardData({data:"https://example.com/past-events/detail?id="+this.eventId,success:function(){e.showToast({title:"zh"===n.lang?"链接已复制":"Link copied",icon:"success"})}})},navigateToEvents:function(){e.navigateTo({url:"/pages/events/index"})},fetchEventDetail:function(e){}}};t.default=n}).call(this,n(2)["default"])},192:function(e,t,n){"use strict";n.r(t);var o=n(193),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=i.a},193:function(e,t,n){}},[[186,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/past-events/detail.js.map