<view class="past-events-page data-v-8ef04adc"><view class="search-bar data-v-8ef04adc"><u-search vue-id="5873f8b4-1" placeholder="{{lang==='zh'?'搜索往期风采':'Search past events'}}" show-action="{{false}}" value="{{searchValue}}" data-event-opts="{{[['^search',[['onSearch']]],['^input',[['__set_model',['','searchValue','$event',[]]]]]]}}" bind:search="__e" bind:input="__e" class="data-v-8ef04adc" bind:__l="__l"></u-search></view><block wx:if="{{searchValue}}"><view class="result-info data-v-8ef04adc"><text class="result-text data-v-8ef04adc">{{''+(lang==='zh'?'"'+searchValue+'" 的搜索结果 ('+$root.g0+')':'Search results for "'+searchValue+'" ('+$root.g1+')')+''}}</text></view></block><view class="event-list data-v-8ef04adc"><block wx:for="{{filteredEvents}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['navigateToDetail',['$0'],[[['filteredEvents','id',item.id,'id']]]]]]]}}" class="event-card data-v-8ef04adc" bindtap="__e"><u-image vue-id="{{'5873f8b4-2-'+__i0__}}" src="{{item.img}}" width="100%" height="250rpx" border-radius="16rpx 16rpx 0 0" class="data-v-8ef04adc" bind:__l="__l"></u-image><view class="card-content data-v-8ef04adc"><view class="card-title data-v-8ef04adc">{{item.title}}</view><view class="card-desc data-v-8ef04adc">{{item.desc}}</view><view class="card-footer data-v-8ef04adc"><view class="card-date data-v-8ef04adc"><u-icon vue-id="{{'5873f8b4-3-'+__i0__}}" name="calendar" size="24rpx" color="#909399" class="data-v-8ef04adc" bind:__l="__l"></u-icon><text class="data-v-8ef04adc">{{item.date}}</text></view><view class="card-views data-v-8ef04adc"><u-icon vue-id="{{'5873f8b4-4-'+__i0__}}" name="eye" size="24rpx" color="#909399" class="data-v-8ef04adc" bind:__l="__l"></u-icon><text class="data-v-8ef04adc">{{item.views}}</text></view></view></view></view></block></view><block wx:if="{{$root.g2}}"><view class="no-results data-v-8ef04adc"><u-empty vue-id="5873f8b4-5" mode="search" text="{{lang==='zh'?'没有找到相关内容':'No results found'}}" class="data-v-8ef04adc" bind:__l="__l"></u-empty></view></block><block wx:if="{{$root.g3>0}}"><u-loadmore vue-id="5873f8b4-6" status="{{loadMoreStatus}}" loading-text="{{lang==='zh'?'正在加载...':'Loading...'}}" loadmore-text="{{lang==='zh'?'点击加载更多':'Load more'}}" nomore-text="{{lang==='zh'?'没有更多了':'No more data'}}" margin-top="20" margin-bottom="20" data-event-opts="{{[['^loadmore',[['loadMore']]]]}}" bind:loadmore="__e" class="data-v-8ef04adc" bind:__l="__l"></u-loadmore></block><lang-switch vue-id="5873f8b4-7" class="data-v-8ef04adc" bind:__l="__l"></lang-switch></view>