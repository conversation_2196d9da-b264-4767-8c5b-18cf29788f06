(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/past-events/list"],{251:function(e,t,n){"use strict";(function(e,t){var a=n(4);n(26);a(n(25));var o=a(n(252));e.__webpack_require_UNI_MP_PLUGIN__=n,t(o.default)}).call(this,n(1)["default"],n(2)["createPage"])},252:function(e,t,n){"use strict";n.r(t);var a=n(253),o=n(255);for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);n(257);var s,r=n(32),l=Object(r["default"])(o["default"],a["render"],a["staticRenderFns"],!1,null,"8ef04adc",null,!1,a["components"],s);l.options.__file="pages/past-events/list.vue",t["default"]=l.exports},253:function(e,t,n){"use strict";n.r(t);var a=n(254);n.d(t,"render",(function(){return a["render"]})),n.d(t,"staticRenderFns",(function(){return a["staticRenderFns"]})),n.d(t,"recyclableRender",(function(){return a["recyclableRender"]})),n.d(t,"components",(function(){return a["components"]}))},254:function(e,t,n){"use strict";var a;n.r(t),n.d(t,"render",(function(){return o})),n.d(t,"staticRenderFns",(function(){return s})),n.d(t,"recyclableRender",(function(){return i})),n.d(t,"components",(function(){return a}));try{a={uSearch:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-search/u-search")]).then(n.bind(null,283))},uImage:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-image/u-image")]).then(n.bind(null,315))},uIcon:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(n.bind(null,323))},uEmpty:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-empty/u-empty")]).then(n.bind(null,420))},uLoadmore:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-loadmore/u-loadmore")]).then(n.bind(null,332))}}}catch(r){if(-1===r.message.indexOf("Cannot find module")||-1===r.message.indexOf(".vue"))throw r;console.error(r.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var o=function(){var e=this,t=e.$createElement,n=(e._self._c,e.searchValue&&"zh"===e.lang?e.filteredEvents.length:null),a=e.searchValue&&"zh"!==e.lang?e.filteredEvents.length:null,o=e.searchValue&&0===e.filteredEvents.length,i=e.filteredEvents.length;e.$mp.data=Object.assign({},{$root:{g0:n,g1:a,g2:o,g3:i}})},i=!1,s=[];o._withStripped=!0},255:function(e,t,n){"use strict";n.r(t);var a=n(256),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=o.a},256:function(e,t,n){"use strict";(function(e){var a=n(4);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(n(18)),i=a(n(13)),s=function(){n.e("components/LangSwitch").then(function(){return resolve(n(355))}.bind(null,n)).catch(n.oe)},r={components:{LangSwitch:s},data:function(){return{lang:e.getStorageSync("lang")||"zh",searchValue:"",pastEvents:[],page:1,limit:10,loadMoreStatus:"loadmore",allEventsLoaded:!1}},computed:{filteredEvents:function(){if(!this.searchValue)return this.pastEvents;var e=this.searchValue.toLowerCase();return this.pastEvents.filter((function(t){return t.title.toLowerCase().includes(e)||t.desc.toLowerCase().includes(e)}))}},onLoad:function(t){this.lang=e.getStorageSync("lang")||"zh",getApp().globalData&&getApp().globalData.lang&&(this.lang=getApp().globalData.lang),e.$on("lang-change",this.onLangChange),this.setNavigationBarTitle(),t.keyword&&(this.searchValue=decodeURIComponent(t.keyword)),this.loadPastEvents()},onUnload:function(){e.$off("lang-change",this.onLangChange)},methods:{onLangChange:function(e){this.lang=e,this.setNavigationBarTitle()},setNavigationBarTitle:function(){e.setNavigationBarTitle({title:"zh"===this.lang?"往期风采":"Past Events"})},onSearch:function(e){"object"===(0,i.default)(e)&&null!==e&&(e=this.searchValue),this.searchValue=e,this.page=1,this.loadMoreStatus="loadmore",this.allEventsLoaded=!1,this.loadPastEvents()},loadPastEvents:function(){var e=this;"loading"===this.loadMoreStatus||this.allEventsLoaded||(this.loadMoreStatus="loading",setTimeout((function(){var t=[{id:1,title:"2023夏季编程营精彩回顾",img:"https://images.unsplash.com/photo-1530124566582-a618bc2615dc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",desc:"2023年夏令营圆满结束，120名学员参与，收获满满。",date:"2023-09-01",views:1286},{id:2,title:"2022冬令营精彩瞬间",img:"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",desc:"2022冬令营活动丰富，学员们在学习中收获快乐。",date:"2022-12-28",views:962},{id:3,title:"2021青少年科技展回顾",img:"https://images.unsplash.com/photo-1581472723648-909f4851d4ae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",desc:"青少年科技展展示了学生们的创新能力和科学素养。",date:"2021-11-15",views:754},{id:4,title:"2021春季音乐会回顾",img:"https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",desc:"春季音乐会上，学生们展示了精彩的音乐才艺。",date:"2021-04-20",views:683},{id:5,title:"2020冬季艺术节回顾",img:"https://images.unsplash.com/photo-1511379938547-c1f69419868d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",desc:"冬季艺术节展示了学生们在艺术领域的才华和创意。",date:"2020-12-15",views:592},{id:6,title:"2020夏季运动会回顾",img:"https://images.unsplash.com/photo-1517649763962-0c623066013b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",desc:"夏季运动会上，学生们展现了团队协作和体育精神。",date:"2020-07-30",views:547}],n=t;if(e.searchValue){var a=e.searchValue.toLowerCase();n=t.filter((function(e){return e.title.toLowerCase().includes(a)||e.desc.toLowerCase().includes(a)}))}var i=(e.page-1)*e.limit,s=e.page*e.limit,r=n.slice(i,s);1===e.page?e.pastEvents=r:e.pastEvents=[].concat((0,o.default)(e.pastEvents),(0,o.default)(r)),e.pastEvents.length>=n.length?(e.loadMoreStatus="nomore",e.allEventsLoaded=!0):e.loadMoreStatus="loadmore"}),1e3))},loadMore:function(){this.page++,this.loadPastEvents()},navigateToDetail:function(t){e.navigateTo({url:"/pages/past-events/detail?id=".concat(t)})}}};t.default=r}).call(this,n(2)["default"])},257:function(e,t,n){"use strict";n.r(t);var a=n(258),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=o.a},258:function(e,t,n){}},[[251,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/past-events/list.js.map