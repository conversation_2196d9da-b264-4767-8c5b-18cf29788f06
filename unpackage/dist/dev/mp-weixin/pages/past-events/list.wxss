
.past-events-page.data-v-8ef04adc {
  min-height: 100vh;
  padding-bottom: 30rpx;
  box-sizing: border-box;
}
.search-bar.data-v-8ef04adc {
  margin: 24rpx;
  margin-top: 0rpx;
  padding-top: 24rpx;
  box-sizing: border-box;
}
.result-info.data-v-8ef04adc {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}
.result-text.data-v-8ef04adc {
  font-size: 28rpx;
  color: #909399;
}
.event-list.data-v-8ef04adc {
  padding: 0 30rpx;
}
.event-card.data-v-8ef04adc {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.card-content.data-v-8ef04adc {
  padding: 20rpx;
}
.card-title.data-v-8ef04adc {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10rpx;
}
.card-desc.data-v-8ef04adc {
  font-size: 28rpx;
  color: #606266;
  margin-bottom: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.card-footer.data-v-8ef04adc {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #909399;
}
.card-date.data-v-8ef04adc, .card-views.data-v-8ef04adc {
  display: flex;
  align-items: center;
}
.card-date text.data-v-8ef04adc, .card-views text.data-v-8ef04adc {
  margin-left: 8rpx;
}
.no-results.data-v-8ef04adc {
  padding: 100rpx 0;
}

