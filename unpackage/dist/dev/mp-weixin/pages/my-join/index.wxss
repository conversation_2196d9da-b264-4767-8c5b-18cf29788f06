
.card-img-wrap.data-v-66a8d3c5 {
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    width: 100%;
}
.status-tag.data-v-66a8d3c5 {
    position: absolute;
    top: 16rpx;
    right: 16rpx;
    z-index: 0;
}
.poster-popup.data-v-66a8d3c5 {
    /* 消除 u-popup 内部的默认 padding */
    line-height: 1;
}
.search-bar.data-v-66a8d3c5 {
    margin: 24rpx;
    margin-top: 0rpx;
    padding-top: 24rpx;
    box-sizing: border-box;
}
.index-page.data-v-66a8d3c5 {
    min-height: 100vh;
    padding-bottom: 0;
}
.banner-swiper.data-v-66a8d3c5 {
    margin: 24rpx;
}
.category-grid.data-v-66a8d3c5 {
    margin-top: 24rpx;
}
.cat-title.data-v-66a8d3c5 {
    font-size: 24rpx;
    margin-top: 8rpx;
    color: #333;
}
.section-title.data-v-66a8d3c5 {
    font-size: 32rpx;
    font-weight: bold;
    margin: 32rpx 24rpx 16rpx 24rpx;
    color: #222;
}
.activity-list.data-v-66a8d3c5 {
    padding: 0 24rpx;
}
.activity-card.data-v-66a8d3c5 {
    margin-bottom: 24rpx;
    background: #fff;
    border-radius: 16rpx;
    width: 100%;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    overflow: hidden;
}
.card-content.data-v-66a8d3c5 {
    padding: 24rpx;
}
.card-title.data-v-66a8d3c5 {
    font-size: 30rpx;
    font-weight: bold;
    color: #303133;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.card-subtitle.data-v-66a8d3c5 {
    font-size: 24rpx;
    color: #909399;
    margin-top: 8rpx;
}
.card-desc.data-v-66a8d3c5 {
    font-size: 26rpx;
    color: #606266;
    margin-top: 16rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}
.popup-content.data-v-66a8d3c5 {
    padding: 48rpx 32rpx;
    text-align: center;
}

