(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/events/detail"],{178:function(e,n,t){"use strict";(function(e,n){var i=t(4);t(26);i(t(25));var o=i(t(179));e.__webpack_require_UNI_MP_PLUGIN__=t,n(o.default)}).call(this,t(1)["default"],t(2)["createPage"])},179:function(e,n,t){"use strict";t.r(n);var i=t(180),o=t(182);for(var a in o)["default"].indexOf(a)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(a);t(184);var s,l=t(32),r=Object(l["default"])(o["default"],i["render"],i["staticRenderFns"],!1,null,"5398ca44",null,!1,i["components"],s);r.options.__file="pages/events/detail.vue",n["default"]=r.exports},180:function(e,n,t){"use strict";t.r(n);var i=t(181);t.d(n,"render",(function(){return i["render"]})),t.d(n,"staticRenderFns",(function(){return i["staticRenderFns"]})),t.d(n,"recyclableRender",(function(){return i["recyclableRender"]})),t.d(n,"components",(function(){return i["components"]}))},181:function(e,n,t){"use strict";var i;t.r(n),t.d(n,"render",(function(){return o})),t.d(n,"staticRenderFns",(function(){return s})),t.d(n,"recyclableRender",(function(){return a})),t.d(n,"components",(function(){return i}));try{i={uIcon:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(t.bind(null,323))},uPopup:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-popup/u-popup")]).then(t.bind(null,340))}}}catch(l){if(-1===l.message.indexOf("Cannot find module")||-1===l.message.indexOf(".vue"))throw l;console.error(l.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var o=function(){var e=this,n=e.$createElement;e._self._c;e._isMounted||(e.e0=function(n){e.showProfileSelector=!0},e.e1=function(n){e.showProfileSelector=!1},e.e2=function(n){e.showProfileSelector=!1},e.e3=function(n){e.showPayment=!1},e.e4=function(n){e.paymentMethod="wechat"},e.e5=function(n){e.showPayment=!1})},a=!1,s=[];o._withStripped=!0},182:function(e,n,t){"use strict";t.r(n);var i=t(183),o=t.n(i);for(var a in i)["default"].indexOf(a)<0&&function(e){t.d(n,e,(function(){return i[e]}))}(a);n["default"]=o.a},183:function(e,n,t){"use strict";(function(e){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var t={data:function(){return{lang:"zh",eventId:null,eventData:{id:1,title:"2023夏季编程训练营",title_en:"2023 Summer Coding Camp",images:["https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80","https://images.unsplash.com/photo-1531482615713-2afd69097998?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80","https://images.unsplash.com/photo-1581472723648-909f4851d4ae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"],startTime:"2023-07-15 09:00",endTime:"2023-07-20 17:00",location:"深圳市南山区科技园",location_en:"Nanshan District, Shenzhen",currentParticipants:45,maxParticipants:50,fee:1980,points:200,status:"ongoing",description:"本次训练营将带领学员深入学习人工智能和机器学习的核心概念和实践应用。课程包括Python编程基础、数据分析、机器学习算法以及深度学习入门等内容。学员将通过实际项目来巩固所学知识，提升编程能力和解决问题的能力。",description_en:"This training camp will lead students to deeply learn the core concepts and practical applications of artificial intelligence and machine learning. The course includes Python programming basics, data analysis, machine learning algorithms, and an introduction to deep learning. Students will consolidate their knowledge and improve their programming and problem-solving abilities through practical projects."},showProfileSelector:!1,showPayment:!1,showPaymentSuccess:!1,selectedProfileIndex:-1,selectedProfile:null,paymentMethod:"wechat",statusTextZh:{ongoing:"进行中",coming:"即将开始",full:"已满员",ended:"已结束"},statusTextEn:{ongoing:"Ongoing",coming:"Coming Soon",full:"Full",ended:"Ended"},signupProfiles:[{id:1,name:"张三",phone:"138****1234",email:"<EMAIL>",idCard:"440******1234",address:"广东省深圳市南山区科技园",isDefault:!0},{id:2,name:"李四",phone:"139****5678",email:"<EMAIL>",idCard:"440******5678",address:"广东省深圳市福田区中心区",isDefault:!1},{id:3,name:"John Doe",phone:"135****9012",email:"<EMAIL>",idCard:"440******9012",address:"Nanshan District, Shenzhen",isDefault:!1}]}},onLoad:function(n){var t=this;getApp().globalData.lang&&(this.lang=getApp().globalData.lang),e.$on("languageChanged",(function(e){t.lang=e.lang,t.setNavigationBarTitle()})),n.id&&(this.eventId=n.id),this.setDefaultProfile(),this.setNavigationBarTitle()},onUnload:function(){e.$off("languageChanged")},methods:{setNavigationBarTitle:function(){e.setNavigationBarTitle({title:"zh"===this.lang?"活动详情":"Event Details"})},setDefaultProfile:function(){var e=this.signupProfiles.findIndex((function(e){return e.isDefault}));-1!==e&&(this.selectedProfileIndex=e,this.selectedProfile=this.signupProfiles[e])},selectProfile:function(e){this.selectedProfileIndex=e},confirmProfileSelection:function(){-1!==this.selectedProfileIndex&&(this.selectedProfile=this.signupProfiles[this.selectedProfileIndex]),this.showProfileSelector=!1},handleSignup:function(){this.selectedProfile?"full"!==this.eventData.status?this.eventData.fee<=0?this.showPaymentSuccess=!0:this.showPayment=!0:e.showToast({title:"zh"===this.lang?"活动已满员":"Event is full",icon:"none"}):e.showToast({title:"zh"===this.lang?"请选择报名信息":"Please select a profile",icon:"none"})},processPayment:function(){var n=this;e.showLoading({title:"zh"===this.lang?"处理中...":"Processing..."}),setTimeout((function(){e.hideLoading(),n.showPayment=!1,n.showPaymentSuccess=!0}),2e3)},handlePaymentSuccessDone:function(){this.showPaymentSuccess=!1,e.navigateTo({url:"/pages/my-join/index"})},fetchEventDetail:function(e){},switchLanguage:function(){var n="zh"===this.lang?"en":"zh";this.lang=n,getApp().globalData&&(getApp().globalData.lang=n),e.$emit("languageChanged",{lang:n}),this.setNavigationBarTitle()}}};n.default=t}).call(this,t(2)["default"])},184:function(e,n,t){"use strict";t.r(n);var i=t(185),o=t.n(i);for(var a in i)["default"].indexOf(a)<0&&function(e){t.d(n,e,(function(){return i[e]}))}(a);n["default"]=o.a},185:function(e,n,t){}},[[178,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/events/detail.js.map