@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.event-detail.data-v-5398ca44 {
  padding-bottom: calc(120rpx + constant(safe-area-inset-bottom));
  /* iOS 11.0 */
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
  /* iOS 11.2+ */
  background-color: #f5f5f5;
}
.event-swiper.data-v-5398ca44 {
  width: 100%;
  height: 450rpx;
}
.event-image.data-v-5398ca44 {
  width: 100%;
  height: 100%;
}
.event-header.data-v-5398ca44 {
  padding: 30rpx;
  background-color: #ffffff;
}
.event-title.data-v-5398ca44 {
  font-size: 36rpx;
  font-weight: bold;
  color: #303133;
  line-height: 1.5;
  margin-bottom: 16rpx;
}
.event-status.data-v-5398ca44 {
  display: inline-block;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #ffffff;
}
.event-status.ongoing.data-v-5398ca44 {
  background-color: #2979ff;
}
.event-status.coming.data-v-5398ca44 {
  background-color: #ff9900;
}
.event-status.full.data-v-5398ca44 {
  background-color: #909399;
}
.event-status.ended.data-v-5398ca44 {
  background-color: #c0c4cc;
}
.info-card.data-v-5398ca44 {
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.card-header.data-v-5398ca44 {
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f2f2f2;
}
.card-title.data-v-5398ca44 {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  position: relative;
  padding-left: 20rpx;
}
.card-title.data-v-5398ca44::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8rpx;
  bottom: 8rpx;
  width: 8rpx;
  background-color: #2979ff;
  border-radius: 4rpx;
}
.info-item.data-v-5398ca44 {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
}
.info-item.data-v-5398ca44:last-child {
  border-bottom: none;
}
.info-label.data-v-5398ca44 {
  margin-left: 16rpx;
  color: #606266;
  font-size: 28rpx;
  width: 120rpx;
}
.info-content.data-v-5398ca44 {
  flex: 1;
  font-size: 28rpx;
  color: #303133;
}
.event-description.data-v-5398ca44 {
  padding: 20rpx 30rpx 30rpx;
  font-size: 28rpx;
  color: #606266;
  line-height: 1.6;
}
.select-profile.data-v-5398ca44 {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx 30rpx;
}
.select-label.data-v-5398ca44 {
  font-size: 28rpx;
  color: #606266;
}
.profile-selector.data-v-5398ca44 {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  margin-left: 20rpx;
}
.profile-selector text.data-v-5398ca44 {
  font-size: 28rpx;
  color: #303133;
}
.selected-profile-info.data-v-5398ca44 {
  padding: 20rpx 30rpx 30rpx;
  border-top: 1rpx solid #f8f8f8;
}
.profile-detail-item.data-v-5398ca44 {
  display: flex;
  margin-bottom: 16rpx;
}
.profile-detail-item.data-v-5398ca44:last-child {
  margin-bottom: 0;
}
.profile-detail-label.data-v-5398ca44 {
  font-size: 28rpx;
  color: #606266;
  width: 120rpx;
}
.profile-detail-value.data-v-5398ca44 {
  flex: 1;
  font-size: 28rpx;
  color: #303133;
}
.bottom-bar.data-v-5398ca44 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-bottom: constant(safe-area-inset-bottom);
  /* iOS 11.0 */
  padding-bottom: env(safe-area-inset-bottom);
  /* iOS 11.2+ */
}
.safe-area-inset-bottom.data-v-5398ca44 {
  height: constant(safe-area-inset-bottom);
  /* iOS 11.0 */
  height: env(safe-area-inset-bottom);
  /* iOS 11.2+ */
}
.bottom-price.data-v-5398ca44 {
  flex: 1;
}
.price-label.data-v-5398ca44 {
  font-size: 28rpx;
  color: #606266;
}
.price-value.data-v-5398ca44 {
  font-size: 36rpx;
  color: #ff5722;
  font-weight: bold;
}
.bottom-button.data-v-5398ca44 {
  width: 240rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  background-color: #2979ff;
  color: #ffffff;
  border-radius: 35rpx;
  font-size: 30rpx;
}
.bottom-button.disabled.data-v-5398ca44 {
  background-color: #a0cfff;
}
.popup-content.data-v-5398ca44 {
  padding: 30rpx;
}
.popup-header.data-v-5398ca44 {
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 30rpx;
}
.profile-list.data-v-5398ca44 {
  max-height: 600rpx;
}
.profile-item.data-v-5398ca44 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f2f2f2;
}
.profile-item.selected.data-v-5398ca44 {
  background-color: #f5f7fa;
}
.profile-item.default.data-v-5398ca44 {
  border: 2rpx solid #2979ff;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}
.profile-info.data-v-5398ca44 {
  display: flex;
  flex-direction: column;
}
.profile-name.data-v-5398ca44 {
  font-size: 28rpx;
  color: #303133;
  margin-bottom: 8rpx;
}
.profile-phone.data-v-5398ca44 {
  font-size: 24rpx;
  color: #909399;
}
.popup-buttons.data-v-5398ca44 {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}
.cancel-button.data-v-5398ca44 {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #f2f2f2;
  color: #606266;
  border-radius: 8rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
}
.confirm-button.data-v-5398ca44 {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #2979ff;
  color: #ffffff;
  border-radius: 8rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}
.payment-info.data-v-5398ca44 {
  margin-bottom: 30rpx;
}
.payment-item.data-v-5398ca44 {
  display: flex;
  margin-bottom: 16rpx;
}
.payment-item.data-v-5398ca44:last-child {
  margin-bottom: 0;
}
.payment-label.data-v-5398ca44 {
  width: 160rpx;
  font-size: 28rpx;
  color: #606266;
}
.payment-value.data-v-5398ca44 {
  flex: 1;
  font-size: 28rpx;
  color: #303133;
}
.payment-value.payment-amount.data-v-5398ca44 {
  color: #ff5722;
  font-weight: bold;
  font-size: 32rpx;
}
.payment-methods.data-v-5398ca44 {
  margin-bottom: 30rpx;
}
.payment-title.data-v-5398ca44 {
  font-size: 28rpx;
  color: #606266;
  margin-bottom: 20rpx;
}
.payment-method-item.data-v-5398ca44 {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border: 1rpx solid #f2f2f2;
  border-radius: 8rpx;
}
.payment-method-item.selected.data-v-5398ca44 {
  border-color: #2979ff;
  background-color: #f5f7fa;
}
.payment-method-icon.data-v-5398ca44 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.payment-method-name.data-v-5398ca44 {
  flex: 1;
  font-size: 28rpx;
  color: #303133;
}
.success-popup.data-v-5398ca44 {
  width: 500rpx;
  padding: 50rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.success-title.data-v-5398ca44 {
  font-size: 36rpx;
  font-weight: bold;
  color: #303133;
  margin: 30rpx 0 20rpx;
}
.success-message.data-v-5398ca44 {
  font-size: 28rpx;
  color: #606266;
  margin-bottom: 40rpx;
  text-align: center;
}
.success-button.data-v-5398ca44 {
  width: 60%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #2979ff;
  color: #ffffff;
  border-radius: 40rpx;
  font-size: 30rpx;
}
.lang-switch.data-v-5398ca44 {
  position: fixed;
  right: 30rpx;
  top: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  z-index: 10;
}
