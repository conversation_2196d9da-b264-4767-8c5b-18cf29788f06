
.events-page.data-v-338b5f55 {
  min-height: 100vh;
  box-sizing: border-box;
}
.main-content.data-v-338b5f55 {
  display: flex;
  flex-direction: row;
}
.sidebar-scroll.data-v-338b5f55 {
  width: 160rpx;
  background: #fff;
  border-radius: 0 24rpx 24rpx 0;
  box-shadow: 2rpx 0 8rpx rgba(0, 0, 0, 0.03);
  padding: 0;
}
.sidebar.data-v-338b5f55 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 0;
}
.sidebar-item.data-v-338b5f55 {
  width: 100%;
  padding: 24rpx 0;
  text-align: center;
  cursor: pointer;
  border-left: 8rpx solid transparent;
  transition: border-color 0.2s;
}
.sidebar-item.active.data-v-338b5f55 {
  background: #f0f7ff;
  border-left: 8rpx solid #2979ff;
}
.sidebar-title.data-v-338b5f55 {
  font-size: 24rpx;
  color: #333;
}
.activity-panel-scroll.data-v-338b5f55 {
  flex: 1;
  background: transparent;
}
.activity-panel.data-v-338b5f55 {
  display: flex;
  flex-direction: column;
  min-width: 0;
}
.search-bar-fix.data-v-338b5f55 {
  padding: 24rpx;
  box-sizing: border-box;
}
.sort-tabs.data-v-338b5f55 {
  margin-bottom: 16rpx;
  background: #f7f8fa;
}
.activity-list.data-v-338b5f55 {
  flex: 1;
  padding: 0 24rpx 24rpx 24rpx;
  box-sizing: border-box;
}
.activity-card.data-v-338b5f55 {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.activity-card.data-v-338b5f55:not(:first-child) {
  margin-top: 24rpx;
}
.card-content.data-v-338b5f55 {
  padding: 24rpx;
}
.card-title.data-v-338b5f55 {
  font-size: 30rpx;
  font-weight: bold;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.card-subtitle.data-v-338b5f55 {
  font-size: 24rpx;
  color: #909399;
  margin-top: 8rpx;
}
.card-desc.data-v-338b5f55 {
  font-size: 26rpx;
  color: #606266;
  margin-top: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

