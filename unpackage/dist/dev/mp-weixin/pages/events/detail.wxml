<view class="event-detail data-v-5398ca44"><swiper class="event-swiper data-v-5398ca44" circular="{{true}}" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}"><block wx:for="{{eventData.images}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="data-v-5398ca44"><image class="event-image data-v-5398ca44" src="{{item}}" mode="aspectFill"></image></swiper-item></block></swiper><view class="event-header data-v-5398ca44"><view class="event-title data-v-5398ca44">{{lang==='zh'?eventData.title:eventData.title_en}}</view><view class="{{['event-status','data-v-5398ca44',eventData.status]}}">{{''+(lang==='zh'?statusTextZh[eventData.status]:statusTextEn[eventData.status])+''}}</view></view><view class="info-card data-v-5398ca44"><view class="card-header data-v-5398ca44"><view class="card-title data-v-5398ca44">{{lang==='zh'?'活动信息':'Event Information'}}</view></view><view class="info-item data-v-5398ca44"><u-icon vue-id="1f7f7c30-1" name="calendar" size="36rpx" color="#2979ff" class="data-v-5398ca44" bind:__l="__l"></u-icon><text class="info-label data-v-5398ca44">{{lang==='zh'?'开始：':'Start: '}}</text><text class="info-content data-v-5398ca44">{{eventData.startTime}}</text></view><view class="info-item data-v-5398ca44"><u-icon vue-id="1f7f7c30-2" name="calendar" size="36rpx" color="#2979ff" class="data-v-5398ca44" bind:__l="__l"></u-icon><text class="info-label data-v-5398ca44">{{lang==='zh'?'结束：':'End: '}}</text><text class="info-content data-v-5398ca44">{{eventData.endTime}}</text></view><view class="info-item data-v-5398ca44"><u-icon vue-id="1f7f7c30-3" name="map" size="36rpx" color="#2979ff" class="data-v-5398ca44" bind:__l="__l"></u-icon><text class="info-label data-v-5398ca44">{{lang==='zh'?'地点：':'Location: '}}</text><text class="info-content data-v-5398ca44">{{lang==='zh'?eventData.location:eventData.location_en}}</text></view><view class="info-item data-v-5398ca44"><u-icon vue-id="1f7f7c30-4" name="account" size="36rpx" color="#2979ff" class="data-v-5398ca44" bind:__l="__l"></u-icon><text class="info-label data-v-5398ca44">{{lang==='zh'?'人数：':'Capacity: '}}</text><text class="info-content data-v-5398ca44">{{eventData.currentParticipants+"/"+eventData.maxParticipants}}</text></view><view class="info-item data-v-5398ca44"><u-icon vue-id="1f7f7c30-5" name="rmb-circle" size="36rpx" color="#2979ff" class="data-v-5398ca44" bind:__l="__l"></u-icon><text class="info-label data-v-5398ca44">{{lang==='zh'?'费用：':'Fee: '}}</text><text class="info-content data-v-5398ca44">{{eventData.fee>0?'¥'+eventData.fee:lang==='zh'?'免费':'Free'}}</text></view><view class="info-item data-v-5398ca44"><u-icon vue-id="1f7f7c30-6" name="star" size="36rpx" color="#2979ff" class="data-v-5398ca44" bind:__l="__l"></u-icon><text class="info-label data-v-5398ca44">{{lang==='zh'?'积分：':'Points: '}}</text><text class="info-content data-v-5398ca44">{{"+"+eventData.points}}</text></view></view><view class="info-card data-v-5398ca44"><view class="card-header data-v-5398ca44"><view class="card-title data-v-5398ca44">{{lang==='zh'?'活动详情':'Event Details'}}</view></view><view class="event-description data-v-5398ca44">{{lang==='zh'?eventData.description:eventData.description_en}}</view></view><block wx:if="{{eventData.status!=='ended'}}"><view class="info-card data-v-5398ca44"><view class="card-header data-v-5398ca44"><view class="card-title data-v-5398ca44">{{lang==='zh'?'报名信息':'Registration'}}</view></view><view class="select-profile data-v-5398ca44"><text class="select-label data-v-5398ca44">{{lang==='zh'?'选择报名信息：':'Select Profile:'}}</text><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="profile-selector data-v-5398ca44" bindtap="__e"><text class="data-v-5398ca44">{{selectedProfile?selectedProfile.name:lang==='zh'?'请选择':'Please select'}}</text><u-icon vue-id="1f7f7c30-7" name="arrow-right" size="28rpx" color="#909399" class="data-v-5398ca44" bind:__l="__l"></u-icon></view></view><block wx:if="{{selectedProfile}}"><view class="selected-profile-info data-v-5398ca44"><view class="profile-detail-item data-v-5398ca44"><text class="profile-detail-label data-v-5398ca44">{{lang==='zh'?'姓名：':'Name:'}}</text><text class="profile-detail-value data-v-5398ca44">{{selectedProfile.name}}</text></view><view class="profile-detail-item data-v-5398ca44"><text class="profile-detail-label data-v-5398ca44">{{lang==='zh'?'手机：':'Phone:'}}</text><text class="profile-detail-value data-v-5398ca44">{{selectedProfile.phone}}</text></view><block wx:if="{{selectedProfile.email}}"><view class="profile-detail-item data-v-5398ca44"><text class="profile-detail-label data-v-5398ca44">{{lang==='zh'?'邮箱：':'Email:'}}</text><text class="profile-detail-value data-v-5398ca44">{{selectedProfile.email}}</text></view></block><block wx:if="{{selectedProfile.idCard}}"><view class="profile-detail-item data-v-5398ca44"><text class="profile-detail-label data-v-5398ca44">{{lang==='zh'?'身份证：':'ID Card:'}}</text><text class="profile-detail-value data-v-5398ca44">{{selectedProfile.idCard}}</text></view></block><block wx:if="{{selectedProfile.address}}"><view class="profile-detail-item data-v-5398ca44"><text class="profile-detail-label data-v-5398ca44">{{lang==='zh'?'地址：':'Address:'}}</text><text class="profile-detail-value data-v-5398ca44">{{selectedProfile.address}}</text></view></block></view></block></view></block><block wx:if="{{eventData.status!=='ended'}}"><view class="bottom-bar data-v-5398ca44"><view class="bottom-price data-v-5398ca44"><text class="price-label data-v-5398ca44">{{lang==='zh'?'费用：':'Fee: '}}</text><block wx:if="{{eventData.fee>0}}"><text class="price-value data-v-5398ca44">{{"¥"+eventData.fee}}</text></block><block wx:else><text class="price-value data-v-5398ca44">{{lang==='zh'?'免费':'Free'}}</text></block></view><view data-event-opts="{{[['tap',[['handleSignup',['$event']]]]]}}" class="{{['bottom-button','data-v-5398ca44',(!selectedProfile||eventData.status==='full')?'disabled':'']}}" bindtap="__e">{{''+(lang==='zh'?'立即报名':'Sign Up')+''}}</view></view></block><u-popup vue-id="1f7f7c30-8" show="{{showProfileSelector}}" mode="bottom" data-event-opts="{{[['^close',[['e1']]]]}}" bind:close="__e" class="data-v-5398ca44" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-5398ca44"><view class="popup-header data-v-5398ca44"><text class="data-v-5398ca44">{{lang==='zh'?'选择报名信息':'Select Profile'}}</text></view><scroll-view class="profile-list data-v-5398ca44" scroll-y="{{true}}"><block wx:for="{{signupProfiles}}" wx:for-item="profile" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectProfile',[index]]]]]}}" class="{{['profile-item','data-v-5398ca44',(selectedProfileIndex===index)?'selected':'',(profile.isDefault)?'default':'']}}" bindtap="__e"><view class="profile-info data-v-5398ca44"><text class="profile-name data-v-5398ca44">{{profile.name}}</text><text class="profile-phone data-v-5398ca44">{{profile.phone}}</text></view><block wx:if="{{selectedProfileIndex===index}}"><u-icon vue-id="{{('1f7f7c30-9-'+index)+','+('1f7f7c30-8')}}" name="checkmark" color="#2979ff" size="40rpx" class="data-v-5398ca44" bind:__l="__l"></u-icon></block></view></block></scroll-view><view class="popup-buttons data-v-5398ca44"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="cancel-button data-v-5398ca44" bindtap="__e">{{''+(lang==='zh'?'取消':'Cancel')+''}}</view><view data-event-opts="{{[['tap',[['confirmProfileSelection',['$event']]]]]}}" class="confirm-button data-v-5398ca44" bindtap="__e">{{''+(lang==='zh'?'确认':'Confirm')+''}}</view></view></view></u-popup><u-popup vue-id="1f7f7c30-10" show="{{showPayment}}" mode="bottom" data-event-opts="{{[['^close',[['e3']]]]}}" bind:close="__e" class="data-v-5398ca44" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-5398ca44"><view class="popup-header data-v-5398ca44"><text class="data-v-5398ca44">{{lang==='zh'?'支付费用':'Payment'}}</text></view><view class="payment-info data-v-5398ca44"><view class="payment-item data-v-5398ca44"><text class="payment-label data-v-5398ca44">{{lang==='zh'?'活动名称：':'Event: '}}</text><text class="payment-value data-v-5398ca44">{{lang==='zh'?eventData.title:eventData.title_en}}</text></view><view class="payment-item data-v-5398ca44"><text class="payment-label data-v-5398ca44">{{lang==='zh'?'报名人：':'Participant: '}}</text><text class="payment-value data-v-5398ca44">{{selectedProfile?selectedProfile.name:''}}</text></view><view class="payment-item data-v-5398ca44"><text class="payment-label data-v-5398ca44">{{lang==='zh'?'应付金额：':'Amount: '}}</text><text class="payment-value payment-amount data-v-5398ca44">{{"¥"+eventData.fee}}</text></view></view><view class="payment-methods data-v-5398ca44"><view class="payment-title data-v-5398ca44">{{lang==='zh'?'支付方式':'Payment Method'}}</view><view data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" class="{{['payment-method-item','data-v-5398ca44',(paymentMethod==='wechat')?'selected':'']}}" bindtap="__e"><view class="payment-method-icon wechat data-v-5398ca44"><u-icon vue-id="{{('1f7f7c30-11')+','+('1f7f7c30-10')}}" name="weixin-fill" color="#09BB07" size="40rpx" class="data-v-5398ca44" bind:__l="__l"></u-icon></view><view class="payment-method-name data-v-5398ca44">{{''+(lang==='zh'?'微信支付':'WeChat Pay')+''}}</view><block wx:if="{{paymentMethod==='wechat'}}"><u-icon vue-id="{{('1f7f7c30-12')+','+('1f7f7c30-10')}}" name="checkmark" color="#2979ff" size="40rpx" class="data-v-5398ca44" bind:__l="__l"></u-icon></block></view></view><view class="popup-buttons data-v-5398ca44"><view data-event-opts="{{[['tap',[['e5',['$event']]]]]}}" class="cancel-button data-v-5398ca44" bindtap="__e">{{''+(lang==='zh'?'取消':'Cancel')+''}}</view><view data-event-opts="{{[['tap',[['processPayment',['$event']]]]]}}" class="confirm-button data-v-5398ca44" bindtap="__e">{{''+(lang==='zh'?'确认支付':'Pay Now')+''}}</view></view></view></u-popup><u-popup vue-id="1f7f7c30-13" show="{{showPaymentSuccess}}" mode="center" round="{{16}}" safeAreaInsetBottom="{{false}}" data-event-opts="{{[['^close',[['handlePaymentSuccessDone']]]]}}" bind:close="__e" class="data-v-5398ca44" bind:__l="__l" vue-slots="{{['default']}}"><view class="success-popup data-v-5398ca44"><u-icon vue-id="{{('1f7f7c30-14')+','+('1f7f7c30-13')}}" name="checkmark-circle" color="#09BB07" size="120rpx" class="data-v-5398ca44" bind:__l="__l"></u-icon><text class="success-title data-v-5398ca44">{{lang==='zh'?'支付成功':'Payment Successful'}}</text><text class="success-message data-v-5398ca44">{{lang==='zh'?'您已成功报名参加活动':'You have successfully signed up for the event'}}</text><view data-event-opts="{{[['tap',[['handlePaymentSuccessDone',['$event']]]]]}}" class="success-button data-v-5398ca44" bindtap="__e">{{''+(lang==='zh'?'完成':'Done')+''}}</view></view></u-popup><view data-event-opts="{{[['tap',[['switchLanguage',['$event']]]]]}}" class="lang-switch data-v-5398ca44" bindtap="__e"><text class="data-v-5398ca44">{{lang==='zh'?'EN':'中'}}</text></view></view>