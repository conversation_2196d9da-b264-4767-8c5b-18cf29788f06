(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/events/index"],{170:function(e,t,n){"use strict";(function(e,t){var o=n(4);n(26);o(n(25));var a=o(n(171));e.__webpack_require_UNI_MP_PLUGIN__=n,t(a.default)}).call(this,n(1)["default"],n(2)["createPage"])},171:function(e,t,n){"use strict";n.r(t);var o=n(172),a=n(174);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n(177);var i,c=n(32),s=Object(c["default"])(a["default"],o["render"],o["staticRenderFns"],!1,null,"338b5f55",null,!1,o["components"],i);s.options.__file="pages/events/index.vue",t["default"]=s.exports},172:function(e,t,n){"use strict";n.r(t);var o=n(173);n.d(t,"render",(function(){return o["render"]})),n.d(t,"staticRenderFns",(function(){return o["staticRenderFns"]})),n.d(t,"recyclableRender",(function(){return o["recyclableRender"]})),n.d(t,"components",(function(){return o["components"]}))},173:function(e,t,n){"use strict";var o;n.r(t),n.d(t,"render",(function(){return a})),n.d(t,"staticRenderFns",(function(){return i})),n.d(t,"recyclableRender",(function(){return r})),n.d(t,"components",(function(){return o}));try{o={uSearch:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-search/u-search")]).then(n.bind(null,283))},uImage:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-image/u-image")]).then(n.bind(null,315))},uLoadmore:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-loadmore/u-loadmore")]).then(n.bind(null,332))}}}catch(c){if(-1===c.message.indexOf("Cannot find module")||-1===c.message.indexOf(".vue"))throw c;console.error(c.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var a=function(){var e=this,t=e.$createElement,n=(e._self._c,e.__map(e.filteredActivities,(function(t,n){var o=e.__get_orig(t),a=t.beginTime?t.beginTime.split(" "):null,r=t.endTime?t.endTime.split(" "):null;return{$orig:o,g0:a,g1:r}}))),o=e.activityList.length;e.$mp.data=Object.assign({},{$root:{l0:n,g2:o}})},r=!1,i=[];a._withStripped=!0},174:function(e,t,n){"use strict";n.r(t);var o=n(175),a=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);t["default"]=a.a},175:function(e,t,n){"use strict";(function(e){var o=n(4);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(56)),r=o(n(58)),i=o(n(18)),c=n(176),s=n(166),u=function(){n.e("components/CustomTabbar").then(function(){return resolve(n(348))}.bind(null,n)).catch(n.oe)},l=function(){n.e("components/LangSwitch").then(function(){return resolve(n(355))}.bind(null,n)).catch(n.oe)},d={components:{CustomTabbar:u,LangSwitch:l},data:function(){return{lang:e.getStorageSync("lang")||"zh",searchValue:"",currentCategory:0,currentSort:0,contentHeight:0,categoryList:[{id:0,name:"全部",name_en:"All"}],activityList:[],currentPage:1,pageSize:10,total:0,loading:!1,hasMore:!0,loadMoreStatus:"loadmore"}},computed:{filteredActivities:function(){var e=(0,i.default)(this.activityList);return 0===this.currentSort?e=e.slice().sort((function(e,t){return t.state-e.state})):1===this.currentSort?e=e.slice().sort((function(e,t){return new Date(t.beginTime)-new Date(e.beginTime)})):2===this.currentSort&&(e=e.slice().sort((function(e,t){return e.price-t.price}))),e}},onLoad:function(t){var n=this;console.log(t),getApp().globalData&&getApp().globalData.lang?this.lang=getApp().globalData.lang:this.lang=e.getStorageSync("lang")||"zh",e.$on("lang-change",(function(e){n.lang=e,n.setNavigationBarTitle(),n.onLangChange(e)})),this.setNavigationBarTitle(),setTimeout((function(){n.calcContentHeight()}),500),this.getCategoryList(),this.getActivityList()},onShow:function(){var t=0,n=e.getStorageSync("selectedCategory");""!==n&&void 0!==n&&(t=n,e.removeStorageSync("selectedCategory"),-1!==t&&t>=0&&(this.currentCategory=t))},onUnload:function(){e.$off("languageChanged"),e.$off("lang-change")},methods:{calcContentHeight:function(){var t=this,n=e.getSystemInfoSync();console.log(n),e.createSelectorQuery().select(".custom-tabbar").boundingClientRect((function(e){console.log("大考方便",e);var o=e?e.height:0;t.contentHeight=n.windowHeight-o})).exec()},onCategoryClick:function(e){console.log("点击分类索引:",e),console.log("分类列表:",this.categoryList),console.log("选择的分类:",this.categoryList[e]),this.currentCategory=e,this.getActivityList(!0)},onSortChange:function(e){this.currentSort=e},onSearch:function(e){this.searchValue=e,this.getActivityList(!0)},navigateToEventDetail:function(t){e.navigateTo({url:"/pages/events/detail?id=".concat(t.id)})},onLangChange:function(e){this.lang=e,this.setNavigationBarTitle(),this.searchValue&&this.searchValue.trim()&&this.getActivityList(!0)},setNavigationBarTitle:function(){e.setNavigationBarTitle({title:"zh"===this.lang?"活动列表":"Events"})},getCategoryList:function(){var e=this;return(0,r.default)(a.default.mark((function t(){var n,o,r,u,l,d;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:t.prev=0,n=[],o=1,r=20,u=!0;case 5:if(!u){t.next=13;break}return t.next=8,(0,c.getCategoryList)({pg:o,size:r});case 8:l=t.sent,console.log("获取分类列表第".concat(o,"页响应:"),l),l&&0===l.code&&l.data?(d=l.data.map((function(e){return{id:e.id,name:e.name||"",name_en:e.nameEN||e.name||"",types:e.types,logo:(0,s.formatImageUrl)(e.logo||""),sort:e.sort||0}})),n=[].concat((0,i.default)(n),(0,i.default)(d)),d.length<r?u=!1:o++):(console.error("获取分类列表失败:",l),u=!1),t.next=5;break;case 13:e.categoryList=[{id:0,name:"全部",name_en:"All",types:0}].concat((0,i.default)(n)),console.log("获取到的所有分类:",e.categoryList),t.next=20;break;case 17:t.prev=17,t.t0=t["catch"](0),console.error("获取分类列表异常:",t.t0);case 20:case"end":return t.stop()}}),t,null,[[0,17]])})))()},getActivityList:function(){var t=arguments,n=this;return(0,r.default)(a.default.mark((function o(){var r,u,l,d,g;return a.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:return r=!(t.length>0&&void 0!==t[0])||t[0],o.prev=1,r?(n.currentPage=1,n.activityList=[],e.showLoading({title:"zh"===n.lang?"加载中...":"Loading..."})):n.loadMoreStatus="loading",u={pg:n.currentPage,size:n.pageSize},n.currentCategory>0&&n.categoryList[n.currentCategory]&&(l=n.categoryList[n.currentCategory],0!==l.id&&(u.types=l.id),console.log("选择的分类:",l,"传递的types参数:",u.types)),n.searchValue&&n.searchValue.trim()&&("zh"===n.lang?u.title=n.searchValue.trim():u.titleEN=n.searchValue.trim()),o.next=8,(0,c.getOperationList)(u);case 8:d=o.sent,console.log("获取活动列表响应:",d),d&&0===d.code&&d.data?(g=d.data.map((function(e){return{id:e.id,types:e.types,title:e.title||"",titleEN:e.titleEN||"",beginTime:e.beginTime||"",endTime:e.endTime||"",img:(0,s.formatImageUrl)(e.img||""),contents:e.contents||"",contentsEN:e.contentsEN||"",address:e.address||"",addressEN:e.addressEN||"",price:e.price||0,nums:e.nums||0,regNums:e.regNums||0,integral:e.integral||0,state:e.state,typesName:e.typesName||""}})),n.activityList=r?g:[].concat((0,i.default)(n.activityList),(0,i.default)(g)),n.total=d.count||0,g.length<n.pageSize?(n.loadMoreStatus="nomore",n.hasMore=!1):(n.loadMoreStatus="loadmore",n.hasMore=!0)):(console.error("获取活动列表失败:",d),e.showToast({title:"zh"===n.lang?"获取数据失败":"Failed to load data",icon:"none"}),n.loadMoreStatus="loadmore"),o.next=18;break;case 13:o.prev=13,o.t0=o["catch"](1),console.error("获取活动列表异常:",o.t0),e.showToast({title:"zh"===n.lang?"网络错误":"Network error",icon:"none"}),n.loadMoreStatus="loadmore";case 18:return o.prev=18,r&&e.hideLoading(),o.finish(18);case 21:case"end":return o.stop()}}),o,null,[[1,13,18,21]])})))()},loadMore:function(){var e=this;return(0,r.default)(a.default.mark((function t(){return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("loading"!==e.loadMoreStatus&&e.hasMore){t.next=2;break}return t.abrupt("return");case 2:return e.currentPage++,t.next=5,e.getActivityList(!1);case 5:case"end":return t.stop()}}),t)})))()}}};t.default=d}).call(this,n(2)["default"])},177:function(e,t,n){"use strict";n.r(t);var o=n(178),a=n.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(r);t["default"]=a.a},178:function(e,t,n){}},[[170,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/events/index.js.map