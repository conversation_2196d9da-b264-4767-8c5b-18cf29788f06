(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/events/index"],{170:function(e,t,n){"use strict";(function(e,t){var r=n(4);n(26);r(n(25));var a=r(n(171));e.__webpack_require_UNI_MP_PLUGIN__=n,t(a.default)}).call(this,n(1)["default"],n(2)["createPage"])},171:function(e,t,n){"use strict";n.r(t);var r=n(172),a=n(174);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n(177);var i,c=n(32),s=Object(c["default"])(a["default"],r["render"],r["staticRenderFns"],!1,null,"338b5f55",null,!1,r["components"],i);s.options.__file="pages/events/index.vue",t["default"]=s.exports},172:function(e,t,n){"use strict";n.r(t);var r=n(173);n.d(t,"render",(function(){return r["render"]})),n.d(t,"staticRenderFns",(function(){return r["staticRenderFns"]})),n.d(t,"recyclableRender",(function(){return r["recyclableRender"]})),n.d(t,"components",(function(){return r["components"]}))},173:function(e,t,n){"use strict";var r;n.r(t),n.d(t,"render",(function(){return a})),n.d(t,"staticRenderFns",(function(){return i})),n.d(t,"recyclableRender",(function(){return o})),n.d(t,"components",(function(){return r}));try{r={uSearch:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-search/u-search")]).then(n.bind(null,283))},uImage:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-image/u-image")]).then(n.bind(null,315))},uLoadmore:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-loadmore/u-loadmore")]).then(n.bind(null,332))}}}catch(c){if(-1===c.message.indexOf("Cannot find module")||-1===c.message.indexOf(".vue"))throw c;console.error(c.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var a=function(){var e=this,t=e.$createElement,n=(e._self._c,e.__map(e.filteredActivities,(function(t,n){var r=e.__get_orig(t),a=t.beginTime?t.beginTime.split(" "):null,o=t.endTime?t.endTime.split(" "):null;return{$orig:r,g0:a,g1:o}}))),r=e.activityList.length;e.$mp.data=Object.assign({},{$root:{l0:n,g2:r}})},o=!1,i=[];a._withStripped=!0},174:function(e,t,n){"use strict";n.r(t);var r=n(175),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},175:function(e,t,n){"use strict";(function(e){var r=n(4);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n(56)),o=r(n(58)),i=r(n(18)),c=n(176),s=n(166),u=function(){n.e("components/CustomTabbar").then(function(){return resolve(n(348))}.bind(null,n)).catch(n.oe)},l=function(){n.e("components/LangSwitch").then(function(){return resolve(n(355))}.bind(null,n)).catch(n.oe)},d={components:{CustomTabbar:u,LangSwitch:l},data:function(){return{lang:e.getStorageSync("lang")||"zh",searchValue:"",currentCategory:0,currentSort:0,contentHeight:0,categoryList:[{id:0,name:"全部",name_en:"All"}],activityList:[],currentPage:1,pageSize:10,total:0,loading:!1,hasMore:!0,loadMoreStatus:"loadmore"}},computed:{filteredActivities:function(){var e=(0,i.default)(this.activityList);return 0===this.currentSort?e=e.slice().sort((function(e,t){return t.state-e.state})):1===this.currentSort?e=e.slice().sort((function(e,t){return new Date(t.beginTime)-new Date(e.beginTime)})):2===this.currentSort&&(e=e.slice().sort((function(e,t){return e.price-t.price}))),e}},onLoad:function(t){var n=this;console.log(t),getApp().globalData&&getApp().globalData.lang?this.lang=getApp().globalData.lang:this.lang=e.getStorageSync("lang")||"zh",e.$on("lang-change",(function(e){n.lang=e,n.setNavigationBarTitle(),n.onLangChange(e)})),this.setNavigationBarTitle(),setTimeout((function(){n.calcContentHeight()}),500),this.getCategoryList(),this.getActivityList()},onShow:function(){var t=0,n=e.getStorageSync("selectedCategory");""!==n&&void 0!==n&&(t=n,e.removeStorageSync("selectedCategory"),-1!==t&&t>=0&&(this.currentCategory=t))},onUnload:function(){e.$off("languageChanged"),e.$off("lang-change")},methods:{calcContentHeight:function(){var t=this,n=e.getSystemInfoSync();console.log(n),e.createSelectorQuery().select(".custom-tabbar").boundingClientRect((function(e){console.log("大考方便",e);var r=e?e.height:0;t.contentHeight=n.windowHeight-r})).exec()},onCategoryClick:function(e){this.currentCategory=e,this.getActivityList(!0)},onSortChange:function(e){this.currentSort=e},onSearch:function(e){this.searchValue=e,this.getActivityList(!0)},navigateToEventDetail:function(t){e.navigateTo({url:"/pages/events/detail?id=".concat(t.id)})},onLangChange:function(e){this.lang=e,this.setNavigationBarTitle(),this.searchValue&&this.searchValue.trim()&&this.getActivityList(!0)},setNavigationBarTitle:function(){e.setNavigationBarTitle({title:"zh"===this.lang?"活动列表":"Events"})},getCategoryList:function(){var e=this;return(0,o.default)(a.default.mark((function t(){var n,r;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,c.getCategoryList)({});case 3:n=t.sent,console.log("获取分类列表响应:",n),n&&0===n.code&&n.data?(r=n.data.map((function(e){return{id:e.id,name:e.name||"",name_en:e.nameEN||e.name||"",types:e.types,logo:(0,s.formatImageUrl)(e.logo||""),sort:e.sort||0}})),e.categoryList=[{id:0,name:"全部",name_en:"All",types:0}].concat((0,i.default)(r))):console.error("获取分类列表失败:",n),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](0),console.error("获取分类列表异常:",t.t0);case 11:case"end":return t.stop()}}),t,null,[[0,8]])})))()},getActivityList:function(){var t=arguments,n=this;return(0,o.default)(a.default.mark((function r(){var o,u,l,d,g;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return o=!(t.length>0&&void 0!==t[0])||t[0],r.prev=1,o?(n.currentPage=1,n.activityList=[],e.showLoading({title:"zh"===n.lang?"加载中...":"Loading..."})):n.loadMoreStatus="loading",u={pg:n.currentPage,size:n.pageSize},n.currentCategory>0&&n.categoryList[n.currentCategory]&&(l=n.categoryList[n.currentCategory],0!==l.id&&(u.types=l.types||l.id)),n.searchValue&&n.searchValue.trim()&&("zh"===n.lang?u.title=n.searchValue.trim():u.titleEN=n.searchValue.trim()),r.next=8,(0,c.getOperationList)(u);case 8:d=r.sent,console.log("获取活动列表响应:",d),d&&0===d.code&&d.data?(g=d.data.map((function(e){return{id:e.id,types:e.types,title:e.title||"",titleEN:e.titleEN||"",beginTime:e.beginTime||"",endTime:e.endTime||"",img:(0,s.formatImageUrl)(e.img||""),contents:e.contents||"",contentsEN:e.contentsEN||"",address:e.address||"",addressEN:e.addressEN||"",price:e.price||0,nums:e.nums||0,regNums:e.regNums||0,integral:e.integral||0,state:e.state,typesName:e.typesName||""}})),n.activityList=o?g:[].concat((0,i.default)(n.activityList),(0,i.default)(g)),n.total=d.count||0,g.length<n.pageSize?(n.loadMoreStatus="nomore",n.hasMore=!1):(n.loadMoreStatus="loadmore",n.hasMore=!0)):(console.error("获取活动列表失败:",d),e.showToast({title:"zh"===n.lang?"获取数据失败":"Failed to load data",icon:"none"}),n.loadMoreStatus="loadmore"),r.next=18;break;case 13:r.prev=13,r.t0=r["catch"](1),console.error("获取活动列表异常:",r.t0),e.showToast({title:"zh"===n.lang?"网络错误":"Network error",icon:"none"}),n.loadMoreStatus="loadmore";case 18:return r.prev=18,o&&e.hideLoading(),r.finish(18);case 21:case"end":return r.stop()}}),r,null,[[1,13,18,21]])})))()},loadMore:function(){var e=this;return(0,o.default)(a.default.mark((function t(){return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("loading"!==e.loadMoreStatus&&e.hasMore){t.next=2;break}return t.abrupt("return");case 2:return e.currentPage++,t.next=5,e.getActivityList(!1);case 5:case"end":return t.stop()}}),t)})))()}}};t.default=d}).call(this,n(2)["default"])},177:function(e,t,n){"use strict";n.r(t);var r=n(178),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a},178:function(e,t,n){}},[[170,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/events/index.js.map