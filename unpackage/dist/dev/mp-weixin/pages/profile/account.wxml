<view class="account-page data-v-0eb8f644"><view class="account-container data-v-0eb8f644"><view class="security-section data-v-0eb8f644"><view class="section-title data-v-0eb8f644">{{lang==='zh'?'手机号绑定':'Phone Number'}}</view><view data-event-opts="{{[['tap',[['onPhoneBinding',['$event']]]]]}}" class="security-item data-v-0eb8f644" bindtap="__e"><view class="security-info data-v-0eb8f644"><u-icon vue-id="1bcd97e8-1" name="phone" size="28" color="#2979ff" class="data-v-0eb8f644" bind:__l="__l"></u-icon><view class="security-content data-v-0eb8f644"><view class="security-label data-v-0eb8f644">{{lang==='zh'?'已绑定手机号':'Bound Phone'}}</view><view class="security-value data-v-0eb8f644">{{phoneNumber||(lang==='zh'?'未绑定':'Not Bound')}}</view></view></view><view class="security-action data-v-0eb8f644"><text class="action-text data-v-0eb8f644">{{phoneNumber?lang==='zh'?'修改':'Change':lang==='zh'?'绑定':'Bind'}}</text><u-icon vue-id="1bcd97e8-2" name="arrow-right" size="20" color="#c0c4cc" class="data-v-0eb8f644" bind:__l="__l"></u-icon></view></view></view><u-popup vue-id="1bcd97e8-3" show="{{showPhonePopup}}" mode="center" round="{{16}}" width="80%" safeAreaInsetBottom="{{false}}" class="data-v-0eb8f644" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-container data-v-0eb8f644"><view class="popup-title data-v-0eb8f644">{{phoneNumber?lang==='zh'?'修改手机号':'Change Phone Number':lang==='zh'?'绑定手机号':'Bind Phone Number'}}</view><view class="popup-form data-v-0eb8f644"><view class="form-item data-v-0eb8f644"><u-input bind:input="__e" vue-id="{{('1bcd97e8-4')+','+('1bcd97e8-3')}}" placeholder="{{lang==='zh'?'请输入手机号':'Enter phone number'}}" type="number" border="bottom" clearable="{{true}}" value="{{formPhone}}" data-event-opts="{{[['^input',[['__set_model',['','formPhone','$event',[]]]]]]}}" class="data-v-0eb8f644" bind:__l="__l"></u-input></view><view class="form-item verification-code data-v-0eb8f644"><u-input bind:input="__e" class="code-input data-v-0eb8f644" vue-id="{{('1bcd97e8-5')+','+('1bcd97e8-3')}}" placeholder="{{lang==='zh'?'请输入验证码':'Enter verification code'}}" type="number" border="bottom" clearable="{{true}}" value="{{formCode}}" data-event-opts="{{[['^input',[['__set_model',['','formCode','$event',[]]]]]]}}" bind:__l="__l"></u-input><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['code-button','data-v-0eb8f644',(codeButtonDisabled)?'disabled':'']}}" bindtap="__e">{{''+codeButtonText+''}}</view></view></view><view class="popup-buttons data-v-0eb8f644"><block wx:if="{{lang==='zh'}}"><view data-event-opts="{{[['tap',[['cancelPhoneBinding',['$event']]]]]}}" class="cancel-button data-v-0eb8f644" bindtap="__e">取消</view></block><block wx:else><view data-event-opts="{{[['tap',[['cancelPhoneBinding',['$event']]]]]}}" class="cancel-button data-v-0eb8f644" bindtap="__e">Cancel</view></block><block wx:if="{{lang==='zh'}}"><view data-event-opts="{{[['tap',[['confirmPhoneBinding',['$event']]]]]}}" class="confirm-button data-v-0eb8f644" bindtap="__e">确认</view></block><block wx:else><view data-event-opts="{{[['tap',[['confirmPhoneBinding',['$event']]]]]}}" class="confirm-button data-v-0eb8f644" bindtap="__e">Confirm</view></block></view></view></u-popup><lang-switch vue-id="1bcd97e8-6" class="data-v-0eb8f644" bind:__l="__l"></lang-switch></view></view>