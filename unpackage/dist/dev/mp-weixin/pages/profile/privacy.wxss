
.privacy-page.data-v-1e9ef8b9 {
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #f5f7fa;
}
.privacy-container.data-v-1e9ef8b9 {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
  min-height: 500rpx;
}
/* 加载状态样式 */
.loading-container.data-v-1e9ef8b9 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.loading-text.data-v-1e9ef8b9 {
  font-size: 28rpx;
  color: #2979ff;
}
/* 内容样式 */
.content-container.data-v-1e9ef8b9 {
  min-height: 400rpx;
}
.content-body.data-v-1e9ef8b9 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
}
/* 富文本内容样式优化 */
.content-body rich-text.data-v-1e9ef8b9 {
  word-break: break-all;
}

