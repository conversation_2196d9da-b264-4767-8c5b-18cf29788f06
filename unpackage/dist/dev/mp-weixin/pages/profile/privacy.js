(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/profile/privacy"],{211:function(n,t,e){"use strict";(function(n,t){var r=e(4);e(26);r(e(25));var a=r(e(212));n.__webpack_require_UNI_MP_PLUGIN__=e,t(a.default)}).call(this,e(1)["default"],e(2)["createPage"])},212:function(n,t,e){"use strict";e.r(t);var r=e(213),a=e(215);for(var o in a)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return a[n]}))}(o);e(217);var i,c=e(32),u=Object(c["default"])(a["default"],r["render"],r["staticRenderFns"],!1,null,"1e9ef8b9",null,!1,r["components"],i);u.options.__file="pages/profile/privacy.vue",t["default"]=u.exports},213:function(n,t,e){"use strict";e.r(t);var r=e(214);e.d(t,"render",(function(){return r["render"]})),e.d(t,"staticRenderFns",(function(){return r["staticRenderFns"]})),e.d(t,"recyclableRender",(function(){return r["recyclableRender"]})),e.d(t,"components",(function(){return r["components"]}))},214:function(n,t,e){"use strict";var r;e.r(t),e.d(t,"render",(function(){return a})),e.d(t,"staticRenderFns",(function(){return i})),e.d(t,"recyclableRender",(function(){return o})),e.d(t,"components",(function(){return r}));var a=function(){var n=this,t=n.$createElement;n._self._c},o=!1,i=[];a._withStripped=!0},215:function(n,t,e){"use strict";e.r(t);var r=e(216),a=e.n(r);for(var o in r)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return r[n]}))}(o);t["default"]=a.a},216:function(n,t,e){"use strict";(function(n){var r=e(4);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(e(56)),o=r(e(58)),i=e(167),c=function(){e.e("components/LangSwitch").then(function(){return resolve(e(355))}.bind(null,e)).catch(e.oe)},u={components:{LangSwitch:c},data:function(){return{lang:"zh",loading:!0,privacyContent:{title:"",content:""}}},mounted:function(){var t=n.getStorageSync("lang")||"zh";this.lang=t,n.$on("lang-change",this.onLangChange),this.setNavTitle(),this.getPrivacyContent()},beforeDestroy:function(){n.$off("lang-change",this.onLangChange)},methods:{getPrivacyContent:function(){var n=this;return(0,o.default)(a.default.mark((function t(){var e;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,n.loading=!0,t.next=4,(0,i.postSingleInfo)({id:2});case 4:e=t.sent,e&&0===e.code&&e.data?n.privacyContent={title:"",content:e.data}:(console.error("获取隐私政策失败:",e),n.setDefaultContent()),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](0),console.error("获取隐私政策异常:",t.t0),n.setDefaultContent();case 12:return t.prev=12,n.loading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,null,[[0,8,12,15]])})))()},setDefaultContent:function(){this.privacyContent={title:"",content:"zh"===this.lang?"我们致力于保护您的隐私和个人信息安全。请仔细阅读本隐私政策以了解我们如何收集、使用和保护您的信息。":"We are committed to protecting your privacy and personal information security. Please read this privacy policy carefully to understand how we collect, use and protect your information."}},onLangChange:function(n){this.lang=n,this.setNavTitle(),this.getPrivacyContent()},setNavTitle:function(){var t="zh"===this.lang?"隐私协议":"Privacy Policy";n.setNavigationBarTitle({title:t})}}};t.default=u}).call(this,e(2)["default"])},217:function(n,t,e){"use strict";e.r(t);var r=e(218),a=e.n(r);for(var o in r)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return r[n]}))}(o);t["default"]=a.a},218:function(n,t,e){}},[[211,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/privacy.js.map