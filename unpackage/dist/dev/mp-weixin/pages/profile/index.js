(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/profile/index"],{203:function(n,e,t){"use strict";(function(n,e){var o=t(4);t(26);o(t(25));var a=o(t(204));n.__webpack_require_UNI_MP_PLUGIN__=t,e(a.default)}).call(this,t(1)["default"],t(2)["createPage"])},204:function(n,e,t){"use strict";t.r(e);var o=t(205),a=t(207);for(var r in a)["default"].indexOf(r)<0&&function(n){t.d(e,n,(function(){return a[n]}))}(r);t(209);var i,u=t(32),c=Object(u["default"])(a["default"],o["render"],o["staticRenderFns"],!1,null,"14bc1b43",null,!1,o["components"],i);c.options.__file="pages/profile/index.vue",e["default"]=c.exports},205:function(n,e,t){"use strict";t.r(e);var o=t(206);t.d(e,"render",(function(){return o["render"]})),t.d(e,"staticRenderFns",(function(){return o["staticRenderFns"]})),t.d(e,"recyclableRender",(function(){return o["recyclableRender"]})),t.d(e,"components",(function(){return o["components"]}))},206:function(n,e,t){"use strict";var o;t.r(e),t.d(e,"render",(function(){return a})),t.d(e,"staticRenderFns",(function(){return i})),t.d(e,"recyclableRender",(function(){return r})),t.d(e,"components",(function(){return o}));try{o={uAvatar:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-avatar/u-avatar")]).then(t.bind(null,370))},uIcon:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(t.bind(null,323))},uButton:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-button/u-button")]).then(t.bind(null,378))},uTag:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-tag/u-tag")]).then(t.bind(null,362))}}}catch(u){if(-1===u.message.indexOf("Cannot find module")||-1===u.message.indexOf(".vue"))throw u;console.error(u.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var a=function(){var n=this,e=n.$createElement;n._self._c},r=!1,i=[];a._withStripped=!0},207:function(n,e,t){"use strict";t.r(e);var o=t(208),a=t.n(o);for(var r in o)["default"].indexOf(r)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(r);e["default"]=a.a},208:function(n,e,t){"use strict";(function(n){var o=t(4);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=o(t(56)),r=o(t(58)),i=t(176),u=function(){t.e("components/CustomTabbar").then(function(){return resolve(t(348))}.bind(null,t)).catch(t.oe)},c=function(){t.e("components/LangSwitch").then(function(){return resolve(t(355))}.bind(null,t)).catch(t.oe)},s={components:{CustomTabbar:u,LangSwitch:c},data:function(){return{lang:"zh",user:{avatar:"https://picsum.photos/200",nickname:"User123",signature:"这是个性签名",desc:"这里是用户描述信息"},points:1280}},onShow:function(){this.loadLocalUserInfo()},mounted:function(){var e=n.getStorageSync("lang")||"zh";this.lang=e,n.$on("lang-change",this.onLangChange),this.setNavTitle(),this.getUserInfo()},beforeDestroy:function(){n.$off("lang-change",this.onLangChange)},methods:{getUserInfo:function(){var e=this;return(0,r.default)(a.default.mark((function t(){var o,r;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,(0,i.postUserInfo)();case 3:o=t.sent,o&&0===o.code&&o.data?(r=o.data.head||"https://picsum.photos/200",r.startsWith("/")&&(r="https://xialingying.guaguakj.com"+r),e.user={id:o.data.userID,avatar:r,nickname:o.data.name||"User123",signature:"这是个性签名",desc:"这里是用户描述信息",level:o.data.level||0,money:o.data.money||0,cardNums:o.data.cardNums||0,isHaveEWM:o.data.isHaveEWM||0},e.points=o.data.integral||0,n.setStorageSync("userInfo",e.user),n.setStorageSync("userPoints",e.points),console.log("用户信息获取成功:",o.data)):(console.error("获取用户信息失败:",o),e.loadLocalUserInfo()),t.next=12;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("获取用户信息异常:",t.t0),e.loadLocalUserInfo(),n.showToast({title:"zh"===e.lang?"获取用户信息失败":"Failed to get user info",icon:"none"});case 12:case"end":return t.stop()}}),t,null,[[0,7]])})))()},loadLocalUserInfo:function(){var e=n.getStorageSync("userInfo");e&&(this.user=e)},onLangChange:function(n){this.lang=n,this.setNavTitle()},setNavTitle:function(){var e="zh"===this.lang?"我的":"Profile";n.setNavigationBarTitle({title:e})},onEditProfile:function(){n.navigateTo({url:"/pages/profile/edit"})},onExchange:function(){n.navigateTo({url:"/pages/points/exchange"})},onPointsRecord:function(){n.navigateTo({url:"/pages/points/record"})},onSignupManage:function(){n.navigateTo({url:"/pages/signup/manage"})},onPrivacy:function(){n.navigateTo({url:"/pages/profile/privacy"})},onAccount:function(){n.navigateTo({url:"/pages/profile/account"})},onService:function(){},onContactSuccess:function(n){console.log("客服联系成功",n)},onContactFail:function(e){console.log("客服联系失败",e),n.showToast({title:"zh"===this.lang?"客服联系失败":"Contact failed",icon:"none"})}}};e.default=s}).call(this,t(2)["default"])},209:function(n,e,t){"use strict";t.r(e);var o=t(210),a=t.n(o);for(var r in o)["default"].indexOf(r)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(r);e["default"]=a.a},210:function(n,e,t){}},[[203,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/index.js.map