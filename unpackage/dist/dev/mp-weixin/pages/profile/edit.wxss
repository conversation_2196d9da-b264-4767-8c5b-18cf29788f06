
.edit-profile-page.data-v-7683d8ae {
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #f5f7fa;
}
.edit-form.data-v-7683d8ae {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}
.form-item.data-v-7683d8ae {
  margin-bottom: 40rpx;
}
.item-label.data-v-7683d8ae {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.avatar-item.data-v-7683d8ae {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 50rpx;
}
.avatar-wrapper.data-v-7683d8ae {
  position: relative;
  margin-top: 20rpx;
}
.avatar-edit-icon.data-v-7683d8ae {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 60rpx;
  height: 60rpx;
  background-color: #2979ff;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.avatar-edit-icon.uploading.data-v-7683d8ae {
  background-color: #ff9900;
}
.upload-mask.data-v-7683d8ae {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-text.data-v-7683d8ae {
  color: #ffffff;
  font-size: 24rpx;
}
.btn-wrapper.data-v-7683d8ae {
  margin-top: 60rpx;
  padding: 0 20rpx;
}

