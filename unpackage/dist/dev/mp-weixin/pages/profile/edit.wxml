<view class="edit-profile-page data-v-7683d8ae"><view class="edit-form data-v-7683d8ae"><view class="form-item avatar-item data-v-7683d8ae"><view class="item-label data-v-7683d8ae">{{lang==='zh'?'头像':'Avatar'}}</view><view data-event-opts="{{[['tap',[['chooseAvatar',['$event']]]]]}}" class="avatar-wrapper data-v-7683d8ae" bindtap="__e"><u-avatar vue-id="59f63b06-1" src="{{userForm.avatar}}" size="140" class="data-v-7683d8ae" bind:__l="__l"></u-avatar><view class="{{['avatar-edit-icon','data-v-7683d8ae',(uploading)?'uploading':'']}}"><block wx:if="{{!uploading}}"><u-icon vue-id="59f63b06-2" name="camera-fill" color="#ffffff" size="40" class="data-v-7683d8ae" bind:__l="__l"></u-icon></block><block wx:else><u-loading-icon vue-id="59f63b06-3" color="#ffffff" size="30" class="data-v-7683d8ae" bind:__l="__l"></u-loading-icon></block></view><block wx:if="{{uploading}}"><view class="upload-mask data-v-7683d8ae"><text class="upload-text data-v-7683d8ae">{{lang==='zh'?'上传中...':'Uploading...'}}</text></view></block></view></view><view class="form-item data-v-7683d8ae"><view class="item-label data-v-7683d8ae">{{lang==='zh'?'昵称':'Nickname'}}</view><u-input bind:input="__e" vue-id="59f63b06-4" placeholder="{{lang==='zh'?'请输入昵称':'Enter nickname'}}" border="bottom" clearable="{{true}}" value="{{userForm.nickname}}" data-event-opts="{{[['^input',[['__set_model',['$0','nickname','$event',[]],['userForm']]]]]}}" class="data-v-7683d8ae" bind:__l="__l"></u-input></view><view class="form-item data-v-7683d8ae"><view class="item-label data-v-7683d8ae">{{lang==='zh'?'个性签名':'Signature'}}</view><u-input bind:input="__e" vue-id="59f63b06-5" placeholder="{{lang==='zh'?'请输入个性签名':'Enter signature'}}" border="bottom" clearable="{{true}}" value="{{userForm.signature}}" data-event-opts="{{[['^input',[['__set_model',['$0','signature','$event',[]],['userForm']]]]]}}" class="data-v-7683d8ae" bind:__l="__l"></u-input></view><view class="form-item data-v-7683d8ae"><view class="item-label data-v-7683d8ae">{{lang==='zh'?'个人简介':'Description'}}</view><u-textarea bind:input="__e" vue-id="59f63b06-6" placeholder="{{lang==='zh'?'请输入个人简介':'Enter description'}}" count="{{true}}" maxlength="{{100}}" height="200" value="{{userForm.desc}}" data-event-opts="{{[['^input',[['__set_model',['$0','desc','$event',[]],['userForm']]]]]}}" class="data-v-7683d8ae" bind:__l="__l"></u-textarea></view><view class="btn-wrapper data-v-7683d8ae"><u-button vue-id="59f63b06-7" type="primary" text="{{lang==='zh'?'保存':'Save'}}" data-event-opts="{{[['^click',[['saveProfile']]]]}}" bind:click="__e" class="data-v-7683d8ae" bind:__l="__l"></u-button></view></view><lang-switch vue-id="59f63b06-8" class="data-v-7683d8ae" bind:__l="__l"></lang-switch></view>