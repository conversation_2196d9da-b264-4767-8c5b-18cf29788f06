(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/profile/edit"],{219:function(e,n,t){"use strict";(function(e,n){var o=t(4);t(26);o(t(25));var a=o(t(220));e.__webpack_require_UNI_MP_PLUGIN__=t,n(a.default)}).call(this,t(1)["default"],t(2)["createPage"])},220:function(e,n,t){"use strict";t.r(n);var o=t(221),a=t(223);for(var r in a)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return a[e]}))}(r);t(225);var i,u=t(32),s=Object(u["default"])(a["default"],o["render"],o["staticRenderFns"],!1,null,"7683d8ae",null,!1,o["components"],i);s.options.__file="pages/profile/edit.vue",n["default"]=s.exports},221:function(e,n,t){"use strict";t.r(n);var o=t(222);t.d(n,"render",(function(){return o["render"]})),t.d(n,"staticRenderFns",(function(){return o["staticRenderFns"]})),t.d(n,"recyclableRender",(function(){return o["recyclableRender"]})),t.d(n,"components",(function(){return o["components"]}))},222:function(e,n,t){"use strict";var o;t.r(n),t.d(n,"render",(function(){return a})),t.d(n,"staticRenderFns",(function(){return i})),t.d(n,"recyclableRender",(function(){return r})),t.d(n,"components",(function(){return o}));try{o={uAvatar:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-avatar/u-avatar")]).then(t.bind(null,370))},uIcon:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(t.bind(null,323))},uLoadingIcon:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-loading-icon/u-loading-icon")]).then(t.bind(null,388))},uInput:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-input/u-input")]).then(t.bind(null,396))},uTextarea:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-textarea/u-textarea")]).then(t.bind(null,404))},uButton:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-button/u-button")]).then(t.bind(null,378))}}}catch(u){if(-1===u.message.indexOf("Cannot find module")||-1===u.message.indexOf(".vue"))throw u;console.error(u.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var a=function(){var e=this,n=e.$createElement;e._self._c},r=!1,i=[];a._withStripped=!0},223:function(e,n,t){"use strict";t.r(n);var o=t(224),a=t.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(r);n["default"]=a.a},224:function(e,n,t){"use strict";(function(e){var o=t(4);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=o(t(56)),r=o(t(58)),i=t(166),u=t(208),s=function(){t.e("components/LangSwitch").then(function(){return resolve(t(355))}.bind(null,t)).catch(t.oe)},c={components:{LangSwitch:s},data:function(){return{lang:"zh",uploading:!1,userForm:{avatar:"",nickname:"",signature:"",desc:""}}},mounted:function(){var n=e.getStorageSync("lang")||"zh";this.lang=n,e.$on("lang-change",this.onLangChange),this.setNavTitle(),this.getUserInfo()},beforeDestroy:function(){e.$off("lang-change",this.onLangChange)},methods:{onLangChange:function(e){this.lang=e,this.setNavTitle()},setNavTitle:function(){var n="zh"===this.lang?"编辑资料":"Edit Profile";e.setNavigationBarTitle({title:n})},getUserInfo:function(){var n=e.getStorageSync("userInfo")||{avatar:"https://picsum.photos/200",nickname:"User123",signature:"这是个性签名",desc:"这里是用户描述信息"};this.userForm={avatar:n.avatar||"https://picsum.photos/200",nickname:n.nickname||"User123",signature:n.signature||"这是个性签名",desc:n.desc||"这里是用户描述信息"}},chooseAvatar:function(){var n=this;this.uploading?e.showToast({title:"zh"===this.lang?"正在上传中，请稍候":"Uploading, please wait",icon:"none"}):e.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"],success:function(e){var t=e.tempFilePaths[0];n.uploadAvatar(t)},fail:function(t){console.error("选择图片失败:",t),e.showToast({title:"zh"===n.lang?"选择图片失败":"Failed to select image",icon:"none"})}})},uploadAvatar:function(n){var t=this;this.uploading=!0;var o={temfile:n,type:"avatar"};(0,i.File)(o,(function(n){console.log("头像上传成功:",n),t.uploading=!1,n.data&&n.data.length>0&&n.data[0].url?(t.userForm.avatar=n.data[0].url,e.showToast({title:"zh"===t.lang?"头像上传成功":"Avatar uploaded successfully",icon:"success"})):(console.error("上传响应数据格式异常:",n),e.showToast({title:"zh"===t.lang?"上传失败，返回数据异常":"Upload failed, invalid response",icon:"none"}))}),(function(n){console.error("头像上传失败:",n),t.uploading=!1;var o=n.msg||n.message||"上传失败";e.showToast({title:"zh"===t.lang?o:"Upload failed",icon:"none"})}))},saveProfile:function(){var n=this;return(0,r.default)(a.default.mark((function t(){var o,r;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n.userForm.nickname){t.next=2;break}return t.abrupt("return",e.showToast({title:"zh"===n.lang?"请输入昵称":"Please enter nickname",icon:"none"}));case 2:return t.prev=2,e.showLoading({title:"zh"===n.lang?"保存中...":"Saving..."}),t.next=6,(0,u.postUpdateMe)({name:n.userForm.nickname,head:n.userForm.avatar});case 6:o=t.sent,e.hideLoading(),o&&0===o.code?(r=e.getStorageSync("userInfo")||{},r.nickname=n.userForm.nickname,r.avatar=n.userForm.avatar,r.signature=n.userForm.signature,r.desc=n.userForm.desc,e.setStorageSync("userInfo",r),e.showToast({title:"zh"===n.lang?"保存成功":"Saved successfully",icon:"success"}),setTimeout((function(){e.navigateBack()}),1500)):e.showToast({title:"zh"===n.lang?"保存失败，请重试":"Save failed, please try again",icon:"none"}),t.next=16;break;case 11:t.prev=11,t.t0=t["catch"](2),console.error("保存用户信息失败:",t.t0),e.hideLoading(),e.showToast({title:"zh"===n.lang?"保存失败，请检查网络":"Save failed, please check network",icon:"none"});case 16:case"end":return t.stop()}}),t,null,[[2,11]])})))()}}};n.default=c}).call(this,t(2)["default"])},225:function(e,n,t){"use strict";t.r(n);var o=t(226),a=t.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(r);n["default"]=a.a},226:function(e,n,t){}},[[219,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/edit.js.map