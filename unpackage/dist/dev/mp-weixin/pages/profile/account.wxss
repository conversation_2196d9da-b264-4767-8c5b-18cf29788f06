
.account-page.data-v-0eb8f644 {
  min-height: 100vh;
  background-color: #f8f8f8;
}
.account-container.data-v-0eb8f644 {
  padding: 24rpx;
}
.security-section.data-v-0eb8f644 {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  padding: 0 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
}
.section-title.data-v-0eb8f644 {
  font-size: 28rpx;
  color: #909399;
  padding: 24rpx 0 16rpx;
}
.security-item.data-v-0eb8f644 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-top: 1px solid #f5f5f5;
}
.security-item.data-v-0eb8f644:first-child {
  border-top: none;
}
.security-info.data-v-0eb8f644 {
  display: flex;
  align-items: center;
  flex: 1;
}
.security-content.data-v-0eb8f644 {
  flex: 1;
  margin-left: 24rpx; /* 增加图标与文本的距离 */
}
.security-label.data-v-0eb8f644 {
  font-size: 28rpx;
  color: #303133;
  margin-bottom: 4rpx;
}
.security-value.data-v-0eb8f644 {
  font-size: 24rpx;
  color: #909399;
}
.security-action.data-v-0eb8f644 {
  display: flex;
  align-items: center;
}
.action-text.data-v-0eb8f644 {
  font-size: 24rpx;
  color: #2979ff;
  margin-right: 8rpx;
}
/* 弹窗样式 */
.popup-container.data-v-0eb8f644 {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  border-radius: 14rpx;
}
.popup-title.data-v-0eb8f644 {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #303133;
}
.popup-form.data-v-0eb8f644 {
  margin-bottom: 40rpx;
}
.form-item.data-v-0eb8f644 {
  margin-bottom: 30rpx;
}
.verification-code.data-v-0eb8f644 {
  display: flex;
  align-items: center;
}
.code-input.data-v-0eb8f644 {
  flex: 1;
  margin-right: 20rpx;
}
.code-button.data-v-0eb8f644 {
  width: 180rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2979ff;
  color: #ffffff;
  font-size: 26rpx;
  border-radius: 8rpx;
  margin-left: 20rpx;
}
.code-button.disabled.data-v-0eb8f644 {
  background-color: #a0cfff;
  color: #ffffff;
}
.popup-buttons.data-v-0eb8f644 {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}
.cancel-button.data-v-0eb8f644 {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #f2f2f2;
  color: #606266;
  border-radius: 8rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
}
.confirm-button.data-v-0eb8f644 {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #2979ff;
  color: #ffffff;
  border-radius: 8rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}

