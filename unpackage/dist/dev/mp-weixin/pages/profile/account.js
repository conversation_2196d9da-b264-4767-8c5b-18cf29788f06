(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/profile/account"],{227:function(n,e,o){"use strict";(function(n,e){var t=o(4);o(26);t(o(25));var i=t(o(228));n.__webpack_require_UNI_MP_PLUGIN__=o,e(i.default)}).call(this,o(1)["default"],o(2)["createPage"])},228:function(n,e,o){"use strict";o.r(e);var t=o(229),i=o(231);for(var r in i)["default"].indexOf(r)<0&&function(n){o.d(e,n,(function(){return i[n]}))}(r);o(233);var u,c=o(32),s=Object(c["default"])(i["default"],t["render"],t["staticRenderFns"],!1,null,"0eb8f644",null,!1,t["components"],u);s.options.__file="pages/profile/account.vue",e["default"]=s.exports},229:function(n,e,o){"use strict";o.r(e);var t=o(230);o.d(e,"render",(function(){return t["render"]})),o.d(e,"staticRenderFns",(function(){return t["staticRenderFns"]})),o.d(e,"recyclableRender",(function(){return t["recyclableRender"]})),o.d(e,"components",(function(){return t["components"]}))},230:function(n,e,o){"use strict";var t;o.r(e),o.d(e,"render",(function(){return i})),o.d(e,"staticRenderFns",(function(){return u})),o.d(e,"recyclableRender",(function(){return r})),o.d(e,"components",(function(){return t}));try{t={uIcon:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(o.bind(null,323))},uPopup:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-popup/u-popup")]).then(o.bind(null,340))},uInput:function(){return Promise.all([o.e("common/vendor"),o.e("node-modules/uview-ui/components/u-input/u-input")]).then(o.bind(null,396))}}}catch(c){if(-1===c.message.indexOf("Cannot find module")||-1===c.message.indexOf(".vue"))throw c;console.error(c.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var i=function(){var n=this,e=n.$createElement;n._self._c;n._isMounted||(n.e0=function(e){!n.codeButtonDisabled&&n.sendVerificationCode()})},r=!1,u=[];i._withStripped=!0},231:function(n,e,o){"use strict";o.r(e);var t=o(232),i=o.n(t);for(var r in t)["default"].indexOf(r)<0&&function(n){o.d(e,n,(function(){return t[n]}))}(r);e["default"]=i.a},232:function(n,e,o){"use strict";(function(n){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t=function(){o.e("components/LangSwitch").then(function(){return resolve(o(355))}.bind(null,o)).catch(o.oe)},i={components:{LangSwitch:t},data:function(){return{lang:"zh",phoneNumber:"",showPhonePopup:!1,formPhone:"",formCode:"",codeCountdown:0,codeTimer:null}},computed:{codeButtonText:function(){return this.codeCountdown>0?"".concat(this.codeCountdown,"s"):"zh"===this.lang?"获取验证码":"Get Code"},codeButtonDisabled:function(){return this.codeCountdown>0}},onLoad:function(){var e=n.getStorageSync("lang")||"zh";this.lang=e,n.$on("lang-change",this.onLangChange),this.setNavTitle(),this.getAccountSecurityInfo()},onUnload:function(){n.$off("lang-change",this.onLangChange),this.codeTimer&&clearInterval(this.codeTimer)},methods:{onLangChange:function(n){this.lang=n,this.setNavTitle()},setNavTitle:function(){var e="zh"===this.lang?"账户安全":"Account Security";n.setNavigationBarTitle({title:e})},getAccountSecurityInfo:function(){var n=this;setTimeout((function(){n.phoneNumber=""}),500)},onPhoneBinding:function(){this.formPhone="",this.formCode="",this.showPhonePopup=!0},sendVerificationCode:function(){var e=this;if(!this.formPhone||11!==this.formPhone.length)return n.showToast({title:"zh"===this.lang?"请输入正确的手机号":"Please enter a valid phone number",icon:"none"});this.codeCountdown=60,this.codeTimer=setInterval((function(){e.codeCountdown--,e.codeCountdown<=0&&clearInterval(e.codeTimer)}),1e3),n.showToast({title:"zh"===this.lang?"验证码已发送":"Verification code sent",icon:"success"})},cancelPhoneBinding:function(){this.showPhonePopup=!1},confirmPhoneBinding:function(){var e=this;return this.formPhone&&11===this.formPhone.length?this.formCode&&6===this.formCode.length?(n.showLoading({title:"zh"===this.lang?"处理中...":"Processing..."}),void setTimeout((function(){n.hideLoading(),e.showPhonePopup=!1,e.phoneNumber=e.formPhone.substring(0,3)+"****"+e.formPhone.substring(7),n.showToast({title:"zh"===e.lang?"手机号绑定成功":"Phone number bound successfully",icon:"success"})}),1500)):n.showToast({title:"zh"===this.lang?"请输入6位验证码":"Please enter 6-digit verification code",icon:"none"}):n.showToast({title:"zh"===this.lang?"请输入正确的手机号":"Please enter a valid phone number",icon:"none"})}}};e.default=i}).call(this,o(2)["default"])},233:function(n,e,o){"use strict";o.r(e);var t=o(234),i=o.n(t);for(var r in t)["default"].indexOf(r)<0&&function(n){o.d(e,n,(function(){return t[n]}))}(r);e["default"]=i.a},234:function(n,e,o){}},[[227,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/account.js.map