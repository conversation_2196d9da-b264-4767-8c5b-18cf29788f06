
.profile-page.data-v-14bc1b43 {
  min-height: 100vh;
  box-sizing: border-box;
  padding-bottom: 100rpx;
  padding-top: 20rpx;
}
.profile-header.data-v-14bc1b43 {
  background: #f5f7fa;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
  padding: 0 24rpx;
  margin: 0 24rpx;
  margin-bottom: 24rpx;
  box-sizing: border-box;
}
.profile-header-inner.data-v-14bc1b43 {
  display: flex;
  align-items: center;
  padding: 32rpx 0 24rpx 0;
  position: relative;
}
.profile-info.data-v-14bc1b43 {
  flex: 1;
  margin-left: 24rpx;
}
.profile-nick.data-v-14bc1b43 {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  display: flex;
  align-items: center;
}
.level-badge.data-v-14bc1b43 {
  background: linear-gradient(45deg, #ff9900, #ffcc00);
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  margin-left: 12rpx;
  font-weight: normal;
}
.profile-sign.data-v-14bc1b43 {
  font-size: 26rpx;
  color: #888;
  margin-top: 8rpx;
}
.profile-desc.data-v-14bc1b43 {
  font-size: 24rpx;
  color: #aaa;
  margin-top: 8rpx;
}
.profile-money.data-v-14bc1b43 {
  font-size: 24rpx;
  color: #19be6b;
  margin-top: 8rpx;
  font-weight: 500;
}
.loading-text.data-v-14bc1b43 {
  font-size: 22rpx;
  color: #2979ff;
  margin-top: 8rpx;
}
.profile-edit.data-v-14bc1b43 {
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  color: #c0c4cc;
}
.section-card.data-v-14bc1b43 {
  background: #f5f7fa;
  border-radius: 12rpx;
  margin: 0 24rpx 24rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
  border: 1rpx solid #f0f0f0;
  padding-bottom: 8rpx;
}
.profile-points-bar.data-v-14bc1b43 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
}
.points-left.data-v-14bc1b43 {
  display: flex;
  align-items: baseline;
}
.points-right.data-v-14bc1b43 {
  flex: 1;
  margin-left: 24rpx;
  max-width: 60%;
}
.points-num.data-v-14bc1b43 {
  color: #ff9900;
  font-weight: bold;
  font-size: 40rpx;
}
.points-unit.data-v-14bc1b43 {
  font-size: 26rpx;
  color: #888;
  margin-left: 8rpx;
}
.exchange-btn.data-v-14bc1b43 {
  width: 100%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.divider.data-v-14bc1b43 {
  height: 1px;
  background-color: #eee;
  margin: 0;
}
.points-record.data-v-14bc1b43 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
}
.record-text.data-v-14bc1b43 {
  font-size: 28rpx;
  color: #333;
}
.section-title.data-v-14bc1b43 {
  font-size: 28rpx;
  font-weight: bold;
  padding: 24rpx 0 8rpx 24rpx;
  color: #2979ff;
}
.signup-card.data-v-14bc1b43 {
  margin-top: 0;
  padding: 24rpx;
  background: #f5f7fa;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
  border: 1rpx solid #f0f0f0;
}
.signup-header.data-v-14bc1b43 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.signup-title.data-v-14bc1b43 {
  display: flex;
  align-items: center;
}
.signup-title text.data-v-14bc1b43 {
  font-size: 32rpx;
  font-weight: bold;
  color: #2979ff;
}
.signup-icon.data-v-14bc1b43 {
  margin-right: 12rpx;
}
.signup-content.data-v-14bc1b43 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 12rpx;
}
.signup-desc.data-v-14bc1b43 {
  font-size: 26rpx;
  color: #666;
  flex: 1;
  margin-right: 16rpx;
}
.signup-tag.data-v-14bc1b43 {
  margin-left: 16rpx;
}
.settings-card.data-v-14bc1b43 {
  margin-top: 0;
  padding: 24rpx;
  background: #f5f7fa;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
  border: 1rpx solid #f0f0f0;
}
.settings-item.data-v-14bc1b43 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}
.settings-left.data-v-14bc1b43 {
  display: flex;
  align-items: center;
}
.settings-title.data-v-14bc1b43 {
  font-size: 28rpx;
  color: #333;
  margin-left: 16rpx;
}
.settings-divider.data-v-14bc1b43 {
  height: 1px;
  background-color: #eee;
  margin: 8rpx 0;
}
.custom-icon.data-v-14bc1b43 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 12rpx;
}
.privacy-icon.data-v-14bc1b43 {
  background-color: #e6f2ff; /* 浅蓝色背景 */
}
.security-icon.data-v-14bc1b43 {
  background-color: #fff5e6; /* 浅橙色背景 */
}
.service-icon.data-v-14bc1b43 {
  background-color: #e6f9f0; /* 浅绿色背景 */
}
.contact-btn.data-v-14bc1b43 {
  padding: 0;
  margin: 0;
  line-height: 1;
  height: auto;
  background-color: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 0;
  width: auto;
  overflow: visible;
}
.contact-btn.data-v-14bc1b43::after {
  border: none;
}
.contact-btn-full.data-v-14bc1b43 {
  width: 100%;
  display: block;
  padding: 0;
  margin: 0;
  background-color: transparent;
  border: none;
  line-height: normal;
  text-align: left;
}
.contact-btn-full.data-v-14bc1b43::after {
  border: none;
}
.service-item.data-v-14bc1b43 {
  width: 100%;
  box-sizing: border-box;
}
.arrow-right.data-v-14bc1b43 {
  display: flex;
  align-items: center;
}

