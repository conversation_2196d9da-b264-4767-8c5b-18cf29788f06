(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/survey/result"],{275:function(e,n,t){"use strict";(function(e,n){var i=t(4);t(26);i(t(25));var o=i(t(276));e.__webpack_require_UNI_MP_PLUGIN__=t,n(o.default)}).call(this,t(1)["default"],t(2)["createPage"])},276:function(e,n,t){"use strict";t.r(n);var i=t(277),o=t(279);for(var a in o)["default"].indexOf(a)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(a);t(281);var r,c=t(32),s=Object(c["default"])(o["default"],i["render"],i["staticRenderFns"],!1,null,null,null,!1,i["components"],r);s.options.__file="pages/survey/result.vue",n["default"]=s.exports},277:function(e,n,t){"use strict";t.r(n);var i=t(278);t.d(n,"render",(function(){return i["render"]})),t.d(n,"staticRenderFns",(function(){return i["staticRenderFns"]})),t.d(n,"recyclableRender",(function(){return i["recyclableRender"]})),t.d(n,"components",(function(){return i["components"]}))},278:function(e,n,t){"use strict";var i;t.r(n),t.d(n,"render",(function(){return o})),t.d(n,"staticRenderFns",(function(){return r})),t.d(n,"recyclableRender",(function(){return a})),t.d(n,"components",(function(){return i}));try{i={uIcon:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(t.bind(null,323))},uButton:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-button/u-button")]).then(t.bind(null,378))}}}catch(c){if(-1===c.message.indexOf("Cannot find module")||-1===c.message.indexOf(".vue"))throw c;console.error(c.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var o=function(){var e=this,n=e.$createElement;e._self._c},a=!1,r=[];o._withStripped=!0},279:function(e,n,t){"use strict";t.r(n);var i=t(280),o=t.n(i);for(var a in i)["default"].indexOf(a)<0&&function(e){t.d(n,e,(function(){return i[e]}))}(a);n["default"]=o.a},280:function(e,n,t){"use strict";(function(e){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i=function(){t.e("components/LangSwitch").then(function(){return resolve(t(355))}.bind(null,t)).catch(t.oe)},o={components:{LangSwitch:i},data:function(){return{lang:e.getStorageSync("lang")||"zh",activityId:null,result:{language:85,logic:70,creativity:90,social:65,physical:75},recommendedCourses:[{icon:"edit-pen",name:"创意绘画课程",name_en:"Creative Drawing Course",desc:"根据孩子的创造力水平，推荐参加创意绘画课程，培养艺术感知能力。",desc_en:"Based on your child's creativity level, we recommend the Creative Drawing Course to develop artistic perception."},{icon:"chat",name:"英语口语强化班",name_en:"English Speaking Intensive Class",desc:"提高孩子的语言表达能力，增强沟通自信。",desc_en:"Improve your child's language expression skills and enhance communication confidence."},{icon:"grid",name:"团队协作训练营",name_en:"Team Collaboration Camp",desc:"通过团队活动提升孩子的社交能力和协作精神。",desc_en:"Enhance your child's social skills and collaborative spirit through team activities."}],suggestions:{zh:"根据问卷结果，您的孩子在创造力方面表现出色，建议多参加艺术类和创新思维类的活动。语言能力也较强，可以通过阅读和口语练习进一步提升。社交能力相对较弱，建议参加更多团队活动，培养合作精神和人际交往能力。",en:"Based on the survey results, your child shows excellent performance in creativity. We recommend more participation in artistic and innovative thinking activities. Language ability is also strong and can be further improved through reading and speaking practice. Social skills are relatively weak, so we suggest participating in more team activities to develop cooperation and interpersonal skills."}}},onLoad:function(n){n.id&&(this.activityId=n.id),this.lang=e.getStorageSync("lang")||"zh",getApp().globalData&&getApp().globalData.lang&&(this.lang=getApp().globalData.lang),e.$on("lang-change",this.onLangChange),this.setNavigationBarTitle()},onUnload:function(){e.$off("lang-change",this.onLangChange)},methods:{viewActivityDetail:function(){e.navigateTo({url:"/pages/events/detail?id=".concat(this.activityId)})},downloadReport:function(){var n=this;e.showLoading({title:"zh"===this.lang?"准备下载...":"Preparing download..."}),setTimeout((function(){e.hideLoading(),e.showToast({title:"zh"===n.lang?"报告已保存到手机":"Report saved to your device",icon:"success"})}),2e3)},goBack:function(){e.navigateBack()},onLangChange:function(e){this.lang=e,this.setNavigationBarTitle()},setNavigationBarTitle:function(){e.setNavigationBarTitle({title:"zh"===this.lang?"调查结果反馈":"Survey Result"})}}};n.default=o}).call(this,t(2)["default"])},281:function(e,n,t){"use strict";t.r(n);var i=t(282),o=t.n(i);for(var a in i)["default"].indexOf(a)<0&&function(e){t.d(n,e,(function(){return i[e]}))}(a);n["default"]=o.a},282:function(e,n,t){}},[[275,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/survey/result.js.map