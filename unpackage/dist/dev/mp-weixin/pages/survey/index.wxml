<view class="survey-page"><view class="survey-intro"><view class="intro-title">{{lang==='zh'?'亲爱的家长':'Dear Parent'}}</view><view class="intro-text">{{''+$root.t0+''}}</view></view><view class="survey-content"><block wx:for="{{questions}}" wx:for-item="question" wx:for-index="index" wx:key="index"><view class="question-item"><view class="question-title">{{index+1+". "+(lang==='zh'?question.title:question.title_en)}}</view><block wx:if="{{question.type==='radio'}}"><view class="options-list"><u-radio-group bind:input="__e" vue-id="{{'cdaef920-1-'+index}}" placement="column" value="{{answers[index]}}" data-event-opts="{{[['^input',[['__set_model',['$0',index,'$event',[]],['answers']]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{lang==='zh'?question.options:question.options_en}}" wx:for-item="option" wx:for-index="optIndex" wx:key="optIndex"><u-radio vue-id="{{('cdaef920-2-'+index+'-'+optIndex)+','+('cdaef920-1-'+index)}}" label="{{option}}" name="{{optIndex}}" customStyle="{{({marginBottom:'20rpx'})}}" bind:__l="__l"></u-radio></block></u-radio-group></view></block><block wx:if="{{question.type==='checkbox'}}"><view class="options-list"><u-checkbox-group bind:input="__e" vue-id="{{'cdaef920-3-'+index}}" placement="column" value="{{answers[index]}}" data-event-opts="{{[['^input',[['__set_model',['$0',index,'$event',[]],['answers']]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{lang==='zh'?question.options:question.options_en}}" wx:for-item="option" wx:for-index="optIndex" wx:key="optIndex"><u-checkbox vue-id="{{('cdaef920-4-'+index+'-'+optIndex)+','+('cdaef920-3-'+index)}}" label="{{option}}" name="{{optIndex}}" customStyle="{{({marginBottom:'20rpx'})}}" bind:__l="__l"></u-checkbox></block></u-checkbox-group></view></block><block wx:if="{{question.type==='text'}}"><view class="text-input"><u-input bind:input="__e" vue-id="{{'cdaef920-5-'+index}}" type="text" border="bottom" placeholder="{{lang==='zh'?'请输入您的回答':'Please enter your answer'}}" value="{{answers[index]}}" data-event-opts="{{[['^input',[['__set_model',['$0',index,'$event',[]],['answers']]]]]}}" bind:__l="__l"></u-input></view></block></view></block></view><view class="submit-section safe-area-inset-bottom"><u-button vue-id="cdaef920-6" type="primary" data-event-opts="{{[['^click',[['submitSurvey']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">{{lang==='zh'?'提交问卷':'Submit Survey'}}</u-button></view><lang-switch vue-id="cdaef920-7" bind:__l="__l"></lang-switch></view>