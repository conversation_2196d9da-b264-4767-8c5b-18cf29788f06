(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/survey/index"],{267:function(e,n,t){"use strict";(function(e,n){var o=t(4);t(26);o(t(25));var i=o(t(268));e.__webpack_require_UNI_MP_PLUGIN__=t,n(i.default)}).call(this,t(1)["default"],t(2)["createPage"])},268:function(e,n,t){"use strict";t.r(n);var o=t(269),i=t(271);for(var r in i)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return i[e]}))}(r);t(273);var a,u=t(32),s=Object(u["default"])(i["default"],o["render"],o["staticRenderFns"],!1,null,null,null,!1,o["components"],a);s.options.__file="pages/survey/index.vue",n["default"]=s.exports},269:function(e,n,t){"use strict";t.r(n);var o=t(270);t.d(n,"render",(function(){return o["render"]})),t.d(n,"staticRenderFns",(function(){return o["staticRenderFns"]})),t.d(n,"recyclableRender",(function(){return o["recyclableRender"]})),t.d(n,"components",(function(){return o["components"]}))},270:function(e,n,t){"use strict";var o;t.r(n),t.d(n,"render",(function(){return i})),t.d(n,"staticRenderFns",(function(){return a})),t.d(n,"recyclableRender",(function(){return r})),t.d(n,"components",(function(){return o}));try{o={uRadioGroup:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-radio-group/u-radio-group")]).then(t.bind(null,436))},uRadio:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-radio/u-radio")]).then(t.bind(null,444))},uCheckboxGroup:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-checkbox-group/u-checkbox-group")]).then(t.bind(null,452))},uCheckbox:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-checkbox/u-checkbox")]).then(t.bind(null,428))},uInput:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-input/u-input")]).then(t.bind(null,396))},uButton:function(){return Promise.all([t.e("common/vendor"),t.e("node-modules/uview-ui/components/u-button/u-button")]).then(t.bind(null,378))}}}catch(u){if(-1===u.message.indexOf("Cannot find module")||-1===u.message.indexOf(".vue"))throw u;console.error(u.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var i=function(){var e=this,n=e.$createElement,t=(e._self._c,"zh"===e.lang?"为了更好地了解您孩子的认知水平，请您花几分钟时间填写以下问卷。这将帮助我们为您的孩子提供更加个性化的培训课程。":"To better understand your child's cognitive level, please take a few minutes to complete the following questionnaire. This will help us provide more personalized training courses for your child.");e.$mp.data=Object.assign({},{$root:{t0:t}})},r=!1,a=[];i._withStripped=!0},271:function(e,n,t){"use strict";t.r(n);var o=t(272),i=t.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(r);n["default"]=i.a},272:function(e,n,t){"use strict";(function(e){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=function(){t.e("components/LangSwitch").then(function(){return resolve(t(355))}.bind(null,t)).catch(t.oe)},i={components:{LangSwitch:o},data:function(){return{lang:e.getStorageSync("lang")||"zh",activityId:null,answers:[],questions:[{type:"radio",title:"您的孩子年龄是？",title_en:"What is your child's age?",options:["3-5岁","6-8岁","9-12岁","13-15岁","16岁以上"],options_en:["3-5 years","6-8 years","9-12 years","13-15 years","16+ years"]},{type:"radio",title:"您的孩子是否有参加过类似的培训课程？",title_en:"Has your child participated in similar training courses before?",options:["是，多次参加","是，参加过1-2次","没有参加过"],options_en:["Yes, multiple times","Yes, 1-2 times","No, never"]},{type:"checkbox",title:"您的孩子对以下哪些领域表现出兴趣？（可多选）",title_en:"In which of the following areas does your child show interest? (Multiple choices)",options:["艺术与创意","体育运动","科学探索","语言学习","数学逻辑","音乐舞蹈","社交活动"],options_en:["Arts & Creativity","Sports","Science Exploration","Language Learning","Mathematical Logic","Music & Dance","Social Activities"]},{type:"radio",title:"您的孩子在学习新知识时的接受能力如何？",title_en:"How would you rate your child's ability to learn new knowledge?",options:["非常快","较快","一般","需要反复学习","学习困难"],options_en:["Very fast","Fast","Average","Needs repetition","Difficult"]},{type:"radio",title:"您的孩子在团队活动中通常扮演什么角色？",title_en:"What role does your child usually play in team activities?",options:["领导者","积极参与者","跟随者","观察者","不喜欢团队活动"],options_en:["Leader","Active participant","Follower","Observer","Dislikes team activities"]},{type:"text",title:"您对孩子参加本次培训有什么特别的期望或需求？",title_en:"Do you have any specific expectations or requirements for your child's participation in this training?"}]}},onLoad:function(n){n.id&&(this.activityId=n.id),this.initAnswers(),this.lang=e.getStorageSync("lang")||"zh",getApp().globalData&&getApp().globalData.lang&&(this.lang=getApp().globalData.lang),e.$on("lang-change",this.onLangChange),this.setNavigationBarTitle()},onUnload:function(){e.$off("lang-change",this.onLangChange)},methods:{initAnswers:function(){this.answers=this.questions.map((function(e){return"checkbox"===e.type?[]:""}))},onLangChange:function(e){this.lang=e,this.setNavigationBarTitle()},setNavigationBarTitle:function(){e.setNavigationBarTitle({title:"zh"===this.lang?"认知水平调查问卷":"Cognitive Level Survey"})},submitSurvey:function(){var n=this,t=this.answers.findIndex((function(e,n){return Array.isArray(e)?0===e.length:""===e}));-1===t?(e.showLoading({title:"zh"===this.lang?"提交中...":"Submitting..."}),setTimeout((function(){e.hideLoading(),e.showToast({title:"zh"===n.lang?"提交成功":"Submitted successfully",icon:"success"}),setTimeout((function(){e.navigateTo({url:"/pages/survey/result?id=".concat(n.activityId)})}),1500)}),2e3)):e.showToast({title:"zh"===this.lang?"请回答第".concat(t+1,"个问题"):"Please answer question ".concat(t+1),icon:"none"})}}};n.default=i}).call(this,t(2)["default"])},273:function(e,n,t){"use strict";t.r(n);var o=t(274),i=t.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(r);n["default"]=i.a},274:function(e,n,t){}},[[267,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/survey/index.js.map