<view class="result-page"><view class="result-card"><view class="card-header"><u-icon vue-id="5815fe5d-1" name="checkmark-circle" color="#19be6b" size="60" bind:__l="__l"></u-icon><view class="header-text">{{lang==='zh'?'问卷已完成':'Survey Completed'}}</view></view><view class="divider"></view><view class="result-section"><view class="section-title">{{lang==='zh'?'认知水平评估':'Cognitive Level Assessment'}}</view><view class="level-chart"><view class="chart-item"><view class="chart-label">{{lang==='zh'?'语言能力':'Language'}}</view><view class="chart-bar"><view class="chart-fill" style="{{'width:'+(result.language+'%')+';'}}"></view></view><view class="chart-value">{{result.language+"%"}}</view></view><view class="chart-item"><view class="chart-label">{{lang==='zh'?'逻辑思维':'Logic'}}</view><view class="chart-bar"><view class="chart-fill" style="{{'width:'+(result.logic+'%')+';'}}"></view></view><view class="chart-value">{{result.logic+"%"}}</view></view><view class="chart-item"><view class="chart-label">{{lang==='zh'?'创造力':'Creativity'}}</view><view class="chart-bar"><view class="chart-fill" style="{{'width:'+(result.creativity+'%')+';'}}"></view></view><view class="chart-value">{{result.creativity+"%"}}</view></view><view class="chart-item"><view class="chart-label">{{lang==='zh'?'社交能力':'Social'}}</view><view class="chart-bar"><view class="chart-fill" style="{{'width:'+(result.social+'%')+';'}}"></view></view><view class="chart-value">{{result.social+"%"}}</view></view><view class="chart-item"><view class="chart-label">{{lang==='zh'?'运动能力':'Physical'}}</view><view class="chart-bar"><view class="chart-fill" style="{{'width:'+(result.physical+'%')+';'}}"></view></view><view class="chart-value">{{result.physical+"%"}}</view></view></view></view><view class="divider"></view><view class="result-section"><view class="section-title">{{lang==='zh'?'个性化建议':'Personalized Suggestions'}}</view><view class="suggestion-content">{{''+(lang==='zh'?suggestions.zh:suggestions.en)+''}}</view></view></view><view class="button-group safe-area-inset-bottom"><u-button vue-id="5815fe5d-2" type="primary" data-event-opts="{{[['^click',[['viewActivityDetail']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">{{lang==='zh'?'查看活动详情':'View Activity Details'}}</u-button></view><lang-switch vue-id="5815fe5d-3" bind:__l="__l"></lang-switch></view>