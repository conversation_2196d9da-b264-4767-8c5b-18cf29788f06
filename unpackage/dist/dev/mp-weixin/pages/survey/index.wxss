
.survey-page {
  min-height: 100vh;
  padding-bottom: 180rpx;
  box-sizing: border-box;
}
.survey-intro {
  background-color: #f8f8f8;
  padding: 30rpx;
  margin: 30rpx;
  border-radius: 16rpx;
}
.intro-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  color: #303133;
}
.intro-text {
  font-size: 28rpx;
  color: #606266;
  line-height: 1.6;
}
.survey-content {
  padding: 0 30rpx;
  margin-bottom: 30rpx;
}
.question-item {
  margin-bottom: 40rpx;
  background-color: #ffffff;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.question-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 24rpx;
}
.options-list {
  padding-left: 20rpx;
}
.text-input {
  margin-top: 20rpx;
}
.submit-section {
  padding: 40rpx 30rpx;
  position: fixed;
  bottom: 0;
  box-sizing: border-box;
  left: 0;
  z-index: 9999999;
  width: 100%;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}
.safe-area-inset-bottom {
  padding-bottom: calc(10rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(10rpx + env(safe-area-inset-bottom));
}

