
.result-page {
  min-height: 100vh;
  padding-bottom: 40rpx;
  box-sizing: border-box;
  background-color: #f5f5f5;
  padding-top: 20rpx;
}
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.navbar-left, .navbar-right {
  padding: 10rpx;
  width: 80rpx;
}
.navbar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #303133;
  flex-grow: 1;
  text-align: center;
}
.result-card {
  margin: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.card-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 30rpx;
}
.header-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #303133;
  margin-top: 20rpx;
}
.divider {
  height: 1rpx;
  background-color: #ebeef5;
  margin: 0 30rpx;
}
.result-section {
  padding: 30rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 24rpx;
}
.level-chart {
}
.chart-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.chart-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #606266;
}
.chart-bar {
  flex: 1;
  height: 30rpx;
  background-color: #ebeef5;
  border-radius: 15rpx;
  overflow: hidden;
  margin: 0 20rpx;
}
.chart-fill {
  height: 100%;
  background-color: #409eff;
  border-radius: 15rpx;
}
.chart-value {
  width: 80rpx;
  font-size: 24rpx;
  color: #909399;
  text-align: right;
}
.course-list {
  margin-top: 20rpx;
}
.course-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #ebeef5;
}
.course-item:last-child {
  border-bottom: none;
}
.course-icon {
  margin-right: 20rpx;
  display: flex;
  align-items: center;
}
.course-info {
  flex: 1;
}
.course-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8rpx;
}
.course-desc {
  font-size: 26rpx;
  color: #606266;
  line-height: 1.5;
}
.suggestion-content {
  font-size: 28rpx;
  color: #606266;
  line-height: 1.6;
}
.button-group {
  margin: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.safe-area-inset-bottom {
  padding-bottom: calc(40rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
}

