<view class="exchange-page data-v-45560ae6"><view class="points-info data-v-45560ae6"><view class="points-title data-v-45560ae6">{{lang==='zh'?'我的积分':'My Points'}}</view><view class="points-value data-v-45560ae6">{{points}}</view></view><view class="exchange-tabs data-v-45560ae6"><u-tabs vue-id="656bd2e0-1" list="{{tabList}}" current="{{currentTab}}" activeStyle="{{({color:'#4285f4',fontWeight:'bold',transform:'scale(1.05)'})}}" inactiveStyle="{{({color:'#666',fontWeight:'normal',transform:'scale(1)'})}}" itemStyle="padding-left: 60rpx; padding-right: 60rpx; height: 80rpx;" scrollable="{{false}}" lineColor="#4285f4" lineWidth="40" data-event-opts="{{[['^click',[['onTabChange']]]]}}" bind:click="__e" class="data-v-45560ae6" bind:__l="__l"></u-tabs></view><view class="exchange-list data-v-45560ae6"><block wx:if="{{currentTab===0}}"><block class="data-v-45560ae6"><block wx:for="{{activityList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['viewActivityDetail',['$0'],[[['activityList','',index]]]]]]]}}" class="exchange-item data-v-45560ae6" bindtap="__e"><view class="item-main data-v-45560ae6"><u-image class="item-image data-v-45560ae6" vue-id="{{'656bd2e0-2-'+index}}" src="{{item.image}}" width="200rpx" height="160rpx" radius="8" bind:__l="__l"></u-image><view class="item-content data-v-45560ae6"><view class="item-title data-v-45560ae6">{{item.title}}</view><view class="item-desc data-v-45560ae6">{{item.desc}}</view></view></view><view class="item-footer data-v-45560ae6"><view class="item-points data-v-45560ae6">{{item.points+" "+(lang==='zh'?'积分':'points')}}</view><view data-event-opts="{{[['tap',[['joinActivity',['$0'],[[['activityList','',index]]]]]]]}}" class="exchange-btn data-v-45560ae6" catchtap="__e">{{''+(lang==='zh'?'立即报名':'Join Now')+''}}</view></view></view></block></block></block><block wx:if="{{currentTab===1}}"><block class="data-v-45560ae6"><block wx:for="{{giftList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['viewGiftDetail',['$0'],[[['giftList','',index]]]]]]]}}" class="exchange-item data-v-45560ae6" bindtap="__e"><view class="item-main data-v-45560ae6"><u-image class="item-image data-v-45560ae6" vue-id="{{'656bd2e0-3-'+index}}" src="{{item.image}}" width="200rpx" height="160rpx" radius="8" bind:__l="__l"></u-image><view class="item-content data-v-45560ae6"><view class="item-title data-v-45560ae6">{{item.title}}</view><view class="item-desc data-v-45560ae6">{{item.desc}}</view></view></view><view class="item-footer data-v-45560ae6"><view class="item-points data-v-45560ae6">{{item.points+" "+(lang==='zh'?'积分':'points')}}</view><view data-event-opts="{{[['tap',[['exchangeGift',['$0'],[[['giftList','',index]]]]]]]}}" class="exchange-btn data-v-45560ae6" catchtap="__e">{{''+(lang==='zh'?'立即兑换':'Exchange')+''}}</view></view></view></block></block></block><block wx:if="{{$root.g0===0}}"><view class="empty-tip data-v-45560ae6"><u-empty vue-id="656bd2e0-4" mode="data" text="{{lang==='zh'?'暂无可兑换项目':'No items available'}}" class="data-v-45560ae6" bind:__l="__l"></u-empty></view></block></view><u-popup vue-id="656bd2e0-5" show="{{showActivityDetail}}" mode="center" round="16" safeAreaInsetBottom="{{false}}" data-event-opts="{{[['^close',[['closeActivityDetail']]]]}}" bind:close="__e" class="data-v-45560ae6" bind:__l="__l" vue-slots="{{['default']}}"><view class="detail-popup data-v-45560ae6"><view class="detail-header data-v-45560ae6"><text class="detail-title data-v-45560ae6">{{lang==='zh'?'活动详情':'Activity Detail'}}</text><u-icon vue-id="{{('656bd2e0-6')+','+('656bd2e0-5')}}" name="close" size="24" color="#999" data-event-opts="{{[['^click',[['closeActivityDetail']]]]}}" bind:click="__e" class="data-v-45560ae6" bind:__l="__l"></u-icon></view><view class="detail-content data-v-45560ae6"><u-image vue-id="{{('656bd2e0-7')+','+('656bd2e0-5')}}" src="{{currentActivity.image}}" width="100%" height="300rpx" radius="8" class="data-v-45560ae6" bind:__l="__l"></u-image><view class="detail-item data-v-45560ae6"><text class="detail-label data-v-45560ae6">{{lang==='zh'?'活动名称':'Name'}}</text><text class="detail-value data-v-45560ae6">{{currentActivity.title}}</text></view><view class="detail-item data-v-45560ae6"><text class="detail-label data-v-45560ae6">{{lang==='zh'?'所需积分':'Points'}}</text><text class="detail-value data-v-45560ae6">{{currentActivity.points}}</text></view><view class="detail-item data-v-45560ae6"><text class="detail-label data-v-45560ae6">{{lang==='zh'?'活动时间':'Time'}}</text><text class="detail-value data-v-45560ae6">{{currentActivity.time}}</text></view><view class="detail-item data-v-45560ae6"><text class="detail-label data-v-45560ae6">{{lang==='zh'?'活动地点':'Location'}}</text><text class="detail-value data-v-45560ae6">{{currentActivity.location}}</text></view><view class="detail-item data-v-45560ae6"><text class="detail-label data-v-45560ae6">{{lang==='zh'?'活动详情':'Description'}}</text><text class="detail-value data-v-45560ae6">{{currentActivity.desc}}</text></view></view><view class="detail-footer data-v-45560ae6"><u-button vue-id="{{('656bd2e0-8')+','+('656bd2e0-5')}}" type="primary" data-event-opts="{{[['^click',[['joinActivity',['$0'],['currentActivity']]]]]}}" bind:click="__e" class="data-v-45560ae6" bind:__l="__l" vue-slots="{{['default']}}">{{''+(lang==='zh'?'立即报名':'Join Now')+''}}</u-button></view></view></u-popup><u-popup vue-id="656bd2e0-9" show="{{showGiftDetail}}" mode="center" round="16" safeAreaInsetBottom="{{fakse}}" data-event-opts="{{[['^close',[['closeGiftDetail']]]]}}" bind:close="__e" class="data-v-45560ae6" bind:__l="__l" vue-slots="{{['default']}}"><view class="detail-popup data-v-45560ae6"><view class="detail-header data-v-45560ae6"><text class="detail-title data-v-45560ae6">{{lang==='zh'?'礼品详情':'Gift Detail'}}</text><u-icon vue-id="{{('656bd2e0-10')+','+('656bd2e0-9')}}" name="close" size="24" color="#999" data-event-opts="{{[['^click',[['closeGiftDetail']]]]}}" bind:click="__e" class="data-v-45560ae6" bind:__l="__l"></u-icon></view><view class="detail-content data-v-45560ae6"><u-image vue-id="{{('656bd2e0-11')+','+('656bd2e0-9')}}" src="{{currentGift.image}}" width="100%" height="300rpx" radius="8" class="data-v-45560ae6" bind:__l="__l"></u-image><view class="detail-item data-v-45560ae6"><text class="detail-label data-v-45560ae6">{{lang==='zh'?'礼品名称':'Name'}}</text><text class="detail-value data-v-45560ae6">{{currentGift.title}}</text></view><view class="detail-item data-v-45560ae6"><text class="detail-label data-v-45560ae6">{{lang==='zh'?'所需积分':'Points'}}</text><text class="detail-value data-v-45560ae6">{{currentGift.points}}</text></view><view class="detail-item data-v-45560ae6"><text class="detail-label data-v-45560ae6">{{lang==='zh'?'礼品详情':'Description'}}</text><text class="detail-value data-v-45560ae6">{{currentGift.desc}}</text></view></view><view class="detail-footer data-v-45560ae6"><u-button vue-id="{{('656bd2e0-12')+','+('656bd2e0-9')}}" type="primary" data-event-opts="{{[['^click',[['exchangeGift',['$0'],['currentGift']]]]]}}" bind:click="__e" class="data-v-45560ae6" bind:__l="__l" vue-slots="{{['default']}}">{{''+(lang==='zh'?'立即兑换':'Exchange')+''}}</u-button></view></view></u-popup><u-popup vue-id="656bd2e0-13" show="{{showActivityJoin}}" mode="center" round="16" safeAreaInsetBottom="{{false}}" data-event-opts="{{[['^close',[['closeActivityJoin']]]]}}" bind:close="__e" class="data-v-45560ae6" bind:__l="__l" vue-slots="{{['default']}}"><view class="form-popup data-v-45560ae6"><view class="form-header data-v-45560ae6"><text class="form-title data-v-45560ae6">{{lang==='zh'?'活动报名':'Activity Registration'}}</text><u-icon vue-id="{{('656bd2e0-14')+','+('656bd2e0-13')}}" name="close" size="24" color="#999" data-event-opts="{{[['^click',[['closeActivityJoin']]]]}}" bind:click="__e" class="data-v-45560ae6" bind:__l="__l"></u-icon></view><view class="form-content data-v-45560ae6"><view class="form-section data-v-45560ae6"><view class="form-section-title data-v-45560ae6">{{lang==='zh'?'选择报名信息：':'Select Profile:'}}</view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="profile-selector-new data-v-45560ae6" bindtap="__e"><text class="data-v-45560ae6">{{selectedProfile?selectedProfile.name:lang==='zh'?'请选择报名资料':'Please select a profile'}}</text><u-icon vue-id="{{('656bd2e0-15')+','+('656bd2e0-13')}}" name="arrow-right" size="36rpx" color="#909399" class="data-v-45560ae6" bind:__l="__l"></u-icon></view></view><block wx:if="{{selectedProfile}}"><view class="selected-profile-info-new data-v-45560ae6"><view class="profile-item-row data-v-45560ae6"><text class="profile-item-label data-v-45560ae6">{{lang==='zh'?'姓名：':'Name:'}}</text><text class="profile-item-value data-v-45560ae6">{{selectedProfile.name}}</text></view><view class="profile-item-row data-v-45560ae6"><text class="profile-item-label data-v-45560ae6">{{lang==='zh'?'手机：':'Phone:'}}</text><text class="profile-item-value data-v-45560ae6">{{selectedProfile.phone}}</text></view><block wx:if="{{selectedProfile.email}}"><view class="profile-item-row data-v-45560ae6"><text class="profile-item-label data-v-45560ae6">{{lang==='zh'?'邮箱：':'Email:'}}</text><text class="profile-item-value data-v-45560ae6">{{selectedProfile.email}}</text></view></block><block wx:if="{{selectedProfile.idCard}}"><view class="profile-item-row data-v-45560ae6"><text class="profile-item-label data-v-45560ae6">{{lang==='zh'?'身份证：':'ID Card:'}}</text><text class="profile-item-value data-v-45560ae6">{{selectedProfile.idCard}}</text></view></block><block wx:if="{{selectedProfile.address}}"><view class="profile-item-row data-v-45560ae6"><text class="profile-item-label data-v-45560ae6">{{lang==='zh'?'地址：':'Address:'}}</text><text class="profile-item-value data-v-45560ae6">{{selectedProfile.address}}</text></view></block><block wx:if="{{selectedProfile.school}}"><view class="profile-item-row data-v-45560ae6"><text class="profile-item-label data-v-45560ae6">{{lang==='zh'?'学校：':'School:'}}</text><text class="profile-item-value data-v-45560ae6">{{selectedProfile.school}}</text></view></block></view></block><view class="form-tips data-v-45560ae6"><u-icon style="margin-right:10rpx;" vue-id="{{('656bd2e0-16')+','+('656bd2e0-13')}}" name="info-circle" size="32rpx" color="#ff9900" class="data-v-45560ae6" bind:__l="__l"></u-icon><text class="data-v-45560ae6">{{(lang==='zh'?'报名成功后将消耗':'Registration will consume')+" "+currentActivity.points+" "+(lang==='zh'?'积分':'points')}}</text></view><view data-event-opts="{{[['tap',[['goToManageProfiles',['$event']]]]]}}" class="form-link data-v-45560ae6" bindtap="__e">{{''+(lang==='zh'?'管理报名资料 >':'Manage profiles >')+''}}</view></view><view class="form-footer data-v-45560ae6"><u-button vue-id="{{('656bd2e0-17')+','+('656bd2e0-13')}}" type="primary" disabled="{{!selectedProfile}}" data-event-opts="{{[['^click',[['submitActivityJoin']]]]}}" bind:click="__e" class="data-v-45560ae6" bind:__l="__l" vue-slots="{{['default']}}">{{''+(lang==='zh'?'确认报名':'Confirm')+''}}</u-button></view></view></u-popup><u-popup vue-id="656bd2e0-18" show="{{showProfileSelect}}" mode="bottom" safeAreaInsetBottom="{{true}}" data-event-opts="{{[['^close',[['e1']]]]}}" bind:close="__e" class="data-v-45560ae6" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup-content data-v-45560ae6"><view class="popup-header data-v-45560ae6"><text class="data-v-45560ae6">{{lang==='zh'?'选择报名资料':'Select Profile'}}</text></view><scroll-view class="profile-list data-v-45560ae6" scroll-y="{{true}}"><block wx:for="{{profileList}}" wx:for-item="profile" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectProfile',['$0'],[[['profileList','',index]]]]]]]}}" class="{{['profile-item','data-v-45560ae6',(selectedProfileIndex===index)?'selected':'',(profile.isDefault)?'default':'']}}" bindtap="__e"><view class="profile-info data-v-45560ae6"><text class="profile-name data-v-45560ae6">{{profile.name}}</text><text class="profile-phone data-v-45560ae6">{{profile.phone}}</text></view><block wx:if="{{selectedProfileIndex===index}}"><u-icon vue-id="{{('656bd2e0-19-'+index)+','+('656bd2e0-18')}}" name="checkmark" color="#2979ff" size="40rpx" class="data-v-45560ae6" bind:__l="__l"></u-icon></block></view></block></scroll-view><view class="popup-buttons data-v-45560ae6"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="cancel-button data-v-45560ae6" bindtap="__e">{{''+(lang==='zh'?'取消':'Cancel')+''}}</view><view data-event-opts="{{[['tap',[['confirmProfileSelection',['$event']]]]]}}" class="confirm-button data-v-45560ae6" bindtap="__e">{{''+(lang==='zh'?'确认':'Confirm')+''}}</view></view><view data-event-opts="{{[['tap',[['goToManageProfiles',['$event']]]]]}}" class="manage-profiles-link data-v-45560ae6" bindtap="__e">{{''+(lang==='zh'?'管理报名资料 >':'Manage Profiles >')+''}}</view></view></u-popup><u-popup vue-id="656bd2e0-20" show="{{showGiftExchange}}" mode="center" round="16" safeAreaInsetBottom="{{fakse}}" data-event-opts="{{[['^close',[['closeGiftExchange']]]]}}" bind:close="__e" class="data-v-45560ae6" bind:__l="__l" vue-slots="{{['default']}}"><view class="form-popup data-v-45560ae6"><view class="form-header data-v-45560ae6"><text class="form-title data-v-45560ae6">{{lang==='zh'?'礼品兑换':'Gift Exchange'}}</text><u-icon vue-id="{{('656bd2e0-21')+','+('656bd2e0-20')}}" name="close" size="24" color="#999" data-event-opts="{{[['^click',[['closeGiftExchange']]]]}}" bind:click="__e" class="data-v-45560ae6" bind:__l="__l"></u-icon></view><view class="form-content data-v-45560ae6"><view class="form-item data-v-45560ae6"><text class="form-label data-v-45560ae6">{{lang==='zh'?'收件人':'Recipient'}}</text><u-input bind:input="__e" vue-id="{{('656bd2e0-22')+','+('656bd2e0-20')}}" placeholder="{{lang==='zh'?'请输入收件人姓名':'Enter recipient name'}}" border="bottom" value="{{giftForm.name}}" data-event-opts="{{[['^input',[['__set_model',['$0','name','$event',[]],['giftForm']]]]]}}" class="data-v-45560ae6" bind:__l="__l"></u-input></view><view class="form-item data-v-45560ae6"><text class="form-label data-v-45560ae6">{{lang==='zh'?'手机号':'Phone'}}</text><u-input bind:input="__e" vue-id="{{('656bd2e0-23')+','+('656bd2e0-20')}}" placeholder="{{lang==='zh'?'请输入联系电话':'Enter contact phone'}}" border="bottom" type="number" value="{{giftForm.phone}}" data-event-opts="{{[['^input',[['__set_model',['$0','phone','$event',[]],['giftForm']]]]]}}" class="data-v-45560ae6" bind:__l="__l"></u-input></view><view class="form-item data-v-45560ae6"><text class="form-label data-v-45560ae6">{{lang==='zh'?'收货地址':'Address'}}</text><u-input bind:input="__e" vue-id="{{('656bd2e0-24')+','+('656bd2e0-20')}}" placeholder="{{lang==='zh'?'请输入详细地址':'Enter detailed address'}}" border="bottom" value="{{giftForm.address}}" data-event-opts="{{[['^input',[['__set_model',['$0','address','$event',[]],['giftForm']]]]]}}" class="data-v-45560ae6" bind:__l="__l"></u-input></view><view class="form-item data-v-45560ae6"><text class="form-label data-v-45560ae6">{{lang==='zh'?'备注':'Remarks'}}</text><u-input bind:input="__e" vue-id="{{('656bd2e0-25')+','+('656bd2e0-20')}}" placeholder="{{lang==='zh'?'请输入备注信息（选填）':'Enter remarks (optional)'}}" border="bottom" value="{{giftForm.remarks}}" data-event-opts="{{[['^input',[['__set_model',['$0','remarks','$event',[]],['giftForm']]]]]}}" class="data-v-45560ae6" bind:__l="__l"></u-input></view><view class="form-tips data-v-45560ae6">{{''+(lang==='zh'?'兑换成功后将消耗':'This will cost')+" "+currentGift.points+" "+(lang==='zh'?'积分':'points')+''}}</view></view><view class="form-footer data-v-45560ae6"><u-button vue-id="{{('656bd2e0-26')+','+('656bd2e0-20')}}" type="primary" data-event-opts="{{[['^click',[['submitGiftExchange']]]]}}" bind:click="__e" class="data-v-45560ae6" bind:__l="__l" vue-slots="{{['default']}}">{{''+(lang==='zh'?'确认兑换':'Confirm')+''}}</u-button></view></view></u-popup><lang-switch vue-id="656bd2e0-27" class="data-v-45560ae6" bind:__l="__l"></lang-switch></view>