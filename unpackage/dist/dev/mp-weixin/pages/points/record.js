(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/points/record"],{235:function(t,e,n){"use strict";(function(t,e){var i=n(4);n(26);i(n(25));var o=i(n(236));t.__webpack_require_UNI_MP_PLUGIN__=n,e(o.default)}).call(this,n(1)["default"],n(2)["createPage"])},236:function(t,e,n){"use strict";n.r(e);var i=n(237),o=n(239);for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n(241);var a,c=n(32),s=Object(c["default"])(o["default"],i["render"],i["staticRenderFns"],!1,null,"18f9de58",null,!1,i["components"],a);s.options.__file="pages/points/record.vue",e["default"]=s.exports},237:function(t,e,n){"use strict";n.r(e);var i=n(238);n.d(e,"render",(function(){return i["render"]})),n.d(e,"staticRenderFns",(function(){return i["staticRenderFns"]})),n.d(e,"recyclableRender",(function(){return i["recyclableRender"]})),n.d(e,"components",(function(){return i["components"]}))},238:function(t,e,n){"use strict";var i;n.r(e),n.d(e,"render",(function(){return o})),n.d(e,"staticRenderFns",(function(){return a})),n.d(e,"recyclableRender",(function(){return r})),n.d(e,"components",(function(){return i}));try{i={uTabs:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-tabs/u-tabs")]).then(n.bind(null,412))},uIcon:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(n.bind(null,323))},uEmpty:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-empty/u-empty")]).then(n.bind(null,420))},uPopup:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-popup/u-popup")]).then(n.bind(null,340))},uButton:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-button/u-button")]).then(n.bind(null,378))}}}catch(c){if(-1===c.message.indexOf("Cannot find module")||-1===c.message.indexOf(".vue"))throw c;console.error(c.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var o=function(){var t=this,e=t.$createElement,n=(t._self._c,t.getCurrentRecords.length),i=t.getRecordTypeName(t.currentDetail.category),o="gift"===t.currentDetail.category?t.getDeliveryStatus(t.currentDetail.deliveryStatus):null;t.$mp.data=Object.assign({},{$root:{g0:n,m0:i,m1:o}})},r=!1,a=[];o._withStripped=!0},239:function(t,e,n){"use strict";n.r(e);var i=n(240),o=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},240:function(t,e,n){"use strict";(function(t){var i=n(4);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(n(18)),r=function(){n.e("components/LangSwitch").then(function(){return resolve(n(355))}.bind(null,n)).catch(n.oe)},a={components:{LangSwitch:r},data:function(){return{lang:"zh",currentTab:0,tabList:[],allRecords:[],activityRecords:[],giftRecords:[],showDetail:!1,currentDetail:{}}},computed:{getCurrentRecords:function(){switch(this.currentTab){case 0:return this.allRecords;case 1:return this.activityRecords;case 2:return this.giftRecords;default:return[]}}},onReady:function(){this.initTabs()},mounted:function(){var e=t.getStorageSync("lang")||"zh";this.lang=e,t.$on("lang-change",this.onLangChange),this.setNavTitle(),this.getPointsRecords()},beforeDestroy:function(){t.$off("lang-change",this.onLangChange)},methods:{onLangChange:function(t){this.lang=t,this.setNavTitle(),this.initTabs()},setNavTitle:function(){var e="zh"===this.lang?"积分记录":"Points Record";t.setNavigationBarTitle({title:e})},initTabs:function(){this.tabList=[{name:"zh"===this.lang?"全部":"All"},{name:"zh"===this.lang?"活动":"Activities"},{name:"zh"===this.lang?"礼品":"Gifts"}]},onTabChange:function(t){console.log(t,"大家访"),this.currentTab=t.index},getPointsRecords:function(){this.activityRecords=[{id:"act001",category:"activity",type:"add",title:"zh"===this.lang?"参与夏令营活动":"Summer Camp Activity",time:"2023-07-15 14:30",points:200,location:"zh"===this.lang?"深圳市南山区科技园":"Nanshan District, Shenzhen",duration:"zh"===this.lang?"3小时":"3 hours"},{id:"act002",category:"activity",type:"add",title:"zh"===this.lang?"完成编程挑战赛":"Coding Challenge",time:"2023-07-20 10:15",points:150,location:"zh"===this.lang?"线上":"Online",duration:"zh"===this.lang?"2小时":"2 hours"},{id:"act003",category:"activity",type:"add",title:"zh"===this.lang?"分享学习心得":"Learning Experience Sharing",time:"2023-08-05 16:00",points:50,location:"zh"===this.lang?"深圳市福田区会展中心":"Futian District, Shenzhen",duration:"zh"===this.lang?"1小时":"1 hour"}],this.giftRecords=[{id:"gift001",category:"gift",type:"minus",title:"zh"===this.lang?"兑换定制T恤":"Custom T-shirt",time:"2023-08-10 09:45",points:100,exchangeTime:"2023-08-10 09:45",deliveryStatus:"delivered",trackingNo:"SF1234567890",address:"zh"===this.lang?"广东省深圳市南山区科技园南区T3栋5楼":"Floor 5, Building T3, South Area of Science Park, Nanshan District, Shenzhen, Guangdong Province"},{id:"gift002",category:"gift",type:"minus",title:"zh"===this.lang?"兑换蓝牙耳机":"Bluetooth Headphones",time:"2023-08-15 14:20",points:300,exchangeTime:"2023-08-15 14:20",deliveryStatus:"shipping",trackingNo:"YT9876543210",address:"zh"===this.lang?"广东省深圳市南山区科技园南区T3栋5楼":"Floor 5, Building T3, South Area of Science Park, Nanshan District, Shenzhen, Guangdong Province"},{id:"gift003",category:"gift",type:"minus",title:"zh"===this.lang?"兑换电影票券":"Movie Tickets",time:"2023-08-20 11:30",points:80,exchangeTime:"2023-08-20 11:30",deliveryStatus:"virtual",trackingNo:"",address:""}],this.allRecords=[].concat((0,o.default)(this.activityRecords),(0,o.default)(this.giftRecords)).sort((function(t,e){return new Date(e.time)-new Date(t.time)}))},viewDetail:function(t){this.currentDetail=t,this.showDetail=!0},closeDetail:function(){this.showDetail=!1},getRecordTypeName:function(t){return"activity"===t?"zh"===this.lang?"活动":"Activity":"gift"===t?"zh"===this.lang?"礼品":"Gift":""},getDeliveryStatus:function(t){switch(t){case"pending":return"zh"===this.lang?"待发货":"Pending";case"shipping":return"zh"===this.lang?"运输中":"Shipping";case"delivered":return"zh"===this.lang?"已送达":"Delivered";case"virtual":return"zh"===this.lang?"虚拟物品":"Virtual Item";default:return""}},checkDelivery:function(){var e=this;t.showToast({title:"zh"===this.lang?"查询物流信息...":"Checking delivery info...",icon:"none"}),setTimeout((function(){t.showModal({title:"zh"===e.lang?"物流信息":"Delivery Information",content:"zh"===e.lang?"快递单号: ".concat(e.currentDetail.trackingNo,"\n状态: ").concat(e.getDeliveryStatus(e.currentDetail.deliveryStatus),"\n最新位置: 深圳市南山区配送中心"):"Tracking No.: ".concat(e.currentDetail.trackingNo,"\nStatus: ").concat(e.getDeliveryStatus(e.currentDetail.deliveryStatus),"\nLatest Location: Nanshan Distribution Center, Shenzhen"),showCancel:!1})}),1e3)}}};e.default=a}).call(this,n(2)["default"])},241:function(t,e,n){"use strict";n.r(e);var i=n(242),o=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},242:function(t,e,n){}},[[235,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/points/record.js.map