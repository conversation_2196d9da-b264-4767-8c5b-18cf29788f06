(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/points/exchange"],{243:function(t,e,n){"use strict";(function(t,e){var i=n(4);n(26);i(n(25));var o=i(n(244));t.__webpack_require_UNI_MP_PLUGIN__=n,e(o.default)}).call(this,n(1)["default"],n(2)["createPage"])},244:function(t,e,n){"use strict";n.r(e);var i=n(245),o=n(247);for(var s in o)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(s);n(249);var a,c=n(32),r=Object(c["default"])(o["default"],i["render"],i["staticRenderFns"],!1,null,"45560ae6",null,!1,i["components"],a);r.options.__file="pages/points/exchange.vue",e["default"]=r.exports},245:function(t,e,n){"use strict";n.r(e);var i=n(246);n.d(e,"render",(function(){return i["render"]})),n.d(e,"staticRenderFns",(function(){return i["staticRenderFns"]})),n.d(e,"recyclableRender",(function(){return i["recyclableRender"]})),n.d(e,"components",(function(){return i["components"]}))},246:function(t,e,n){"use strict";var i;n.r(e),n.d(e,"render",(function(){return o})),n.d(e,"staticRenderFns",(function(){return a})),n.d(e,"recyclableRender",(function(){return s})),n.d(e,"components",(function(){return i}));try{i={uTabs:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-tabs/u-tabs")]).then(n.bind(null,412))},uImage:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-image/u-image")]).then(n.bind(null,315))},uEmpty:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-empty/u-empty")]).then(n.bind(null,420))},uPopup:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-popup/u-popup")]).then(n.bind(null,340))},uIcon:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(n.bind(null,323))},uButton:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-button/u-button")]).then(n.bind(null,378))},uInput:function(){return Promise.all([n.e("common/vendor"),n.e("node-modules/uview-ui/components/u-input/u-input")]).then(n.bind(null,396))}}}catch(c){if(-1===c.message.indexOf("Cannot find module")||-1===c.message.indexOf(".vue"))throw c;console.error(c.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var o=function(){var t=this,e=t.$createElement,n=(t._self._c,t.getCurrentList.length);t._isMounted||(t.e0=function(e){t.showProfileSelect=!0},t.e1=function(e){t.showProfileSelect=!1},t.e2=function(e){t.showProfileSelect=!1}),t.$mp.data=Object.assign({},{$root:{g0:n}})},s=!1,a=[];o._withStripped=!0},247:function(t,e,n){"use strict";n.r(e);var i=n(248),o=n.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);e["default"]=o.a},248:function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=function(){n.e("components/LangSwitch").then(function(){return resolve(n(355))}.bind(null,n)).catch(n.oe)},o={components:{LangSwitch:i},data:function(){return{lang:"zh",points:1280,currentTab:0,tabList:[],activityList:[],giftList:[],showActivityDetail:!1,showGiftDetail:!1,showActivityJoin:!1,showGiftExchange:!1,showProfileSelect:!1,profileList:[{name:"张三",phone:"13812345678",idCard:"******************",email:"<EMAIL>",school:"深圳大学",address:"广东省深圳市南山区科技园南区",isDefault:!0},{name:"李四",phone:"13987654321",idCard:"******************",email:"<EMAIL>",school:"华南理工大学",address:"广东省广州市天河区五山路",isDefault:!1},{name:"王五",phone:"13500001111",idCard:"******************",email:"<EMAIL>",school:"北京大学",address:"北京市海淀区颐和园路5号",isDefault:!1},{name:"赵六",phone:"13600002222",idCard:"******************",email:"<EMAIL>",school:"清华大学",address:"北京市海淀区清华园1号",isDefault:!1},{name:"钱七",phone:"13700003333",idCard:"******************",email:"<EMAIL>",school:"复旦大学",address:"上海市杨浦区邯郸路220号",isDefault:!1},{name:"孙八",phone:"13800004444",idCard:"******************",email:"<EMAIL>",school:"浙江大学",address:"浙江省杭州市西湖区余杭塘路866号",isDefault:!1},{name:"周九",phone:"13900005555",idCard:"******************",email:"<EMAIL>",school:"南京大学",address:"江苏省南京市栖霞区仙林大道163号",isDefault:!1}],selectedProfile:null,currentActivity:{},currentGift:{},activityForm:{name:"",phone:"",remarks:""},giftForm:{name:"",phone:"",address:"",remarks:""}}},computed:{getCurrentList:function(){return 0===this.currentTab?this.activityList:this.giftList}},onReady:function(){this.initTabs()},mounted:function(){var e=t.getStorageSync("lang")||"zh";this.lang=e,t.$on("lang-change",this.onLangChange),this.setNavTitle(),this.getUserPoints(),this.getExchangeList(),this.initDefaultProfile()},beforeDestroy:function(){t.$off("lang-change",this.onLangChange)},methods:{onLangChange:function(t){this.lang=t,this.setNavTitle(),this.initTabs(),this.getExchangeList()},setNavTitle:function(){var e="zh"===this.lang?"积分兑换":"Points Exchange";t.setNavigationBarTitle({title:e})},initTabs:function(){this.tabList=[{name:"zh"===this.lang?"活动":"Activities"},{name:"zh"===this.lang?"礼品":"Gifts"}]},onTabChange:function(t){console.log(t,"切换标签"),this.currentTab=t.index},getUserPoints:function(){var e=t.getStorageSync("userPoints");if(void 0===e||null===e){var n=t.getStorageSync("userInfo");n&&void 0!==n.points&&(this.points=n.points)}else this.points=e},getExchangeList:function(){this.activityList=[{id:"act001",title:"zh"===this.lang?"编程技能工作坊":"Coding Skills Workshop",desc:"zh"===this.lang?"学习最新的编程技术和实践":"Learn the latest programming techniques and practices",points:100,image:"https://picsum.photos/400/300?random=1",time:"2023-09-15 14:00-17:00",location:"zh"===this.lang?"深圳市南山区科技园":"Nanshan District, Shenzhen"},{id:"act002",title:"zh"===this.lang?"人工智能讲座":"AI Lecture Series",desc:"zh"===this.lang?"了解AI最新发展和应用场景":"Learn about the latest developments in AI and its applications",points:150,image:"https://picsum.photos/400/300?random=2",time:"2023-09-20 19:00-21:00",location:"zh"===this.lang?"线上直播":"Online Live"},{id:"act003",title:"zh"===this.lang?"创业经验分享会":"Entrepreneurship Sharing",desc:"zh"===this.lang?"成功创业者分享经验和教训":"Successful entrepreneurs share their experiences and lessons",points:200,image:"https://picsum.photos/400/300?random=3",time:"2023-09-25 15:00-17:30",location:"zh"===this.lang?"深圳市福田区会展中心":"Futian Convention Center, Shenzhen"}],this.giftList=[{id:"gift001",title:"zh"===this.lang?"定制T恤":"Custom T-shirt",desc:"zh"===this.lang?"舒适透气的纯棉T恤，印有夏令营Logo":"Comfortable cotton T-shirt with Summer Camp Logo",points:100,image:"https://picsum.photos/400/300?random=4",stock:50},{id:"gift002",title:"zh"===this.lang?"蓝牙耳机":"Bluetooth Headphones",desc:"zh"===this.lang?"高音质无线蓝牙耳机，续航时间长":"High-quality wireless headphones with long battery life",points:300,image:"https://picsum.photos/400/300?random=5",stock:20},{id:"gift003",title:"zh"===this.lang?"电影票券":"Movie Tickets",desc:"zh"===this.lang?"全国通用电影票兑换券，可在线选座":"Movie ticket voucher valid nationwide, online seat selection available",points:80,image:"https://picsum.photos/400/300?random=6",stock:100},{id:"gift004",title:"zh"===this.lang?"移动电源":"Power Bank",desc:"zh"===this.lang?"10000mAh大容量，支持快充":"10000mAh capacity, supports fast charging",points:200,image:"https://picsum.photos/400/300?random=7",stock:30}]},viewActivityDetail:function(t){this.currentActivity=t,this.showActivityDetail=!0},closeActivityDetail:function(){this.showActivityDetail=!1},viewGiftDetail:function(t){this.currentGift=t,this.showGiftDetail=!0},closeGiftDetail:function(){this.showGiftDetail=!1},joinActivity:function(e){if(this.points<e.points)return t.showToast({title:"zh"===this.lang?"积分不足":"Insufficient points",icon:"none"});this.currentActivity=e,this.showActivityDetail=!1,this.showActivityJoin=!0},closeActivityJoin:function(){this.showActivityJoin=!1,this.activityForm={name:"",phone:"",remarks:""},this.selectedProfile=null},exchangeGift:function(e){if(this.points<e.points)return t.showToast({title:"zh"===this.lang?"积分不足":"Insufficient points",icon:"none"});this.currentGift=e,this.showGiftDetail=!1,this.showGiftExchange=!0},closeGiftExchange:function(){this.showGiftExchange=!1,this.giftForm={name:"",phone:"",address:"",remarks:""}},submitActivityJoin:function(){var e=this;if(!this.selectedProfile)return t.showToast({title:"zh"===this.lang?"请选择报名资料":"Please select a profile",icon:"none"});t.showLoading({title:"zh"===this.lang?"提交中...":"Submitting..."}),setTimeout((function(){e.points-=e.currentActivity.points;var n=t.getStorageSync("userInfo")||{};n.points=e.points,t.setStorageSync("userInfo",n),t.hideLoading(),t.showToast({title:"zh"===e.lang?"报名成功":"Registration successful",icon:"success"}),e.closeActivityJoin()}),1e3)},submitGiftExchange:function(){var e=this;return this.giftForm.name?this.giftForm.phone?this.giftForm.address?(t.showLoading({title:"zh"===this.lang?"提交中...":"Submitting..."}),void setTimeout((function(){e.points-=e.currentGift.points;var n=t.getStorageSync("userInfo")||{};n.points=e.points,t.setStorageSync("userInfo",n),t.hideLoading(),t.showToast({title:"zh"===e.lang?"兑换成功":"Exchange successful",icon:"success"}),e.closeGiftExchange()}),1e3)):t.showToast({title:"zh"===this.lang?"请输入收货地址":"Please enter delivery address",icon:"none"}):t.showToast({title:"zh"===this.lang?"请输入联系电话":"Please enter contact phone",icon:"none"}):t.showToast({title:"zh"===this.lang?"请输入收件人姓名":"Please enter recipient name",icon:"none"})},selectProfile:function(t){this.selectedProfileIndex=this.profileList.findIndex((function(e){return e.phone===t.phone})),this.selectedProfile=t},goToManageProfiles:function(){this.showProfileSelect=!1,t.navigateTo({url:"/pages/signup/manage"})},initDefaultProfile:function(){var t=this.profileList.find((function(t){return t.isDefault}));t&&(this.selectedProfile=t)},confirmProfileSelection:function(){-1!==this.selectedProfileIndex?this.showProfileSelect=!1:t.showToast({title:"zh"===this.lang?"请选择报名资料":"Please select a profile",icon:"none"})}}};e.default=o}).call(this,n(2)["default"])},249:function(t,e,n){"use strict";n.r(e);var i=n(250),o=n.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);e["default"]=o.a},250:function(t,e,n){}},[[243,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/points/exchange.js.map