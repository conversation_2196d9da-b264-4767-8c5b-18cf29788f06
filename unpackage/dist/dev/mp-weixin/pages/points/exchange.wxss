@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* uni.scss */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.popup-content.data-v-45560ae6 {
  padding: 30rpx;
}
.popup-content .popup-header.data-v-45560ae6 {
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 30rpx;
}
.popup-content .profile-list.data-v-45560ae6 {
  max-height: 600rpx;
}
.popup-content .profile-item.data-v-45560ae6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f2f2f2;
}
.popup-content .profile-item.selected.data-v-45560ae6 {
  background-color: #f5f7fa;
}
.popup-content .profile-item.default.data-v-45560ae6 {
  border: 2rpx solid #2979ff;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}
.popup-content .profile-info.data-v-45560ae6 {
  display: flex;
  flex-direction: column;
}
.popup-content .profile-name.data-v-45560ae6 {
  font-size: 28rpx;
  color: #303133;
  margin-bottom: 8rpx;
}
.popup-content .profile-phone.data-v-45560ae6 {
  font-size: 24rpx;
  color: #909399;
}
.popup-content .popup-buttons.data-v-45560ae6 {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}
.popup-content .cancel-button.data-v-45560ae6 {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #f2f2f2;
  color: #606266;
  border-radius: 8rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
}
.popup-content .confirm-button.data-v-45560ae6 {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #2979ff;
  color: #ffffff;
  border-radius: 8rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
}
.exchange-page.data-v-45560ae6 {
  min-height: 100vh;
  box-sizing: border-box;
  padding-bottom: 100rpx;
  background-color: #f5f7fa;
}
.points-info.data-v-45560ae6 {
  background-color: #4285f4;
  color: #fff;
  padding: 40rpx 30rpx;
  text-align: center;
}
.points-title.data-v-45560ae6 {
  font-size: 30rpx;
  margin-bottom: 16rpx;
}
.points-value.data-v-45560ae6 {
  font-size: 72rpx;
  font-weight: bold;
}
.exchange-tabs.data-v-45560ae6 {
  background-color: #fff;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 10;
}
.exchange-list.data-v-45560ae6 {
  padding: 0;
  background-color: #f5f7fa;
}
.exchange-item.data-v-45560ae6 {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding: 30rpx;
  margin-top: 20rpx;
  border-radius: 0;
}
.item-main.data-v-45560ae6 {
  display: flex;
  margin-bottom: 16rpx;
  position: relative;
}
.item-image.data-v-45560ae6 {
  margin-right: 0;
  border-radius: 8rpx;
  overflow: hidden;
  width: 200rpx;
  height: 160rpx;
  flex-shrink: 0;
}
.item-content.data-v-45560ae6 {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-left: 30rpx;
  position: relative;
}
.item-title.data-v-45560ae6 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.item-desc.data-v-45560ae6 {
  font-size: 26rpx;
  color: #666;
}
.item-footer.data-v-45560ae6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 30rpx;
  border-top: 1px solid #f5f5f5;
  height: 50rpx;
}
.item-points.data-v-45560ae6 {
  font-size: 32rpx;
  color: #ff9900;
  font-weight: bold;
  line-height: 1;
}
.exchange-btn.data-v-45560ae6 {
  background-color: #4285f4;
  color: #fff;
  padding: 15rpx 24rpx;
  border-radius: 6rpx;
  font-size: 26rpx;
  text-align: center;
  width: 160rpx;
  line-height: 1.4;
}
.empty-tip.data-v-45560ae6 {
  padding: 100rpx 0;
}
.detail-popup.data-v-45560ae6 {
  width: 600rpx;
  padding: 30rpx;
  box-sizing: border-box;
}
.detail-header.data-v-45560ae6, .form-header.data-v-45560ae6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.detail-title.data-v-45560ae6, .form-title.data-v-45560ae6 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.detail-content.data-v-45560ae6, .form-content.data-v-45560ae6 {
  margin-bottom: 30rpx;
}
.detail-item.data-v-45560ae6 {
  display: flex;
  margin-top: 20rpx;
}
.detail-item.data-v-45560ae6:first-child {
  margin-top: 0;
}
.detail-label.data-v-45560ae6, .form-label.data-v-45560ae6 {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
}
.detail-value.data-v-45560ae6 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.detail-footer.data-v-45560ae6, .form-footer.data-v-45560ae6 {
  margin-top: 40rpx;
}
.form-popup.data-v-45560ae6 {
  width: 650rpx;
  padding: 30rpx;
  box-sizing: border-box;
}
.form-item.data-v-45560ae6 {
  margin-bottom: 30rpx;
}
.form-tips.data-v-45560ae6 {
  font-size: 24rpx;
  color: #ff9900;
  text-align: center;
  margin-top: 20rpx;
}
.profile-selector.data-v-45560ae6 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1px solid #eee;
}
.profile-selector .placeholder.data-v-45560ae6 {
  color: #999;
  font-size: 28rpx;
}
.profile-selector-new.data-v-45560ae6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}
.profile-selector-left.data-v-45560ae6 {
  display: none;
}
.profile-info-header.data-v-45560ae6 {
  display: none;
}
.profile-info-name.data-v-45560ae6 {
  display: none;
}
.profile-info-tag.data-v-45560ae6 {
  display: none;
}
.profile-card-item.data-v-45560ae6 {
  display: none;
}
.profile-card-text.data-v-45560ae6 {
  display: none;
}
.profile-popup.data-v-45560ae6 {
  width: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  box-sizing: border-box;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
}
.profile-popup-header.data-v-45560ae6 {
  text-align: center;
  padding-bottom: 20rpx;
}
.popup-title.data-v-45560ae6 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.profile-list.data-v-45560ae6 {
  max-height: 60vh;
  overflow-y: auto;
}
.profile-select-item.data-v-45560ae6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #f2f2f2;
}
.profile-select-item.profile-selected.data-v-45560ae6 {
  background-color: #f0f7ff;
  border: 2rpx solid #4285f4;
  border-radius: 12rpx;
  padding: 30rpx 20rpx;
  margin: 10rpx 0;
}
.profile-select-info.data-v-45560ae6 {
  flex: 1;
}
.profile-select-name.data-v-45560ae6 {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.profile-select-phone.data-v-45560ae6 {
  font-size: 28rpx;
  color: #999;
}
.profile-popup-buttons.data-v-45560ae6 {
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0;
}
.profile-cancel-button.data-v-45560ae6, .profile-confirm-button.data-v-45560ae6 {
  width: 320rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 32rpx;
}
.profile-cancel-button.data-v-45560ae6 {
  background-color: #f5f5f5;
  color: #333;
}
.profile-confirm-button.data-v-45560ae6 {
  background-color: #4285f4;
  color: #fff;
}
.manage-profiles-link.data-v-45560ae6 {
  text-align: center;
  font-size: 28rpx;
  color: #2979ff;
  margin-top: 20rpx;
  padding-bottom: 20rpx;
}
.selected-profile-info-new.data-v-45560ae6 {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx 30rpx;
  margin-top: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.profile-info-header.data-v-45560ae6 {
  display: none;
}
.profile-info-name.data-v-45560ae6 {
  display: none;
}
.profile-info-tag.data-v-45560ae6 {
  display: none;
}
.profile-card-item.data-v-45560ae6 {
  display: none;
}
.profile-card-text.data-v-45560ae6 {
  display: none;
}
.form-link.data-v-45560ae6 {
  font-size: 26rpx;
  color: #4285f4;
  text-align: center;
  margin-top: 20rpx;
}
.form-section.data-v-45560ae6 {
  margin-bottom: 20rpx;
}
.form-section-title.data-v-45560ae6 {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.profile-item-new.selected.data-v-45560ae6 {
  background-color: #ecf5ff;
}
.profile-item-new.default.data-v-45560ae6 {
  border-left: 8rpx solid #4285f4;
}
.selected-profile-info-new.data-v-45560ae6 {
  border-left: 8rpx solid #4285f4;
}
.form-tips.data-v-45560ae6 {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fffbe6;
  border-radius: 8rpx;
  margin-top: 20rpx;
}
.form-tips text.data-v-45560ae6 {
  font-size: 28rpx;
  color: #ff9900;
  flex: 1;
}
.profile-item-row.data-v-45560ae6 {
  display: flex;
  padding: 10rpx 0;
}
.profile-item-label.data-v-45560ae6 {
  width: 120rpx;
  font-size: 28rpx;
  color: #666;
}
.profile-item-value.data-v-45560ae6 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
