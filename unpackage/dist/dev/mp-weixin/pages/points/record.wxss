
.points-record-page.data-v-18f9de58 {
  min-height: 100vh;
  box-sizing: border-box;
  padding-bottom: 100rpx;
  background-color: #f5f7fa;
}
.record-tabs.data-v-18f9de58 {
  background-color: #fff;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}
.record-list.data-v-18f9de58 {
  padding: 20rpx;
}
.record-item.data-v-18f9de58 {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.02);
}
.record-main.data-v-18f9de58 {
  flex: 1;
}
.record-title.data-v-18f9de58 {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}
.record-time.data-v-18f9de58 {
  font-size: 24rpx;
  color: #999;
}
.record-points.data-v-18f9de58 {
  font-size: 32rpx;
  font-weight: bold;
  margin: 0 20rpx;
}
.points-add.data-v-18f9de58 {
  color: #19be6b;
}
.points-minus.data-v-18f9de58 {
  color: #ff9900;
}
.empty-tip.data-v-18f9de58 {
  padding: 100rpx 0;
}
.detail-popup.data-v-18f9de58 {
  width: 600rpx;
  padding: 30rpx;
  box-sizing: border-box;
}
.detail-header.data-v-18f9de58 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.detail-title.data-v-18f9de58 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.detail-content.data-v-18f9de58 {
}
.detail-item.data-v-18f9de58 {
  display: flex;
  margin-top: 20rpx;
}
.detail-item.data-v-18f9de58:first-child {
  margin-top: 0;
}
.detail-label.data-v-18f9de58 {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
}
.detail-value.data-v-18f9de58 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.address-value.data-v-18f9de58 {
  word-break: break-all;
}
.detail-footer.data-v-18f9de58 {
  margin-top: 40rpx;
}

